import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';

// Mock data for demonstration
interface Purchase {
  id: string;
  purchaseNumber: string;
  supplierName: string;
  date: string;
  expectedDelivery?: string;
  total: number;
  paidAmount: number;
  status: string;
  paymentMethod: string;
}

const mockPurchases: Purchase[] = [
  {
    id: '1',
    purchaseNumber: 'PO-2024-001',
    supplierName: 'شركة المواد الغذائية',
    date: '2024-01-15',
    expectedDelivery: '2024-01-20',
    total: 25000,
    paidAmount: 25000,
    status: 'مستلمة',
    paymentMethod: 'تحويل بنكي'
  },
  {
    id: '2',
    purchaseNumber: 'PO-2024-002',
    supplierName: 'مؤسسة التجهيزات',
    date: '2024-01-16',
    expectedDelivery: '2024-01-25',
    total: 18500,
    paidAmount: 10000,
    status: 'مطلوبة',
    paymentMethod: 'آجل'
  },
  {
    id: '3',
    purchaseNumber: 'PO-2024-003',
    supplierName: 'شركة المعدات',
    date: '2024-01-17',
    expectedDelivery: '2024-01-30',
    total: 32000,
    paidAmount: 0,
    status: 'جزئية',
    paymentMethod: 'نقداً'
  },
  {
    id: '4',
    purchaseNumber: 'PO-2024-004',
    supplierName: 'مورد المواد الخام',
    date: '2024-01-18',
    total: 15000,
    paidAmount: 15000,
    status: 'مستلمة',
    paymentMethod: 'بطاقة'
  }
];

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ar-SA');
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'مستلمة':
      return '#4caf50';
    case 'مطلوبة':
      return '#2196f3';
    case 'جزئية':
      return '#ff9800';
    case 'ملغية':
      return '#f44336';
    case 'مسودة':
      return '#757575';
    default:
      return '#757575';
  }
};

const PurchasesPage: React.FC = () => {
  const [purchases] = useState<Purchase[]>(mockPurchases);
  const [loading, setLoading] = useState(false);

  // Calculate statistics
  const stats = {
    totalPurchases: purchases.length,
    totalCost: purchases.reduce((sum, purchase) => sum + purchase.total, 0),
    paidAmount: purchases.reduce((sum, purchase) => sum + purchase.paidAmount, 0),
    pendingAmount: purchases.reduce((sum, purchase) => sum + (purchase.total - purchase.paidAmount), 0),
    receivedPurchases: purchases.filter(p => p.status === 'مستلمة').length,
    orderedPurchases: purchases.filter(p => p.status === 'مطلوبة').length,
  };

  // Simulate loading data
  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleAddPurchase = () => {
    alert('سيتم فتح نموذج إضافة أمر شراء جديد');
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 500);
  };

  const handlePrintOrder = (purchaseId: string) => {
    alert(`طباعة أمر الشراء ${purchaseId}`);
  };

  const handleEditPurchase = (purchaseId: string) => {
    alert(`تعديل المشتريات ${purchaseId}`);
  };

  const handleReceiveOrder = (purchaseId: string) => {
    alert(`تأكيد استلام الطلب ${purchaseId}`);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight={700}>
          إدارة المشتريات
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            onClick={handleRefresh}
            disabled={loading}
          >
            تحديث
          </Button>
          <Button
            variant="contained"
            onClick={handleAddPurchase}
          >
            أمر شراء جديد
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            إجمالي المشتريات
          </Typography>
          <Typography variant="h4" component="div" color="primary">
            {stats.totalPurchases}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            إجمالي التكلفة
          </Typography>
          <Typography variant="h4" component="div" color="primary">
            {formatCurrency(stats.totalCost)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            المبلغ المدفوع
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#4caf50' }}>
            {formatCurrency(stats.paidAmount)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            المبلغ المعلق
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#ff9800' }}>
            {formatCurrency(stats.pendingAmount)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            مشتريات مستلمة
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#4caf50' }}>
            {stats.receivedPurchases}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            مشتريات مطلوبة
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#2196f3' }}>
            {stats.orderedPurchases}
          </Typography>
        </Paper>
      </Box>

      {/* Purchases Table */}
      <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
          <Typography variant="h6">قائمة المشتريات</Typography>
        </Box>

        {loading ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography>جاري التحميل...</Typography>
          </Box>
        ) : (
          <Box sx={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f5f5f5' }}>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>رقم الطلب</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المورد</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>تاريخ الطلب</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>تاريخ التسليم</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>الإجمالي</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المدفوع</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المتبقي</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>الحالة</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>طريقة الدفع</th>
                  <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #e0e0e0' }}>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {purchases.map((purchase) => (
                  <tr key={purchase.id} style={{ borderBottom: '1px solid #e0e0e0' }}>
                    <td style={{ padding: '12px' }}>{purchase.purchaseNumber}</td>
                    <td style={{ padding: '12px' }}>{purchase.supplierName}</td>
                    <td style={{ padding: '12px' }}>{formatDate(purchase.date)}</td>
                    <td style={{ padding: '12px' }}>
                      {purchase.expectedDelivery ? formatDate(purchase.expectedDelivery) : '-'}
                    </td>
                    <td style={{ padding: '12px' }}>{formatCurrency(purchase.total)}</td>
                    <td style={{ padding: '12px' }}>{formatCurrency(purchase.paidAmount)}</td>
                    <td style={{ padding: '12px' }}>{formatCurrency(purchase.total - purchase.paidAmount)}</td>
                    <td style={{ padding: '12px' }}>
                      <span
                        style={{
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          color: 'white',
                          backgroundColor: getStatusColor(purchase.status)
                        }}
                      >
                        {purchase.status}
                      </span>
                    </td>
                    <td style={{ padding: '12px' }}>{purchase.paymentMethod}</td>
                    <td style={{ padding: '12px', textAlign: 'center' }}>
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{ mr: 1, minWidth: 'auto', px: 1 }}
                        onClick={() => handleEditPurchase(purchase.id)}
                      >
                        تعديل
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        color="info"
                        sx={{ mr: 1, minWidth: 'auto', px: 1 }}
                        onClick={() => handlePrintOrder(purchase.id)}
                      >
                        طباعة
                      </Button>
                      {purchase.status === 'مطلوبة' && (
                        <Button
                          size="small"
                          variant="outlined"
                          color="success"
                          sx={{ minWidth: 'auto', px: 1 }}
                          onClick={() => handleReceiveOrder(purchase.id)}
                        >
                          استلام
                        </Button>
                      )}
                      {purchase.status !== 'مطلوبة' && (
                        <Button
                          size="small"
                          variant="outlined"
                          color="secondary"
                          sx={{ minWidth: 'auto', px: 1 }}
                          onClick={() => alert(`عرض تفاصيل المشتريات ${purchase.id}`)}
                        >
                          عرض
                        </Button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Box>
        )}

        {!loading && purchases.length === 0 && (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography color="textSecondary">
              لا توجد مشتريات متاحة
            </Typography>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default PurchasesPage;