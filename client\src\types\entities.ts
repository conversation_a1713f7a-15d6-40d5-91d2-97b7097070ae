export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  creditLimit: number;
  currentBalance: number;
  paymentTerms: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface Supplier {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  creditLimit: number;
  currentBalance: number;
  paymentTerms: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  category: string;
  unit: string;
  costPrice: number;
  sellingPrice: number;
  stock: number;
  minStock: number;
  maxStock: number;
  status: 'active' | 'inactive';
  images?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: UserRole;
  permissions: Permission[];
  status: 'active' | 'inactive';
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 'admin' | 'manager' | 'sales' | 'accountant' | 'viewer';

export type Permission = 
  | 'sales.view' | 'sales.create' | 'sales.edit' | 'sales.delete'
  | 'purchases.view' | 'purchases.create' | 'purchases.edit' | 'purchases.delete'
  | 'customers.view' | 'customers.create' | 'customers.edit' | 'customers.delete'
  | 'suppliers.view' | 'suppliers.create' | 'suppliers.edit' | 'suppliers.delete'
  | 'products.view' | 'products.create' | 'products.edit' | 'products.delete'
  | 'inventory.view' | 'inventory.edit'
  | 'reports.view' | 'reports.export'
  | 'settings.view' | 'settings.edit'
  | 'users.view' | 'users.create' | 'users.edit' | 'users.delete';

export const USER_ROLES: { value: UserRole; label: string }[] = [
  { value: 'admin', label: 'مدير النظام' },
  { value: 'manager', label: 'مدير' },
  { value: 'sales', label: 'مندوب مبيعات' },
  { value: 'accountant', label: 'محاسب' },
  { value: 'viewer', label: 'مشاهد' },
];

export const PRODUCT_UNITS = [
  { value: 'piece', label: 'قطعة' },
  { value: 'kg', label: 'كيلوجرام' },
  { value: 'gram', label: 'جرام' },
  { value: 'liter', label: 'لتر' },
  { value: 'meter', label: 'متر' },
  { value: 'box', label: 'صندوق' },
  { value: 'pack', label: 'عبوة' },
  { value: 'dozen', label: 'دزينة' },
];

export const COUNTRIES = [
  { code: 'SA', name: 'المملكة العربية السعودية' },
  { code: 'AE', name: 'الإمارات العربية المتحدة' },
  { code: 'KW', name: 'الكويت' },
  { code: 'QA', name: 'قطر' },
  { code: 'BH', name: 'البحرين' },
  { code: 'OM', name: 'عمان' },
  { code: 'JO', name: 'الأردن' },
  { code: 'LB', name: 'لبنان' },
  { code: 'EG', name: 'مصر' },
];
