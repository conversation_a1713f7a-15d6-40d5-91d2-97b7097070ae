{"ast": null, "code": "'use client';\n\nexport { default } from './Popover';\nexport * from './Popover';\nexport { default as popoverClasses } from './popoverClasses';\nexport * from './popoverClasses';", "map": {"version": 3, "names": ["default", "popoverClasses"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/Popover/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Popover';\nexport * from './Popover';\nexport { default as popoverClasses } from './popoverClasses';\nexport * from './popoverClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,WAAW;AACnC,cAAc,WAAW;AACzB,SAASA,OAAO,IAAIC,cAAc,QAAQ,kBAAkB;AAC5D,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}