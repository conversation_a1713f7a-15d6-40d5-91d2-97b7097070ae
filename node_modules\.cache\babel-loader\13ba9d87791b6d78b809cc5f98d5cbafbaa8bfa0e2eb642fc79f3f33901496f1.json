{"ast": null, "code": "// copied from @mui/system/createStyled\nfunction slotShouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nexport default slotShouldForwardProp;", "map": {"version": 3, "names": ["slotShouldForwardProp", "prop"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/styles/slotShouldForwardProp.js"], "sourcesContent": ["// copied from @mui/system/createStyled\nfunction slotShouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nexport default slotShouldForwardProp;"], "mappings": "AAAA;AACA,SAASA,qBAAqBA,CAACC,IAAI,EAAE;EACnC,OAAOA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI;AACpF;AACA,eAAeD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}