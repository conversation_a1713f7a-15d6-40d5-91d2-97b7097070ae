import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider,
  Card,
  CardContent
} from '@mui/material';

interface InvoiceItem {
  id: string;
  productName: string;
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate: number;
  subtotal: number;
  taxAmount: number;
  total: number;
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  date: string;
  dueDate: string;
  customerName: string;
  customerTaxId: string;
  customerAddress: string;
  items: InvoiceItem[];
  subtotal: number;
  totalTax: number;
  totalAmount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  notes: string;
}

interface InvoicePrintTemplateProps {
  invoice: Invoice;
  companyInfo: {
    name: string;
    taxId: string;
    address: string;
    phone: string;
    email: string;
  };
}

const InvoicePrintTemplate = React.forwardRef<HTMLDivElement, InvoicePrintTemplateProps>(
  ({ invoice, companyInfo }, ref) => {
    return (
      <Box 
        ref={ref} 
        sx={{ 
          p: 4, 
          backgroundColor: 'white',
          minHeight: '297mm', // A4 height
          width: '210mm', // A4 width
          margin: '0 auto',
          '@media print': {
            margin: 0,
            padding: '20mm',
            boxShadow: 'none'
          }
        }}
      >
        {/* Header */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Typography variant="h4" fontWeight={700} color="primary" sx={{ mb: 2 }}>
              {companyInfo.name}
            </Typography>
            <Typography variant="body2" sx={{ lineHeight: 1.8 }}>
              {companyInfo.address}<br />
              هاتف: {companyInfo.phone}<br />
              البريد الإلكتروني: {companyInfo.email}<br />
              <strong>الرقم الضريبي: {companyInfo.taxId}</strong>
            </Typography>
          </Grid>
          <Grid item xs={12} md={6} sx={{ textAlign: 'right' }}>
            <Typography variant="h3" fontWeight={700} color="primary" sx={{ mb: 2 }}>
              فاتورة ضريبية
            </Typography>
            <Typography variant="h6" sx={{ mb: 1 }}>
              رقم الفاتورة: <strong>{invoice.invoiceNumber}</strong>
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              التاريخ: {new Date(invoice.date).toLocaleDateString('ar-SA')}
            </Typography>
            <Typography variant="body1">
              تاريخ الاستحقاق: {new Date(invoice.dueDate).toLocaleDateString('ar-SA')}
            </Typography>
          </Grid>
        </Grid>

        <Divider sx={{ my: 3 }} />

        {/* Customer Info */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
              بيانات العميل:
            </Typography>
            <Typography variant="body1" sx={{ lineHeight: 1.8 }}>
              <strong>{invoice.customerName}</strong><br />
              {invoice.customerAddress}<br />
              <strong>الرقم الضريبي: {invoice.customerTaxId}</strong>
            </Typography>
          </Grid>
        </Grid>

        <Divider sx={{ my: 3 }} />

        {/* Items Table */}
        <TableContainer sx={{ mb: 4 }}>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                <TableCell sx={{ fontWeight: 'bold', border: '1px solid #ddd' }}>الصنف</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold', border: '1px solid #ddd' }}>الكمية</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold', border: '1px solid #ddd' }}>سعر الوحدة</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold', border: '1px solid #ddd' }}>المجموع الفرعي</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold', border: '1px solid #ddd' }}>ضريبة القيمة المضافة (15%)</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold', border: '1px solid #ddd' }}>الإجمالي</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {invoice.items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell sx={{ border: '1px solid #ddd' }}>
                    <Typography variant="body2" fontWeight={600}>
                      {item.productName}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {item.description}
                    </Typography>
                  </TableCell>
                  <TableCell align="center" sx={{ border: '1px solid #ddd' }}>{item.quantity}</TableCell>
                  <TableCell align="center" sx={{ border: '1px solid #ddd' }}>{item.unitPrice.toLocaleString('ar-SA')} ر.س</TableCell>
                  <TableCell align="center" sx={{ border: '1px solid #ddd' }}>{item.subtotal.toLocaleString('ar-SA')} ر.س</TableCell>
                  <TableCell align="center" sx={{ border: '1px solid #ddd' }}>{item.taxAmount.toLocaleString('ar-SA')} ر.س</TableCell>
                  <TableCell align="center" sx={{ border: '1px solid #ddd' }}>
                    <Typography fontWeight={600}>
                      {item.total.toLocaleString('ar-SA')} ر.س
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Totals */}
        <Grid container justifyContent="flex-end" sx={{ mb: 4 }}>
          <Grid item xs={12} md={4}>
            <Card sx={{ backgroundColor: '#f8f9fa', border: '1px solid #ddd' }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>المجموع الفرعي:</Typography>
                  <Typography fontWeight={600}>
                    {invoice.subtotal.toLocaleString('ar-SA')} ر.س
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>ضريبة القيمة المضافة (15%):</Typography>
                  <Typography fontWeight={600}>
                    {invoice.totalTax.toLocaleString('ar-SA')} ر.س
                  </Typography>
                </Box>
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="h6" fontWeight={700}>
                    الإجمالي:
                  </Typography>
                  <Typography variant="h6" fontWeight={700} color="primary">
                    {invoice.totalAmount.toLocaleString('ar-SA')} ر.س
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Notes */}
        {invoice.notes && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 1 }}>
              ملاحظات:
            </Typography>
            <Typography variant="body2">
              {invoice.notes}
            </Typography>
          </Box>
        )}

        {/* Footer */}
        <Box sx={{ mt: 4, pt: 3, borderTop: '2px solid #e0e0e0', textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            هذه فاتورة ضريبية صادرة إلكترونياً ومتوافقة مع لوائح هيئة الزكاة والضريبة والجمارك
          </Typography>
          <Typography variant="caption" color="text.secondary">
            تم إنشاء هذه الفاتورة بواسطة نظام زين كود ERP - {new Date().toLocaleDateString('ar-SA')}
          </Typography>
        </Box>
      </Box>
    );
  }
);

InvoicePrintTemplate.displayName = 'InvoicePrintTemplate';

export default InvoicePrintTemplate;
