{"ast": null, "code": "/**\n * @mui/private-theming v5.17.1\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nexport { default as ThemeProvider } from './ThemeProvider';\nexport * from './ThemeProvider';\nexport { default as useTheme } from './useTheme';", "map": {"version": 3, "names": ["default", "ThemeProvider", "useTheme"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/private-theming/index.js"], "sourcesContent": ["/**\n * @mui/private-theming v5.17.1\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nexport { default as ThemeProvider } from './ThemeProvider';\nexport * from './ThemeProvider';\nexport { default as useTheme } from './useTheme';"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,cAAc,iBAAiB;AAC/B,SAASD,OAAO,IAAIE,QAAQ,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}