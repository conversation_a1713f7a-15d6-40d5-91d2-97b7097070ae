{"ast": null, "code": "'use client';\n\nexport { default } from './TableSortLabel';\nexport { default as tableSortLabelClasses } from './tableSortLabelClasses';\nexport * from './tableSortLabelClasses';", "map": {"version": 3, "names": ["default", "tableSortLabelClasses"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/TableSortLabel/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TableSortLabel';\nexport { default as tableSortLabelClasses } from './tableSortLabelClasses';\nexport * from './tableSortLabelClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,yBAAyB;AAC1E,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}