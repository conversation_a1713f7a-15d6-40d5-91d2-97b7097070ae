import React from 'react';
import { Box, Typography, Paper } from '@mui/material';

const SalesPage: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" fontWeight={700} sx={{ mb: 3 }}>
        إدارة المبيعات
      </Typography>

      <Paper sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          إحصائيات المبيعات
        </Typography>

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
          <Box sx={{ p: 2, backgroundColor: '#e3f2fd', borderRadius: 1 }}>
            <Typography variant="h6" color="primary">إجمالي المبيعات</Typography>
            <Typography variant="h4">4</Typography>
          </Box>
          <Box sx={{ p: 2, backgroundColor: '#e8f5e8', borderRadius: 1 }}>
            <Typography variant="h6" color="success.main">إجمالي الإيرادات</Typography>
            <Typography variant="h4">58,000 ر.س</Typography>
          </Box>
          <Box sx={{ p: 2, backgroundColor: '#fff3e0', borderRadius: 1 }}>
            <Typography variant="h6" color="warning.main">المبالغ المدفوعة</Typography>
            <Typography variant="h4">32,500 ر.س</Typography>
          </Box>
          <Box sx={{ p: 2, backgroundColor: '#ffebee', borderRadius: 1 }}>
            <Typography variant="h6" color="error.main">المبالغ المتبقية</Typography>
            <Typography variant="h4">25,500 ر.س</Typography>
          </Box>
        </Box>

        <Typography variant="h6" sx={{ mb: 2 }}>
          قائمة المبيعات
        </Typography>

        <Box sx={{ overflowX: 'auto' }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f5f5f5' }}>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>رقم الفاتورة</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>العميل</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>التاريخ</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المبلغ</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المدفوع</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>الحالة</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>طريقة الدفع</th>
              </tr>
            </thead>
            <tbody>
              <tr style={{ borderBottom: '1px solid #f0f0f0' }}>
                <td style={{ padding: '12px' }}>INV-2024-001</td>
                <td style={{ padding: '12px' }}>أحمد محمد</td>
                <td style={{ padding: '12px' }}>15/01/2024</td>
                <td style={{ padding: '12px' }}>15,000 ر.س</td>
                <td style={{ padding: '12px' }}>15,000 ر.س</td>
                <td style={{ padding: '12px' }}>
                  <span style={{ padding: '4px 8px', borderRadius: '4px', backgroundColor: '#4caf50', color: 'white', fontSize: '12px' }}>
                    مكتملة
                  </span>
                </td>
                <td style={{ padding: '12px' }}>نقدي</td>
              </tr>
              <tr style={{ borderBottom: '1px solid #f0f0f0' }}>
                <td style={{ padding: '12px' }}>INV-2024-002</td>
                <td style={{ padding: '12px' }}>فاطمة علي</td>
                <td style={{ padding: '12px' }}>16/01/2024</td>
                <td style={{ padding: '12px' }}>8,500 ر.س</td>
                <td style={{ padding: '12px' }}>5,000 ر.س</td>
                <td style={{ padding: '12px' }}>
                  <span style={{ padding: '4px 8px', borderRadius: '4px', backgroundColor: '#ff9800', color: 'white', fontSize: '12px' }}>
                    جزئية
                  </span>
                </td>
                <td style={{ padding: '12px' }}>بطاقة</td>
              </tr>
              <tr style={{ borderBottom: '1px solid #f0f0f0' }}>
                <td style={{ padding: '12px' }}>INV-2024-003</td>
                <td style={{ padding: '12px' }}>محمد سالم</td>
                <td style={{ padding: '12px' }}>17/01/2024</td>
                <td style={{ padding: '12px' }}>22,000 ر.س</td>
                <td style={{ padding: '12px' }}>0 ر.س</td>
                <td style={{ padding: '12px' }}>
                  <span style={{ padding: '4px 8px', borderRadius: '4px', backgroundColor: '#2196f3', color: 'white', fontSize: '12px' }}>
                    في الانتظار
                  </span>
                </td>
                <td style={{ padding: '12px' }}>آجل</td>
              </tr>
              <tr style={{ borderBottom: '1px solid #f0f0f0' }}>
                <td style={{ padding: '12px' }}>INV-2024-004</td>
                <td style={{ padding: '12px' }}>سارة أحمد</td>
                <td style={{ padding: '12px' }}>18/01/2024</td>
                <td style={{ padding: '12px' }}>12,500 ر.س</td>
                <td style={{ padding: '12px' }}>12,500 ر.س</td>
                <td style={{ padding: '12px' }}>
                  <span style={{ padding: '4px 8px', borderRadius: '4px', backgroundColor: '#4caf50', color: 'white', fontSize: '12px' }}>
                    مكتملة
                  </span>
                </td>
                <td style={{ padding: '12px' }}>تحويل بنكي</td>
              </tr>
            </tbody>
          </table>
        </Box>
      </Paper>
    </Box>
  );
};

export default SalesPage;