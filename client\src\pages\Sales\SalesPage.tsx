import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Alert,
  Snackbar,
  CircularProgress,
  InputAdornment
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Print as PrintIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Receipt as ReceiptIcon,
  PendingActions as PendingIcon,
  CheckCircle as CompletedIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon
} from '@mui/icons-material';

// Mock data for demonstration
interface Sale {
  id: string;
  invoiceNumber: string;
  customerName: string;
  date: string;
  total: number;
  paidAmount: number;
  status: string;
  paymentMethod: string;
}

const mockSales: Sale[] = [
  {
    id: '1',
    invoiceNumber: 'INV-2024-001',
    customerName: 'أحمد محمد',
    date: '2024-01-15',
    total: 15000,
    paidAmount: 15000,
    status: 'مكتملة',
    paymentMethod: 'نقداً'
  },
  {
    id: '2',
    invoiceNumber: 'INV-2024-002',
    customerName: 'فاطمة علي',
    date: '2024-01-16',
    total: 8500,
    paidAmount: 5000,
    status: 'جزئية',
    paymentMethod: 'بطاقة'
  },
  {
    id: '3',
    invoiceNumber: 'INV-2024-003',
    customerName: 'محمد سالم',
    date: '2024-01-17',
    total: 22000,
    paidAmount: 0,
    status: 'في الانتظار',
    paymentMethod: 'آجل'
  },
  {
    id: '4',
    invoiceNumber: 'INV-2024-004',
    customerName: 'سارة أحمد',
    date: '2024-01-18',
    total: 12500,
    paidAmount: 12500,
    status: 'مكتملة',
    paymentMethod: 'تحويل بنكي'
  }
];

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ar-SA');
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'مكتملة':
      return '#4caf50';
    case 'جزئية':
      return '#ff9800';
    case 'في الانتظار':
      return '#2196f3';
    case 'ملغية':
      return '#f44336';
    default:
      return '#757575';
  }
};

const SalesPage: React.FC = () => {
  const [sales, setSales] = useState<Sale[]>(mockSales);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [paymentFilter, setPaymentFilter] = useState('all');
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'warning' | 'info'>('success');

  // Filter sales based on search and filters
  const filteredSales = sales.filter(sale => {
    const matchesSearch = sale.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.customerName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || sale.status === statusFilter;
    const matchesPayment = paymentFilter === 'all' || sale.paymentMethod === paymentFilter;

    return matchesSearch && matchesStatus && matchesPayment;
  });

  // Calculate statistics
  const stats = {
    totalSales: filteredSales.length,
    totalRevenue: filteredSales.reduce((sum, sale) => sum + sale.total, 0),
    paidAmount: filteredSales.reduce((sum, sale) => sum + sale.paidAmount, 0),
    pendingAmount: filteredSales.reduce((sum, sale) => sum + (sale.total - sale.paidAmount), 0),
    completedSales: filteredSales.filter(s => s.status === 'مكتملة').length,
    pendingSales: filteredSales.filter(s => s.status === 'في الانتظار').length,
  };

  // Simulate loading data
  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleAddSale = () => {
    setSnackbarMessage('سيتم فتح نموذج إضافة فاتورة مبيعات جديدة');
    setSnackbarSeverity('info');
    setSnackbarOpen(true);
  };

  const handleRefresh = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setSnackbarMessage('تم تحديث بيانات المبيعات بنجاح');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    }, 1000);
  };

  const handlePrintInvoice = (saleId: string) => {
    const sale = sales.find(s => s.id === saleId);
    if (sale) {
      setSnackbarMessage(`جاري طباعة الفاتورة ${sale.invoiceNumber}`);
      setSnackbarSeverity('info');
      setSnackbarOpen(true);
      // Here you would implement actual printing logic
    }
  };

  const handleEditSale = (saleId: string) => {
    const sale = sales.find(s => s.id === saleId);
    if (sale) {
      setSelectedSale(sale);
      setSnackbarMessage(`فتح نموذج تعديل الفاتورة ${sale.invoiceNumber}`);
      setSnackbarSeverity('info');
      setSnackbarOpen(true);
    }
  };

  const handleViewSale = (sale: Sale) => {
    setSelectedSale(sale);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedSale(null);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight={700}>
          إدارة المبيعات
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            onClick={handleRefresh}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
          >
            {loading ? 'جاري التحديث...' : 'تحديث'}
          </Button>
          <Button
            variant="contained"
            onClick={handleAddSale}
            startIcon={<AddIcon />}
          >
            فاتورة جديدة
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            إجمالي المبيعات
          </Typography>
          <Typography variant="h4" component="div" color="primary">
            {stats.totalSales}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            إجمالي الإيرادات
          </Typography>
          <Typography variant="h4" component="div" color="primary">
            {formatCurrency(stats.totalRevenue)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            المبلغ المدفوع
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#4caf50' }}>
            {formatCurrency(stats.paidAmount)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            المبلغ المعلق
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#ff9800' }}>
            {formatCurrency(stats.pendingAmount)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            مبيعات مكتملة
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#4caf50' }}>
            {stats.completedSales}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            مبيعات معلقة
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#2196f3' }}>
            {stats.pendingSales}
          </Typography>
        </Paper>
      </Box>

      {/* Search and Filters */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="البحث برقم الفاتورة أو اسم العميل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField
              select
              fullWidth
              label="حالة الفاتورة"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <MenuItem value="all">جميع الحالات</MenuItem>
              <MenuItem value="مكتملة">مكتملة</MenuItem>
              <MenuItem value="في الانتظار">في الانتظار</MenuItem>
              <MenuItem value="جزئية">جزئية</MenuItem>
              <MenuItem value="ملغية">ملغية</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField
              select
              fullWidth
              label="طريقة الدفع"
              value={paymentFilter}
              onChange={(e) => setPaymentFilter(e.target.value)}
            >
              <MenuItem value="all">جميع الطرق</MenuItem>
              <MenuItem value="نقدي">نقدي</MenuItem>
              <MenuItem value="بطاقة ائتمان">بطاقة ائتمان</MenuItem>
              <MenuItem value="تحويل بنكي">تحويل بنكي</MenuItem>
              <MenuItem value="شيك">شيك</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterIcon />}
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setPaymentFilter('all');
              }}
            >
              مسح الفلاتر
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Sales Table */}
      <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
          <Typography variant="h6">قائمة المبيعات</Typography>
        </Box>

        {loading ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <CircularProgress />
            <Typography sx={{ mt: 2 }}>جاري تحميل البيانات...</Typography>
          </Box>
        ) : filteredSales.length === 0 ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="h6" color="textSecondary">
              لا توجد فواتير مبيعات مطابقة
            </Typography>
            <Typography color="textSecondary" sx={{ mt: 1 }}>
              جرب تغيير معايير البحث أو مسح الفلاتر
            </Typography>
          </Box>
        ) : (
          <Box sx={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f5f5f5' }}>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>رقم الفاتورة</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>العميل</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>التاريخ</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>الإجمالي</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المدفوع</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المتبقي</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>الحالة</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>طريقة الدفع</th>
                  <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #e0e0e0' }}>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredSales.map((sale) => (
                  <tr key={sale.id} style={{ borderBottom: '1px solid #e0e0e0' }}>
                    <td style={{ padding: '12px' }}>{sale.invoiceNumber}</td>
                    <td style={{ padding: '12px' }}>{sale.customerName}</td>
                    <td style={{ padding: '12px' }}>{formatDate(sale.date)}</td>
                    <td style={{ padding: '12px' }}>{formatCurrency(sale.total)}</td>
                    <td style={{ padding: '12px' }}>{formatCurrency(sale.paidAmount)}</td>
                    <td style={{ padding: '12px' }}>{formatCurrency(sale.total - sale.paidAmount)}</td>
                    <td style={{ padding: '12px' }}>
                      <span
                        style={{
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          color: 'white',
                          backgroundColor: getStatusColor(sale.status)
                        }}
                      >
                        {sale.status}
                      </span>
                    </td>
                    <td style={{ padding: '12px' }}>{sale.paymentMethod}</td>
                    <td style={{ padding: '12px', textAlign: 'center' }}>
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{ mr: 1, minWidth: 'auto', px: 1 }}
                        onClick={() => handleEditSale(sale.id)}
                      >
                        تعديل
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        color="info"
                        sx={{ mr: 1, minWidth: 'auto', px: 1 }}
                        onClick={() => handlePrintInvoice(sale.id)}
                      >
                        طباعة
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        color="success"
                        sx={{ minWidth: 'auto', px: 1 }}
                        onClick={() => handleViewSale(sale)}
                      >
                        عرض
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* Results Summary */}
            <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0', backgroundColor: '#f9f9f9' }}>
              <Typography variant="body2" color="textSecondary">
                عرض {filteredSales.length} من {sales.length} فاتورة مبيعات
              </Typography>
            </Box>
          </Box>
        )}

        {!loading && sales.length === 0 && (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography color="textSecondary">
              لا توجد مبيعات متاحة
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Sale Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          تفاصيل الفاتورة {selectedSale?.invoiceNumber}
        </DialogTitle>
        <DialogContent>
          {selectedSale && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">العميل</Typography>
                  <Typography variant="body1">{selectedSale.customerName}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">التاريخ</Typography>
                  <Typography variant="body1">{formatDate(selectedSale.date)}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">إجمالي المبلغ</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    {formatCurrency(selectedSale.total)}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">المبلغ المدفوع</Typography>
                  <Typography variant="body1" sx={{ color: '#4caf50', fontWeight: 'bold' }}>
                    {formatCurrency(selectedSale.paidAmount)}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">المبلغ المتبقي</Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: selectedSale.total - selectedSale.paidAmount > 0 ? '#f44336' : '#4caf50',
                      fontWeight: 'bold'
                    }}
                  >
                    {formatCurrency(selectedSale.total - selectedSale.paidAmount)}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="textSecondary">طريقة الدفع</Typography>
                  <Typography variant="body1">{selectedSale.paymentMethod}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">الحالة</Typography>
                  <Chip
                    label={selectedSale.status}
                    size="small"
                    sx={{
                      backgroundColor: getStatusColor(selectedSale.status),
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>إغلاق</Button>
          {selectedSale && (
            <>
              <Button
                onClick={() => handleEditSale(selectedSale.id)}
                variant="outlined"
                startIcon={<EditIcon />}
              >
                تعديل
              </Button>
              <Button
                onClick={() => handlePrintInvoice(selectedSale.id)}
                variant="contained"
                startIcon={<PrintIcon />}
              >
                طباعة
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SalesPage;