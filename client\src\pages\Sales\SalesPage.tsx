import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';

// Mock data for demonstration
interface Sale {
  id: string;
  invoiceNumber: string;
  customerName: string;
  date: string;
  total: number;
  paidAmount: number;
  status: string;
  paymentMethod: string;
}

const mockSales: Sale[] = [
  {
    id: '1',
    invoiceNumber: 'INV-2024-001',
    customerName: 'أحمد محمد',
    date: '2024-01-15',
    total: 15000,
    paidAmount: 15000,
    status: 'مكتملة',
    paymentMethod: 'نقداً'
  },
  {
    id: '2',
    invoiceNumber: 'INV-2024-002',
    customerName: 'فاطمة علي',
    date: '2024-01-16',
    total: 8500,
    paidAmount: 5000,
    status: 'جزئية',
    paymentMethod: 'بطاقة'
  },
  {
    id: '3',
    invoiceNumber: 'INV-2024-003',
    customerName: 'محمد سالم',
    date: '2024-01-17',
    total: 22000,
    paidAmount: 0,
    status: 'في الانتظار',
    paymentMethod: 'آجل'
  },
  {
    id: '4',
    invoiceNumber: 'INV-2024-004',
    customerName: 'سارة أحمد',
    date: '2024-01-18',
    total: 12500,
    paidAmount: 12500,
    status: 'مكتملة',
    paymentMethod: 'تحويل بنكي'
  }
];

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ar-SA');
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'مكتملة':
      return '#4caf50';
    case 'جزئية':
      return '#ff9800';
    case 'في الانتظار':
      return '#2196f3';
    case 'ملغية':
      return '#f44336';
    default:
      return '#757575';
  }
};

const SalesPage: React.FC = () => {
  const [sales] = useState<Sale[]>(mockSales);
  const [loading, setLoading] = useState(false);

  // Calculate statistics
  const stats = {
    totalSales: sales.length,
    totalRevenue: sales.reduce((sum, sale) => sum + sale.total, 0),
    paidAmount: sales.reduce((sum, sale) => sum + sale.paidAmount, 0),
    pendingAmount: sales.reduce((sum, sale) => sum + (sale.total - sale.paidAmount), 0),
    completedSales: sales.filter(s => s.status === 'مكتملة').length,
    pendingSales: sales.filter(s => s.status === 'في الانتظار').length,
  };

  // Simulate loading data
  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleAddSale = () => {
    alert('سيتم فتح نموذج إضافة فاتورة مبيعات جديدة');
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 500);
  };

  const handlePrintInvoice = (saleId: string) => {
    alert(`طباعة الفاتورة ${saleId}`);
  };

  const handleEditSale = (saleId: string) => {
    alert(`تعديل المبيعة ${saleId}`);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight={700}>
          إدارة المبيعات
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            onClick={handleRefresh}
            disabled={loading}
          >
            تحديث
          </Button>
          <Button
            variant="contained"
            onClick={handleAddSale}
          >
            فاتورة جديدة
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2, mb: 3 }}>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            إجمالي المبيعات
          </Typography>
          <Typography variant="h4" component="div" color="primary">
            {stats.totalSales}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            إجمالي الإيرادات
          </Typography>
          <Typography variant="h4" component="div" color="primary">
            {formatCurrency(stats.totalRevenue)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            المبلغ المدفوع
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#4caf50' }}>
            {formatCurrency(stats.paidAmount)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            المبلغ المعلق
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#ff9800' }}>
            {formatCurrency(stats.pendingAmount)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            مبيعات مكتملة
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#4caf50' }}>
            {stats.completedSales}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            مبيعات معلقة
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#2196f3' }}>
            {stats.pendingSales}
          </Typography>
        </Paper>
      </Box>

      {/* Sales Table */}
      <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
          <Typography variant="h6">قائمة المبيعات</Typography>
        </Box>

        {loading ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography>جاري التحميل...</Typography>
          </Box>
        ) : (
          <Box sx={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f5f5f5' }}>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>رقم الفاتورة</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>العميل</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>التاريخ</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>الإجمالي</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المدفوع</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المتبقي</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>الحالة</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>طريقة الدفع</th>
                  <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #e0e0e0' }}>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {sales.map((sale) => (
                  <tr key={sale.id} style={{ borderBottom: '1px solid #e0e0e0' }}>
                    <td style={{ padding: '12px' }}>{sale.invoiceNumber}</td>
                    <td style={{ padding: '12px' }}>{sale.customerName}</td>
                    <td style={{ padding: '12px' }}>{formatDate(sale.date)}</td>
                    <td style={{ padding: '12px' }}>{formatCurrency(sale.total)}</td>
                    <td style={{ padding: '12px' }}>{formatCurrency(sale.paidAmount)}</td>
                    <td style={{ padding: '12px' }}>{formatCurrency(sale.total - sale.paidAmount)}</td>
                    <td style={{ padding: '12px' }}>
                      <span
                        style={{
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          color: 'white',
                          backgroundColor: getStatusColor(sale.status)
                        }}
                      >
                        {sale.status}
                      </span>
                    </td>
                    <td style={{ padding: '12px' }}>{sale.paymentMethod}</td>
                    <td style={{ padding: '12px', textAlign: 'center' }}>
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{ mr: 1, minWidth: 'auto', px: 1 }}
                        onClick={() => handleEditSale(sale.id)}
                      >
                        تعديل
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        color="info"
                        sx={{ mr: 1, minWidth: 'auto', px: 1 }}
                        onClick={() => handlePrintInvoice(sale.id)}
                      >
                        طباعة
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        color="success"
                        sx={{ minWidth: 'auto', px: 1 }}
                        onClick={() => alert(`عرض تفاصيل المبيعة ${sale.id}`)}
                      >
                        عرض
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Box>
        )}

        {!loading && sales.length === 0 && (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography color="textSecondary">
              لا توجد مبيعات متاحة
            </Typography>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default SalesPage;