{"ast": null, "code": "'use client';\n\nexport { default } from './Popper';\nexport * from './popperClasses';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/Popper/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Popper';\nexport * from './popperClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,UAAU;AAClC,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}