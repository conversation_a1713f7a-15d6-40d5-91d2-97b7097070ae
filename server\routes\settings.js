const express = require('express');
const router = express.Router();

// Mock settings data
let settings = {
  company: {
    name: 'شركة زين كود',
    nameEn: 'ZenCode Company',
    logo: null,
    address: 'الرياض، المملكة العربية السعودية',
    city: 'الرياض',
    country: 'SA',
    postalCode: '12345',
    phone: '+966501234567',
    mobile: '+966507654321',
    email: '<EMAIL>',
    website: 'https://zencode.com',
    taxNumber: '***************',
    commercialRegister: '**********',
    description: 'شركة متخصصة في تطوير البرمجيات والحلول التقنية'
  },
  system: {
    currency: 'SAR',
    language: 'ar',
    timezone: 'Asia/Riyadh',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24',
    fiscalYearStart: '01/01',
    backupFrequency: 'daily',
    autoBackup: true,
    emailNotifications: true,
    smsNotifications: false
  },
  accounting: {
    taxRate: 15,
    enableInventoryTracking: true,
    enableMultiCurrency: false,
    defaultPaymentTerms: 30,
    lowStockThreshold: 10,
    autoGenerateInvoiceNumbers: true,
    invoicePrefix: 'INV',
    purchasePrefix: 'PO',
    enableDiscounts: true,
    maxDiscountPercentage: 50,
    requireApprovalForLargeTransactions: true,
    largeTransactionThreshold: 100000
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    lowStockAlerts: true,
    overdueInvoiceAlerts: true
  }
};

// GET /api/settings - Get all settings
router.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Settings retrieved successfully',
      data: settings
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving settings',
      data: null
    });
  }
});

// PUT /api/settings/:section - Update settings section
router.put('/:section', (req, res) => {
  try {
    const { section } = req.params;
    const updateData = req.body;

    // Validate section exists
    if (!settings[section]) {
      return res.status(404).json({
        success: false,
        message: 'Settings section not found',
        data: null
      });
    }

    // Update the section
    settings[section] = {
      ...settings[section],
      ...updateData
    };

    res.json({
      success: true,
      message: `${section} settings updated successfully`,
      data: settings
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating settings',
      data: null
    });
  }
});

// POST /api/settings/test-email - Test email configuration
router.post('/test-email', (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email address is required',
        data: null
      });
    }

    // Simulate email test
    setTimeout(() => {
      res.json({
        success: true,
        message: `Test email sent successfully to ${email}`,
        data: null
      });
    }, 1000);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error sending test email',
      data: null
    });
  }
});

// GET /api/settings/system-info - Get system information
router.get('/system-info', (req, res) => {
  try {
    const systemInfo = {
      version: '1.0.0',
      database: 'Connected',
      storage: '85% used (2.1 GB / 2.5 GB)',
      uptime: '5 days, 12 hours',
      lastBackup: '2024-01-18 02:00:00'
    };

    res.json({
      success: true,
      message: 'System information retrieved successfully',
      data: systemInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving system information',
      data: null
    });
  }
});

module.exports = router;