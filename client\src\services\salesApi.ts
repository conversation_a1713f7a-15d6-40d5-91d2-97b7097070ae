import axios from 'axios';
import { Sale, CreateSaleRequest, SalesFilters, SalesStats } from '../types/sales';
import { ApiResponse } from '../types/common';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const salesApi = {
  // Get all sales
  getSales: async (filters?: SalesFilters): Promise<ApiResponse<Sale[]>> => {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.paymentMethod) params.append('paymentMethod', filters.paymentMethod);
    if (filters?.customerId) params.append('customerId', filters.customerId);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`/sales?${params.toString()}`);
    return response.data;
  },

  // Get sale by ID
  getSale: async (id: string): Promise<ApiResponse<Sale>> => {
    const response = await api.get(`/sales/${id}`);
    return response.data;
  },

  // Create new sale
  createSale: async (sale: CreateSaleRequest): Promise<ApiResponse<Sale>> => {
    const response = await api.post('/sales', sale);
    return response.data;
  },

  // Update sale
  updateSale: async (id: string, sale: Partial<Sale>): Promise<ApiResponse<Sale>> => {
    const response = await api.put(`/sales/${id}`, sale);
    return response.data;
  },

  // Delete sale
  deleteSale: async (id: string): Promise<ApiResponse<void>> => {
    const response = await api.delete(`/sales/${id}`);
    return response.data;
  },

  // Get sales statistics
  getSalesStats: async (): Promise<ApiResponse<SalesStats>> => {
    const response = await api.get('/sales/stats');
    return response.data;
  },

  // Print sale invoice
  printInvoice: async (id: string): Promise<Blob> => {
    const response = await api.get(`/sales/${id}/print`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Export sales to Excel
  exportSales: async (filters?: SalesFilters): Promise<Blob> => {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);

    const response = await api.get(`/sales/export?${params.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Process refund
  processRefund: async (id: string, amount: number, reason?: string): Promise<ApiResponse<Sale>> => {
    const response = await api.post(`/sales/${id}/refund`, { amount, reason });
    return response.data;
  },
};
