const express = require('express');
const router = express.Router();

// Mock data for purchases
let purchases = [
  {
    id: '1',
    purchaseNumber: 'PO-2024-001',
    supplierId: '1',
    supplierName: 'شركة المواد الغذائية',
    supplierPhone: '+************',
    supplierEmail: '<EMAIL>',
    date: '2024-01-15',
    expectedDelivery: '2024-01-20',
    items: [
      {
        id: '1',
        productId: '1',
        productName: 'مواد خام أ',
        quantity: 100,
        unitCost: 250,
        discount: 0,
        total: 25000
      }
    ],
    subtotal: 25000,
    discount: 0,
    tax: 3750,
    total: 28750,
    paidAmount: 28750,
    remainingAmount: 0,
    status: 'received',
    paymentMethod: 'bank_transfer',
    notes: '',
    createdBy: 'admin',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    purchaseNumber: 'PO-2024-002',
    supplierId: '2',
    supplierName: 'مؤسسة التجهيزات',
    supplierPhone: '+************',
    supplierEmail: '<EMAIL>',
    date: '2024-01-16',
    expectedDelivery: '2024-01-25',
    items: [
      {
        id: '2',
        productId: '2',
        productName: 'معدات ب',
        quantity: 5,
        unitCost: 3700,
        discount: 500,
        total: 18000
      }
    ],
    subtotal: 18000,
    discount: 500,
    tax: 2700,
    total: 20200,
    paidAmount: 10000,
    remainingAmount: 10200,
    status: 'ordered',
    paymentMethod: 'credit',
    notes: 'دفعة مقدمة',
    createdBy: 'admin',
    createdAt: '2024-01-16T11:00:00Z',
    updatedAt: '2024-01-16T11:00:00Z'
  }
];

// GET /api/purchases - Get all purchases
router.get('/', (req, res) => {
  try {
    const { status, paymentMethod, supplierId, dateFrom, dateTo, search } = req.query;

    let filteredPurchases = [...purchases];

    // Apply filters
    if (status) {
      filteredPurchases = filteredPurchases.filter(purchase => purchase.status === status);
    }

    if (paymentMethod) {
      filteredPurchases = filteredPurchases.filter(purchase => purchase.paymentMethod === paymentMethod);
    }

    if (supplierId) {
      filteredPurchases = filteredPurchases.filter(purchase => purchase.supplierId === supplierId);
    }

    if (dateFrom) {
      filteredPurchases = filteredPurchases.filter(purchase => new Date(purchase.date) >= new Date(dateFrom));
    }

    if (dateTo) {
      filteredPurchases = filteredPurchases.filter(purchase => new Date(purchase.date) <= new Date(dateTo));
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredPurchases = filteredPurchases.filter(purchase =>
        purchase.purchaseNumber.toLowerCase().includes(searchLower) ||
        purchase.supplierName.toLowerCase().includes(searchLower)
      );
    }

    res.json({
      success: true,
      message: 'Purchases retrieved successfully',
      data: filteredPurchases
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving purchases',
      data: null
    });
  }
});

// GET /api/purchases/stats - Get purchases statistics
router.get('/stats', (req, res) => {
  try {
    const totalPurchases = purchases.length;
    const totalCost = purchases.reduce((sum, purchase) => sum + purchase.total, 0);
    const pendingPurchases = purchases.filter(purchase => purchase.status === 'ordered').length;
    const receivedPurchases = purchases.filter(purchase => purchase.status === 'received').length;

    const today = new Date().toISOString().split('T')[0];
    const todayPurchases = purchases.filter(purchase => purchase.date === today).length;

    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const monthlyCost = purchases
      .filter(purchase => {
        const purchaseDate = new Date(purchase.date);
        return purchaseDate.getMonth() === currentMonth && purchaseDate.getFullYear() === currentYear;
      })
      .reduce((sum, purchase) => sum + purchase.total, 0);

    res.json({
      success: true,
      message: 'Purchases statistics retrieved successfully',
      data: {
        totalPurchases,
        totalCost,
        pendingPurchases,
        receivedPurchases,
        todayPurchases,
        monthlyCost
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving purchases statistics',
      data: null
    });
  }
});

// POST /api/purchases - Create new purchase
router.post('/', (req, res) => {
  try {
    const { supplierId, items, discount = 0, paymentMethod, paidAmount = 0, expectedDelivery, notes = '' } = req.body;

    // Validate required fields
    if (!supplierId || !items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Supplier ID and items are required',
        data: null
      });
    }

    // Calculate totals
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitCost - item.discount), 0);
    const tax = subtotal * 0.15; // 15% tax
    const total = subtotal - discount + tax;

    // Generate new purchase
    const newPurchase = {
      id: (purchases.length + 1).toString(),
      purchaseNumber: `PO-2024-${String(purchases.length + 1).padStart(3, '0')}`,
      supplierId,
      supplierName: 'مورد جديد', // In real app, fetch from suppliers table
      date: new Date().toISOString().split('T')[0],
      expectedDelivery: expectedDelivery || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
      items: items.map((item, index) => ({
        ...item,
        id: (index + 1).toString(),
        total: item.quantity * item.unitCost - item.discount
      })),
      subtotal,
      discount,
      tax,
      total,
      paidAmount,
      remainingAmount: total - paidAmount,
      status: 'draft',
      paymentMethod,
      notes,
      createdBy: 'admin',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    purchases.push(newPurchase);

    res.status(201).json({
      success: true,
      message: 'Purchase created successfully',
      data: newPurchase
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error creating purchase',
      data: null
    });
  }
});

module.exports = router;