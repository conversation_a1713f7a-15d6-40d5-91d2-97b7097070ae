import React from 'react';
import { Grid, Paper, Typography, Box } from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AccountBalance,
  Schedule,
  CheckCircle,
  Warning,
} from '@mui/icons-material';
import { PaymentStats as PaymentStatsType } from '../../types/payments';

interface PaymentStatsProps {
  stats: PaymentStatsType;
}

const PaymentStats: React.FC<PaymentStatsProps> = ({ stats }) => {
  const statsCards = [
    {
      title: 'إجمالي الواردات',
      value: stats.totalIncoming.toLocaleString(),
      unit: 'ر.س',
      icon: TrendingUp,
      color: '#4caf50',
      bgColor: '#e8f5e8',
    },
    {
      title: 'إجمالي الصادرات',
      value: stats.totalOutgoing.toLocaleString(),
      unit: 'ر.س',
      icon: TrendingDown,
      color: '#f44336',
      bgColor: '#ffebee',
    },
    {
      title: 'المدفوعات المكتملة',
      value: stats.completedPayments.toString(),
      unit: 'دفعة',
      icon: CheckCircle,
      color: '#2196f3',
      bgColor: '#e3f2fd',
    },
    {
      title: 'المدفوعات المعلقة',
      value: stats.pendingPayments.toString(),
      unit: 'دفعة',
      icon: Schedule,
      color: '#ff9800',
      bgColor: '#fff3e0',
    },
    {
      title: 'المدفوعات المتأخرة',
      value: stats.overduePayments.toString(),
      unit: 'دفعة',
      icon: Warning,
      color: '#f44336',
      bgColor: '#ffebee',
    },
    {
      title: 'رصيد الشهر',
      value: (stats.monthlyIncoming - stats.monthlyOutgoing).toLocaleString(),
      unit: 'ر.س',
      icon: AccountBalance,
      color: stats.monthlyIncoming >= stats.monthlyOutgoing ? '#4caf50' : '#f44336',
      bgColor: stats.monthlyIncoming >= stats.monthlyOutgoing ? '#e8f5e8' : '#ffebee',
    },
  ];

  return (
    <Grid container spacing={3} sx={{ mb: 4 }}>
      {statsCards.map((card, index) => (
        <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
          <Paper
            sx={{
              p: 2,
              borderRadius: 2,
              backgroundColor: card.bgColor,
              border: `1px solid ${card.color}20`,
              height: '100%',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <card.icon sx={{ color: card.color, mr: 1 }} />
              <Typography variant="body2" color="text.secondary" fontWeight={500}>
                {card.title}
              </Typography>
            </Box>
            <Typography variant="h5" fontWeight={700} color={card.color}>
              {card.value}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {card.unit}
            </Typography>
          </Paper>
        </Grid>
      ))}
    </Grid>
  );
};

export default PaymentStats;
