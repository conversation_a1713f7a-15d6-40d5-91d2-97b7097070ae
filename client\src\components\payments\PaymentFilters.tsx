import React from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Paper,
  Grid,
} from '@mui/material';
import { Search, FilterList, Clear } from '@mui/icons-material';
import {
  PaymentFilters as PaymentFiltersType,
  PAYMENT_STATUSES,
  PAYMENT_METHODS,
  PAYMENT_TYPES,
} from '../../types/payments';

interface PaymentFiltersProps {
  filters: PaymentFiltersType;
  onFiltersChange: (filters: PaymentFiltersType) => void;
  onClearFilters: () => void;
}

const PaymentFilters: React.FC<PaymentFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
}) => {
  const handleFilterChange = (field: keyof PaymentFiltersType, value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value || undefined,
    });
  };

  return (
    <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} md={3}>
          <TextField
            fullWidth
            size="small"
            placeholder="البحث..."
            value={filters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            InputProps={{
              startAdornment: <Search sx={{ color: 'text.secondary', mr: 1 }} />,
            }}
          />
        </Grid>

        <Grid item xs={12} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>النوع</InputLabel>
            <Select
              value={filters.type || ''}
              label="النوع"
              onChange={(e) => handleFilterChange('type', e.target.value)}
            >
              <MenuItem value="">الكل</MenuItem>
              {PAYMENT_TYPES.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>الحالة</InputLabel>
            <Select
              value={filters.status || ''}
              label="الحالة"
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <MenuItem value="">الكل</MenuItem>
              {PAYMENT_STATUSES.map((status) => (
                <MenuItem key={status.value} value={status.value}>
                  {status.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>طريقة الدفع</InputLabel>
            <Select
              value={filters.paymentMethod || ''}
              label="طريقة الدفع"
              onChange={(e) => handleFilterChange('paymentMethod', e.target.value)}
            >
              <MenuItem value="">الكل</MenuItem>
              {PAYMENT_METHODS.map((method) => (
                <MenuItem key={method.value} value={method.value}>
                  {method.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={1.5}>
          <TextField
            fullWidth
            size="small"
            type="date"
            label="من تاريخ"
            value={filters.dateFrom || ''}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            InputLabelProps={{ shrink: true }}
          />
        </Grid>

        <Grid item xs={12} md={1.5}>
          <TextField
            fullWidth
            size="small"
            type="date"
            label="إلى تاريخ"
            value={filters.dateTo || ''}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            InputLabelProps={{ shrink: true }}
          />
        </Grid>

        <Grid item xs={12} md={12}>
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={<Clear />}
              onClick={onClearFilters}
              size="small"
            >
              مسح الفلاتر
            </Button>
            <Button
              variant="contained"
              startIcon={<FilterList />}
              size="small"
            >
              تطبيق الفلاتر
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default PaymentFilters;
