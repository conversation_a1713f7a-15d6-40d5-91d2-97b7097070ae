import axios from 'axios';
import { Settings, UpdateSettingsRequest } from '../types/settings';
import { ApiResponse } from '../types/common';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const settingsApi = {
  // Get all settings
  getSettings: async (): Promise<ApiResponse<Settings>> => {
    const response = await api.get('/settings');
    return response.data;
  },

  // Update settings section
  updateSettings: async (request: UpdateSettingsRequest): Promise<ApiResponse<Settings>> => {
    const response = await api.put(`/settings/${request.section}`, request.data);
    return response.data;
  },

  // Upload company logo
  uploadLogo: async (file: File): Promise<ApiResponse<{ url: string }>> => {
    const formData = new FormData();
    formData.append('logo', file);
    
    const response = await api.post('/settings/upload-logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Test email configuration
  testEmail: async (email: string): Promise<ApiResponse<void>> => {
    const response = await api.post('/settings/test-email', { email });
    return response.data;
  },

  // Test SMS configuration
  testSMS: async (phone: string): Promise<ApiResponse<void>> => {
    const response = await api.post('/settings/test-sms', { phone });
    return response.data;
  },

  // Backup database
  backupDatabase: async (): Promise<Blob> => {
    const response = await api.get('/settings/backup', {
      responseType: 'blob',
    });
    return response.data;
  },

  // Restore database
  restoreDatabase: async (file: File): Promise<ApiResponse<void>> => {
    const formData = new FormData();
    formData.append('backup', file);
    
    const response = await api.post('/settings/restore', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get system info
  getSystemInfo: async (): Promise<ApiResponse<{
    version: string;
    database: string;
    storage: string;
    uptime: string;
    lastBackup?: string;
  }>> => {
    const response = await api.get('/settings/system-info');
    return response.data;
  },

  // Clear cache
  clearCache: async (): Promise<ApiResponse<void>> => {
    const response = await api.post('/settings/clear-cache');
    return response.data;
  },

  // Reset settings to default
  resetToDefault: async (section: keyof Settings): Promise<ApiResponse<Settings>> => {
    const response = await api.post(`/settings/reset/${section}`);
    return response.data;
  },
};
