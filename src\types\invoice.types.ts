// Types for Professional Invoice Management System
// Compliant with Saudi Arabia VAT Phase 2 Requirements

export interface Company {
  id: string;
  name: string;
  nameEn: string;
  taxId: string; // 15-digit VAT number (300XXXXXXXXX003)
  commercialRegister: string;
  address: string;
  addressEn: string;
  city: string;
  postalCode: string;
  country: string;
  phone: string;
  email: string;
  website?: string;
  logo?: string;
  bankAccount?: BankAccount[];
}

export interface BankAccount {
  id: string;
  bankName: string;
  accountNumber: string;
  iban: string;
  swiftCode?: string;
}

export interface Customer {
  id: string;
  name: string;
  nameEn?: string;
  taxId?: string; // VAT number if applicable
  commercialRegister?: string;
  customerType: 'individual' | 'business';
  address: string;
  city: string;
  postalCode?: string;
  country: string;
  phone: string;
  email?: string;
  creditLimit?: number;
  paymentTerms: number; // days
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id: string;
  code: string;
  name: string;
  nameEn?: string;
  description?: string;
  category: string;
  unit: string; // piece, kg, liter, etc.
  price: number;
  cost?: number;
  taxRate: number; // 0, 5, 15
  taxType: 'standard' | 'zero' | 'exempt';
  isActive: boolean;
  stockQuantity?: number;
  minStockLevel?: number;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceItem {
  id: string;
  productId?: string;
  productCode?: string;
  productName: string;
  description?: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  discount: number; // percentage
  discountAmount: number;
  subtotal: number; // after discount, before tax
  taxRate: number;
  taxAmount: number;
  total: number; // final amount including tax
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  invoiceType: 'tax_invoice' | 'simplified_invoice' | 'credit_note' | 'debit_note';
  series: string; // Invoice series (e.g., 'INV', 'CN', 'DN')
  
  // Dates
  issueDate: string;
  dueDate: string;
  supplyDate?: string; // Date of supply for services
  
  // Customer Information
  customerId: string;
  customer: Customer;
  
  // Items
  items: InvoiceItem[];
  
  // Financial Calculations
  subtotal: number; // Total before discount and tax
  totalDiscount: number;
  subtotalAfterDiscount: number;
  totalTax: number;
  totalAmount: number;
  
  // Tax Breakdown (for VAT compliance)
  taxBreakdown: TaxBreakdown[];
  
  // Payment Information
  paymentTerms: number;
  paymentMethod?: 'cash' | 'bank_transfer' | 'check' | 'credit_card';
  paymentStatus: 'pending' | 'partial' | 'paid' | 'overdue';
  paidAmount: number;
  remainingAmount: number;
  
  // Status and Workflow
  status: 'draft' | 'sent' | 'viewed' | 'paid' | 'cancelled' | 'overdue';
  
  // Additional Information
  notes?: string;
  internalNotes?: string;
  terms?: string;
  
  // VAT Compliance Fields
  qrCode?: string; // QR code for simplified invoices
  previousInvoiceHash?: string; // For invoice chaining
  invoiceHash?: string;
  
  // Audit Trail
  createdBy: string;
  createdAt: string;
  updatedBy?: string;
  updatedAt?: string;
  sentAt?: string;
  
  // File Attachments
  attachments?: InvoiceAttachment[];
}

export interface TaxBreakdown {
  taxRate: number;
  taxableAmount: number;
  taxAmount: number;
  taxType: 'standard' | 'zero' | 'exempt';
}

export interface InvoiceAttachment {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  filePath: string;
  uploadedAt: string;
}

export interface InvoiceSettings {
  companyInfo: Company;
  invoiceNumberFormat: string; // e.g., "INV-{YYYY}-{MM}-{####}"
  nextInvoiceNumber: number;
  defaultPaymentTerms: number;
  defaultTaxRate: number;
  enableQRCode: boolean;
  enableInvoiceChaining: boolean;
  autoSendReminders: boolean;
  reminderDays: number[];
  emailTemplates: EmailTemplate[];
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  type: 'invoice_sent' | 'payment_reminder' | 'payment_received';
}

// Search and Filter Types
export interface InvoiceFilters {
  dateFrom?: string;
  dateTo?: string;
  customerId?: string;
  status?: Invoice['status'][];
  paymentStatus?: Invoice['paymentStatus'][];
  invoiceType?: Invoice['invoiceType'][];
  amountFrom?: number;
  amountTo?: number;
  searchTerm?: string;
}

export interface InvoiceListResponse {
  invoices: Invoice[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Dashboard Statistics
export interface InvoiceStatistics {
  totalInvoices: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  overdueAmount: number;
  thisMonthInvoices: number;
  thisMonthAmount: number;
  averageInvoiceValue: number;
  topCustomers: CustomerStatistic[];
  monthlyTrend: MonthlyTrend[];
  taxSummary: TaxSummary;
}

export interface CustomerStatistic {
  customerId: string;
  customerName: string;
  totalAmount: number;
  invoiceCount: number;
}

export interface MonthlyTrend {
  month: string;
  invoiceCount: number;
  totalAmount: number;
  paidAmount: number;
}

export interface TaxSummary {
  totalTaxableAmount: number;
  totalTaxAmount: number;
  taxBreakdown: TaxBreakdown[];
  period: {
    from: string;
    to: string;
  };
}

// Form Types
export interface InvoiceFormData {
  customerId: string;
  issueDate: string;
  dueDate: string;
  supplyDate?: string;
  paymentTerms: number;
  items: Omit<InvoiceItem, 'id' | 'subtotal' | 'taxAmount' | 'total'>[];
  notes?: string;
  terms?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Validation Schemas
export interface ValidationError {
  field: string;
  message: string;
}

// Export/Import Types
export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  dateRange?: {
    from: string;
    to: string;
  };
  filters?: InvoiceFilters;
  includeDetails: boolean;
}

// Notification Types
export interface NotificationSettings {
  emailNotifications: boolean;
  smsNotifications: boolean;
  invoiceSent: boolean;
  paymentReceived: boolean;
  paymentOverdue: boolean;
  lowStock: boolean;
}

// User Permissions
export interface UserPermissions {
  canCreateInvoice: boolean;
  canEditInvoice: boolean;
  canDeleteInvoice: boolean;
  canViewAllInvoices: boolean;
  canManageCustomers: boolean;
  canManageProducts: boolean;
  canViewReports: boolean;
  canManageSettings: boolean;
  canExportData: boolean;
}

// Audit Log
export interface AuditLog {
  id: string;
  entityType: 'invoice' | 'customer' | 'product';
  entityId: string;
  action: 'create' | 'update' | 'delete' | 'send' | 'pay';
  changes?: Record<string, { old: any; new: any }>;
  userId: string;
  userEmail: string;
  timestamp: string;
  ipAddress?: string;
}
