{"ast": null, "code": "'use client';\n\nexport { default } from './Switch';\nexport { default as switchClasses } from './switchClasses';\nexport * from './switchClasses';", "map": {"version": 3, "names": ["default", "switchClasses"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/Switch/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Switch';\nexport { default as switchClasses } from './switchClasses';\nexport * from './switchClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,UAAU;AAClC,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}