import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Typography,
  Box,
  Tooltip,
} from '@mui/material';
import {
  Edit,
  Delete,
  Visibility,
  Receipt,
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material';
import { Payment, PAYMENT_STATUSES, PAYMENT_METHODS } from '../../types/payments';

interface PaymentTableProps {
  payments: Payment[];
  onEdit?: (payment: Payment) => void;
  onDelete?: (paymentId: string) => void;
  onView?: (payment: Payment) => void;
}

const PaymentTable: React.FC<PaymentTableProps> = ({
  payments,
  onEdit,
  onDelete,
  onView,
}) => {
  const getStatusColor = (status: string) => {
    const statusConfig = PAYMENT_STATUSES.find(s => s.value === status);
    return statusConfig?.color || 'default';
  };

  const getStatusLabel = (status: string) => {
    const statusConfig = PAYMENT_STATUSES.find(s => s.value === status);
    return statusConfig?.label || status;
  };

  const getPaymentMethodLabel = (method: string) => {
    const methodConfig = PAYMENT_METHODS.find(m => m.value === method);
    return methodConfig?.label || method;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} ر.س`;
  };

  if (payments.length === 0) {
    return (
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          لا توجد مدفوعات
        </Typography>
      </Paper>
    );
  }

  return (
    <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
      <Table>
        <TableHead>
          <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              النوع
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              رقم المرجع
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              العميل/المورد
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              المبلغ
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              المدفوع
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              المتبقي
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              طريقة الدفع
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              التاريخ
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              الحالة
            </TableCell>
            <TableCell align="right" sx={{ fontWeight: 600 }}>
              الإجراءات
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {payments.map((payment) => (
            <TableRow
              key={payment.id}
              sx={{
                '&:hover': { backgroundColor: '#f9f9f9' },
                borderBottom: '1px solid #f0f0f0',
              }}
            >
              <TableCell align="right">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {payment.type === 'incoming' ? (
                    <TrendingUp sx={{ color: '#4caf50', mr: 1 }} />
                  ) : (
                    <TrendingDown sx={{ color: '#f44336', mr: 1 }} />
                  )}
                  <Typography variant="body2" fontWeight={500}>
                    {payment.type === 'incoming' ? 'وارد' : 'صادر'}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell align="right">
                <Typography variant="body2" fontWeight={500}>
                  {payment.invoiceNumber || payment.reference || '-'}
                </Typography>
              </TableCell>
              <TableCell align="right">
                <Typography variant="body2">
                  {payment.customerName || payment.supplierName || '-'}
                </Typography>
              </TableCell>
              <TableCell align="right">
                <Typography variant="body2" fontWeight={600}>
                  {formatCurrency(payment.amount)}
                </Typography>
              </TableCell>
              <TableCell align="right">
                <Typography
                  variant="body2"
                  color={payment.paidAmount > 0 ? 'success.main' : 'text.secondary'}
                  fontWeight={500}
                >
                  {formatCurrency(payment.paidAmount)}
                </Typography>
              </TableCell>
              <TableCell align="right">
                <Typography
                  variant="body2"
                  color={payment.remainingAmount > 0 ? 'warning.main' : 'success.main'}
                  fontWeight={500}
                >
                  {formatCurrency(payment.remainingAmount)}
                </Typography>
              </TableCell>
              <TableCell align="right">
                <Typography variant="body2">
                  {getPaymentMethodLabel(payment.paymentMethod)}
                </Typography>
              </TableCell>
              <TableCell align="right">
                <Typography variant="body2">
                  {formatDate(payment.paymentDate)}
                </Typography>
              </TableCell>
              <TableCell align="right">
                <Chip
                  label={getStatusLabel(payment.status)}
                  color={getStatusColor(payment.status)}
                  size="small"
                  sx={{ fontWeight: 500 }}
                />
              </TableCell>
              <TableCell align="right">
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  {onView && (
                    <Tooltip title="عرض">
                      <IconButton
                        size="small"
                        onClick={() => onView(payment)}
                        sx={{ color: 'primary.main' }}
                      >
                        <Visibility fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                  {onEdit && (
                    <Tooltip title="تعديل">
                      <IconButton
                        size="small"
                        onClick={() => onEdit(payment)}
                        sx={{ color: 'warning.main' }}
                      >
                        <Edit fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                  {onDelete && (
                    <Tooltip title="حذف">
                      <IconButton
                        size="small"
                        onClick={() => onDelete(payment.id)}
                        sx={{ color: 'error.main' }}
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                  <Tooltip title="طباعة الإيصال">
                    <IconButton
                      size="small"
                      sx={{ color: 'info.main' }}
                    >
                      <Receipt fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default PaymentTable;
