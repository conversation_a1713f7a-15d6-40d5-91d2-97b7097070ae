export interface CompanySettings {
  name: string;
  nameEn?: string;
  logo?: string;
  address: string;
  city: string;
  country: string;
  postalCode?: string;
  phone: string;
  mobile?: string;
  email: string;
  website?: string;
  taxNumber: string;
  commercialRegister?: string;
  description?: string;
}

export interface SystemSettings {
  currency: string;
  language: 'ar' | 'en';
  timezone: string;
  dateFormat: string;
  timeFormat: '12' | '24';
  fiscalYearStart: string;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  autoBackup: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

export interface AccountingSettings {
  taxRate: number;
  enableInventoryTracking: boolean;
  enableMultiCurrency: boolean;
  defaultPaymentTerms: number;
  lowStockThreshold: number;
  autoGenerateInvoiceNumbers: boolean;
  invoicePrefix: string;
  purchasePrefix: string;
  enableDiscounts: boolean;
  maxDiscountPercentage: number;
  requireApprovalForLargeTransactions: boolean;
  largeTransactionThreshold: number;
}

export interface SecuritySettings {
  passwordMinLength: number;
  passwordRequireUppercase: boolean;
  passwordRequireLowercase: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireSymbols: boolean;
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  enableTwoFactor: boolean;
  allowMultipleSessions: boolean;
}

export interface NotificationSettings {
  emailNotifications: {
    newSale: boolean;
    newPurchase: boolean;
    lowStock: boolean;
    overdueInvoices: boolean;
    systemUpdates: boolean;
  };
  smsNotifications: {
    newSale: boolean;
    newPurchase: boolean;
    lowStock: boolean;
    overdueInvoices: boolean;
  };
  pushNotifications: {
    newSale: boolean;
    newPurchase: boolean;
    lowStock: boolean;
    overdueInvoices: boolean;
  };
}

export interface PrintSettings {
  defaultPrinter: string;
  paperSize: 'A4' | 'A5' | 'Letter' | 'Thermal';
  orientation: 'portrait' | 'landscape';
  margins: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  includeLogo: boolean;
  includeWatermark: boolean;
  watermarkText?: string;
  footerText?: string;
}

export interface Settings {
  company: CompanySettings;
  system: SystemSettings;
  accounting: AccountingSettings;
  security: SecuritySettings;
  notifications: NotificationSettings;
  printing: PrintSettings;
}

export interface UpdateSettingsRequest {
  section: keyof Settings;
  data: Partial<Settings[keyof Settings]>;
}

export const CURRENCIES = [
  { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
  { code: 'USD', name: 'دولار أمريكي', symbol: '$' },
  { code: 'EUR', name: 'يورو', symbol: '€' },
  { code: 'AED', name: 'درهم إماراتي', symbol: 'د.إ' },
  { code: 'KWD', name: 'دينار كويتي', symbol: 'د.ك' },
];

export const TIMEZONES = [
  { value: 'Asia/Riyadh', label: 'الرياض (GMT+3)' },
  { value: 'Asia/Dubai', label: 'دبي (GMT+4)' },
  { value: 'Asia/Kuwait', label: 'الكويت (GMT+3)' },
  { value: 'Asia/Qatar', label: 'قطر (GMT+3)' },
  { value: 'Asia/Bahrain', label: 'البحرين (GMT+3)' },
];

export const DATE_FORMATS = [
  { value: 'DD/MM/YYYY', label: 'يوم/شهر/سنة (31/12/2024)' },
  { value: 'MM/DD/YYYY', label: 'شهر/يوم/سنة (12/31/2024)' },
  { value: 'YYYY-MM-DD', label: 'سنة-شهر-يوم (2024-12-31)' },
  { value: 'DD-MM-YYYY', label: 'يوم-شهر-سنة (31-12-2024)' },
];

export const PAPER_SIZES = [
  { value: 'A4', label: 'A4 (210 × 297 مم)' },
  { value: 'A5', label: 'A5 (148 × 210 مم)' },
  { value: 'Letter', label: 'Letter (216 × 279 مم)' },
  { value: 'Thermal', label: 'Thermal (80 مم)' },
];
