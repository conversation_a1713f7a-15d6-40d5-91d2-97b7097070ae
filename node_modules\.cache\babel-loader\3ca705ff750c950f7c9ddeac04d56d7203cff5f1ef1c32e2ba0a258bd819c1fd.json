{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\zencod\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport App from './App';\n\n// Create theme\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  direction: 'rtl',\n  palette: {\n    primary: {\n      main: '#1976d2'\n    }\n  },\n  typography: {\n    fontFamily: ['Cairo', 'Roboto', 'Arial', 'sans-serif'].join(',')\n  }\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 30,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "ThemeProvider", "createTheme", "CssBaseline", "App", "jsxDEV", "_jsxDEV", "theme", "direction", "palette", "primary", "main", "typography", "fontFamily", "join", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/src/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\r\nimport { CssBaseline } from '@mui/material';\r\nimport App from './App';\r\n\r\n// Create theme\r\nconst theme = createTheme({\r\n  direction: 'rtl',\r\n  palette: {\r\n    primary: {\r\n      main: '#1976d2',\r\n    },\r\n  },\r\n  typography: {\r\n    fontFamily: [\r\n      'Cairo',\r\n      'Roboto',\r\n      'Arial',\r\n      'sans-serif',\r\n    ].join(','),\r\n  },\r\n});\r\n\r\nconst root = ReactDOM.createRoot(\r\n  document.getElementById('root') as HTMLElement\r\n);\r\n\r\nroot.render(\r\n  <React.StrictMode>\r\n    <ThemeProvider theme={theme}>\r\n      <CssBaseline />\r\n      <App />\r\n    </ThemeProvider>\r\n  </React.StrictMode>\r\n);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,GAAG,MAAM,OAAO;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGL,WAAW,CAAC;EACxBM,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR;EACF,CAAC;EACDC,UAAU,EAAE;IACVC,UAAU,EAAE,CACV,OAAO,EACP,QAAQ,EACR,OAAO,EACP,YAAY,CACb,CAACC,IAAI,CAAC,GAAG;EACZ;AACF,CAAC,CAAC;AAEF,MAAMC,IAAI,GAAGf,QAAQ,CAACgB,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDH,IAAI,CAACI,MAAM,cACTb,OAAA,CAACP,KAAK,CAACqB,UAAU;EAAAC,QAAA,eACff,OAAA,CAACL,aAAa;IAACM,KAAK,EAAEA,KAAM;IAAAc,QAAA,gBAC1Bf,OAAA,CAACH,WAAW;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfnB,OAAA,CAACF,GAAG;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACA,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}