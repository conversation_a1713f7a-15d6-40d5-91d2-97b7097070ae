import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Button, TextField, Switch, FormControlLabel, Di<PERSON>r, Alert } from '@mui/material';

// Mock settings data
interface Settings {
  company: {
    name: string;
    email: string;
    phone: string;
    address: string;
    taxNumber: string;
  };
  system: {
    currency: string;
    language: string;
    timezone: string;
    dateFormat: string;
  };
  accounting: {
    taxRate: number;
    enableInventoryTracking: boolean;
    autoGenerateInvoiceNumbers: boolean;
    invoicePrefix: string;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    lowStockAlerts: boolean;
    overdueInvoiceAlerts: boolean;
  };
}

const mockSettings: Settings = {
  company: {
    name: 'شركة زين كود',
    email: '<EMAIL>',
    phone: '+************',
    address: 'الرياض، المملكة العربية السعودية',
    taxNumber: '***************'
  },
  system: {
    currency: 'SAR',
    language: 'ar',
    timezone: 'Asia/Riyadh',
    dateFormat: 'DD/MM/YYYY'
  },
  accounting: {
    taxRate: 15,
    enableInventoryTracking: true,
    autoGenerateInvoiceNumbers: true,
    invoicePrefix: 'INV'
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    lowStockAlerts: true,
    overdueInvoiceAlerts: true
  }
};

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<Settings>(mockSettings);
  const [loading, setLoading] = useState(false);
  const [saved, setSaved] = useState(false);
  const [activeTab, setActiveTab] = useState('company');

  // Simulate loading data
  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleSave = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setSaved(true);
      setTimeout(() => setSaved(false), 3000);
    }, 1000);
  };

  const handleCompanyChange = (field: keyof Settings['company'], value: string) => {
    setSettings(prev => ({
      ...prev,
      company: {
        ...prev.company,
        [field]: value
      }
    }));
  };

  const handleSystemChange = (field: keyof Settings['system'], value: string) => {
    setSettings(prev => ({
      ...prev,
      system: {
        ...prev.system,
        [field]: value
      }
    }));
  };

  const handleAccountingChange = (field: keyof Settings['accounting'], value: string | number | boolean) => {
    setSettings(prev => ({
      ...prev,
      accounting: {
        ...prev.accounting,
        [field]: value
      }
    }));
  };

  const handleNotificationChange = (field: keyof Settings['notifications'], value: boolean) => {
    setSettings(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [field]: value
      }
    }));
  };

  const renderTabButton = (tabId: string, label: string) => (
    <Button
      key={tabId}
      variant={activeTab === tabId ? 'contained' : 'outlined'}
      onClick={() => setActiveTab(tabId)}
      sx={{ mr: 1, mb: 1 }}
    >
      {label}
    </Button>
  );

  const renderCompanySettings = () => (
    <Box>
      <Typography variant="h6" gutterBottom>إعدادات الشركة</Typography>
      <Box sx={{ display: 'grid', gap: 2, mt: 2 }}>
        <TextField
          label="اسم الشركة"
          value={settings.company.name}
          onChange={(e) => handleCompanyChange('name', e.target.value)}
          fullWidth
        />
        <TextField
          label="البريد الإلكتروني"
          type="email"
          value={settings.company.email}
          onChange={(e) => handleCompanyChange('email', e.target.value)}
          fullWidth
        />
        <TextField
          label="رقم الهاتف"
          value={settings.company.phone}
          onChange={(e) => handleCompanyChange('phone', e.target.value)}
          fullWidth
        />
        <TextField
          label="العنوان"
          value={settings.company.address}
          onChange={(e) => handleCompanyChange('address', e.target.value)}
          multiline
          rows={3}
          fullWidth
        />
        <TextField
          label="الرقم الضريبي"
          value={settings.company.taxNumber}
          onChange={(e) => handleCompanyChange('taxNumber', e.target.value)}
          fullWidth
        />
      </Box>
    </Box>
  );

  const renderSystemSettings = () => (
    <Box>
      <Typography variant="h6" gutterBottom>إعدادات النظام</Typography>
      <Box sx={{ display: 'grid', gap: 2, mt: 2 }}>
        <TextField
          label="العملة"
          value={settings.system.currency}
          onChange={(e) => handleSystemChange('currency', e.target.value)}
          fullWidth
        />
        <TextField
          label="اللغة"
          value={settings.system.language}
          onChange={(e) => handleSystemChange('language', e.target.value)}
          fullWidth
        />
        <TextField
          label="المنطقة الزمنية"
          value={settings.system.timezone}
          onChange={(e) => handleSystemChange('timezone', e.target.value)}
          fullWidth
        />
        <TextField
          label="تنسيق التاريخ"
          value={settings.system.dateFormat}
          onChange={(e) => handleSystemChange('dateFormat', e.target.value)}
          fullWidth
        />
      </Box>
    </Box>
  );

  const renderAccountingSettings = () => (
    <Box>
      <Typography variant="h6" gutterBottom>إعدادات المحاسبة</Typography>
      <Box sx={{ display: 'grid', gap: 2, mt: 2 }}>
        <TextField
          label="معدل الضريبة (%)"
          type="number"
          value={settings.accounting.taxRate}
          onChange={(e) => handleAccountingChange('taxRate', parseFloat(e.target.value))}
          fullWidth
        />
        <TextField
          label="بادئة رقم الفاتورة"
          value={settings.accounting.invoicePrefix}
          onChange={(e) => handleAccountingChange('invoicePrefix', e.target.value)}
          fullWidth
        />
        <FormControlLabel
          control={
            <Switch
              checked={settings.accounting.enableInventoryTracking}
              onChange={(e) => handleAccountingChange('enableInventoryTracking', e.target.checked)}
            />
          }
          label="تفعيل تتبع المخزون"
        />
        <FormControlLabel
          control={
            <Switch
              checked={settings.accounting.autoGenerateInvoiceNumbers}
              onChange={(e) => handleAccountingChange('autoGenerateInvoiceNumbers', e.target.checked)}
            />
          }
          label="توليد أرقام الفواتير تلقائياً"
        />
      </Box>
    </Box>
  );

  const renderNotificationSettings = () => (
    <Box>
      <Typography variant="h6" gutterBottom>إعدادات الإشعارات</Typography>
      <Box sx={{ display: 'grid', gap: 2, mt: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.notifications.emailNotifications}
              onChange={(e) => handleNotificationChange('emailNotifications', e.target.checked)}
            />
          }
          label="إشعارات البريد الإلكتروني"
        />
        <FormControlLabel
          control={
            <Switch
              checked={settings.notifications.smsNotifications}
              onChange={(e) => handleNotificationChange('smsNotifications', e.target.checked)}
            />
          }
          label="إشعارات الرسائل النصية"
        />
        <FormControlLabel
          control={
            <Switch
              checked={settings.notifications.lowStockAlerts}
              onChange={(e) => handleNotificationChange('lowStockAlerts', e.target.checked)}
            />
          }
          label="تنبيهات نفاد المخزون"
        />
        <FormControlLabel
          control={
            <Switch
              checked={settings.notifications.overdueInvoiceAlerts}
              onChange={(e) => handleNotificationChange('overdueInvoiceAlerts', e.target.checked)}
            />
          }
          label="تنبيهات الفواتير المتأخرة"
        />
      </Box>
    </Box>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'company':
        return renderCompanySettings();
      case 'system':
        return renderSystemSettings();
      case 'accounting':
        return renderAccountingSettings();
      case 'notifications':
        return renderNotificationSettings();
      default:
        return renderCompanySettings();
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight={700}>
          إعدادات النظام
        </Typography>
        <Button
          variant="contained"
          onClick={handleSave}
          disabled={loading}
        >
          {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
        </Button>
      </Box>

      {/* Success Alert */}
      {saved && (
        <Alert severity="success" sx={{ mb: 3 }}>
          تم حفظ الإعدادات بنجاح!
        </Alert>
      )}

      {/* Tab Navigation */}
      <Box sx={{ mb: 3 }}>
        {renderTabButton('company', 'إعدادات الشركة')}
        {renderTabButton('system', 'إعدادات النظام')}
        {renderTabButton('accounting', 'إعدادات المحاسبة')}
        {renderTabButton('notifications', 'إعدادات الإشعارات')}
      </Box>

      <Divider sx={{ mb: 3 }} />

      {/* Tab Content */}
      <Paper sx={{ p: 3, borderRadius: 2 }}>
        {loading ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography>جاري تحميل الإعدادات...</Typography>
          </Box>
        ) : (
          renderTabContent()
        )}
      </Paper>

      {/* Additional Settings */}
      <Paper sx={{ p: 3, borderRadius: 2, mt: 3 }}>
        <Typography variant="h6" gutterBottom>إعدادات متقدمة</Typography>
        <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
          <Button
            variant="outlined"
            color="info"
            onClick={() => alert('سيتم إنشاء نسخة احتياطية من قاعدة البيانات')}
          >
            نسخ احتياطي لقاعدة البيانات
          </Button>
          <Button
            variant="outlined"
            color="warning"
            onClick={() => alert('سيتم مسح ذاكرة التخزين المؤقت')}
          >
            مسح ذاكرة التخزين المؤقت
          </Button>
          <Button
            variant="outlined"
            color="error"
            onClick={() => alert('هذه العملية خطيرة! هل أنت متأكد؟')}
          >
            إعادة تعيين النظام
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default SettingsPage;