{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SystemInitColorSchemeScript from '@mui/system/InitColorSchemeScript';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const defaultConfig = {\n  attribute: 'data-mui-color-scheme',\n  colorSchemeStorageKey: 'mui-color-scheme',\n  defaultLightColorScheme: 'light',\n  defaultDarkColorScheme: 'dark',\n  modeStorageKey: 'mui-mode'\n};\nexport default (function InitColorSchemeScript(props) {\n  return /*#__PURE__*/_jsx(SystemInitColorSchemeScript, _extends({}, defaultConfig, props));\n});", "map": {"version": 3, "names": ["_extends", "React", "SystemInitColorSchemeScript", "jsx", "_jsx", "defaultConfig", "attribute", "colorSchemeStorageKey", "defaultLightColorScheme", "defaultDarkColorScheme", "modeStorageKey", "InitColorSchemeScript", "props"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/InitColorSchemeScript/InitColorSchemeScript.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SystemInitColorSchemeScript from '@mui/system/InitColorSchemeScript';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const defaultConfig = {\n  attribute: 'data-mui-color-scheme',\n  colorSchemeStorageKey: 'mui-color-scheme',\n  defaultLightColorScheme: 'light',\n  defaultDarkColorScheme: 'dark',\n  modeStorageKey: 'mui-mode'\n};\nexport default (function InitColorSchemeScript(props) {\n  return /*#__PURE__*/_jsx(SystemInitColorSchemeScript, _extends({}, defaultConfig, props));\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,2BAA2B,MAAM,mCAAmC;AAC3E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,aAAa,GAAG;EAC3BC,SAAS,EAAE,uBAAuB;EAClCC,qBAAqB,EAAE,kBAAkB;EACzCC,uBAAuB,EAAE,OAAO;EAChCC,sBAAsB,EAAE,MAAM;EAC9BC,cAAc,EAAE;AAClB,CAAC;AACD,gBAAgB,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EACpD,OAAO,aAAaR,IAAI,CAACF,2BAA2B,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEK,aAAa,EAAEO,KAAK,CAAC,CAAC;AAC3F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}