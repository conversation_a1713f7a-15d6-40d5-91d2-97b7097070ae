export interface SalesItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  discount: number;
  total: number;
}

export interface Sale {
  id: string;
  invoiceNumber: string;
  customerId: string;
  customerName: string;
  customerPhone?: string;
  customerEmail?: string;
  date: string;
  dueDate?: string;
  items: SalesItem[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  paidAmount: number;
  remainingAmount: number;
  status: SaleStatus;
  paymentMethod: PaymentMethod;
  notes?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export type SaleStatus = 'draft' | 'pending' | 'completed' | 'cancelled' | 'refunded';
export type PaymentMethod = 'cash' | 'card' | 'bank_transfer' | 'check' | 'installment';

export interface CreateSaleRequest {
  customerId: string;
  items: Omit<SalesItem, 'id' | 'total'>[];
  discount?: number;
  paymentMethod: PaymentMethod;
  paidAmount?: number;
  notes?: string;
}

export interface SalesFilters {
  status?: SaleStatus;
  paymentMethod?: PaymentMethod;
  customerId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface SalesStats {
  totalSales: number;
  totalRevenue: number;
  pendingSales: number;
  completedSales: number;
  todaySales: number;
  monthlyRevenue: number;
}

export const SALE_STATUSES: { value: SaleStatus; label: string; color: 'success' | 'warning' | 'error' | 'default' | 'info' }[] = [
  { value: 'draft', label: 'مسودة', color: 'default' },
  { value: 'pending', label: 'في الانتظار', color: 'warning' },
  { value: 'completed', label: 'مكتملة', color: 'success' },
  { value: 'cancelled', label: 'ملغية', color: 'error' },
  { value: 'refunded', label: 'مسترد', color: 'info' },
];

export const PAYMENT_METHODS: { value: PaymentMethod; label: string }[] = [
  { value: 'cash', label: 'نقداً' },
  { value: 'card', label: 'بطاقة' },
  { value: 'bank_transfer', label: 'تحويل بنكي' },
  { value: 'check', label: 'شيك' },
  { value: 'installment', label: 'أقساط' },
];
