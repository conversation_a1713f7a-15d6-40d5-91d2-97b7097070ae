{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"initialWidth\", \"width\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport { getThemeProps } from '@mui/system/useThemeProps';\nimport useTheme from '../styles/useTheme';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useMediaQuery from '../useMediaQuery';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\n\n// By default, returns true if screen width is the same or greater than the given breakpoint.\nexport const isWidthUp = (breakpoint, width, inclusive = true) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(breakpoint) <= breakpointKeys.indexOf(width);\n  }\n  return breakpointKeys.indexOf(breakpoint) < breakpointKeys.indexOf(width);\n};\n\n// By default, returns true if screen width is less than the given breakpoint.\nexport const isWidthDown = (breakpoint, width, inclusive = false) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(width) <= breakpointKeys.indexOf(breakpoint);\n  }\n  return breakpointKeys.indexOf(width) < breakpointKeys.indexOf(breakpoint);\n};\nconst withWidth = (options = {}) => Component => {\n  const {\n    withTheme: withThemeOption = false,\n    noSSR = false,\n    initialWidth: initialWidthOption\n  } = options;\n  function WithWidth(props) {\n    const contextTheme = useTheme();\n    const theme = props.theme || contextTheme;\n    const _getThemeProps = getThemeProps({\n        theme,\n        name: 'MuiWithWidth',\n        props\n      }),\n      {\n        initialWidth,\n        width\n      } = _getThemeProps,\n      other = _objectWithoutPropertiesLoose(_getThemeProps, _excluded);\n    const [mountedState, setMountedState] = React.useState(false);\n    useEnhancedEffect(() => {\n      setMountedState(true);\n    }, []);\n\n    /**\n     * innerWidth |xs      sm      md      lg      xl\n     *            |-------|-------|-------|-------|------>\n     * width      |  xs   |  sm   |  md   |  lg   |  xl\n     */\n    const keys = theme.breakpoints.keys.slice().reverse();\n    const widthComputed = keys.reduce((output, key) => {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      const matches = useMediaQuery(theme.breakpoints.up(key));\n      return !output && matches ? key : output;\n    }, null);\n    const more = _extends({\n      width: width || (mountedState || noSSR ? widthComputed : undefined) || initialWidth || initialWidthOption\n    }, withThemeOption ? {\n      theme\n    } : {}, other);\n\n    // When rendering the component on the server,\n    // we have no idea about the client browser screen width.\n    // In order to prevent blinks and help the reconciliation of the React tree\n    // we are not rendering the child component.\n    //\n    // An alternative is to use the `initialWidth` property.\n    if (more.width === undefined) {\n      return null;\n    }\n    return /*#__PURE__*/_jsx(Component, _extends({}, more));\n  }\n  process.env.NODE_ENV !== \"production\" ? WithWidth.propTypes = {\n    /**\n     * As `window.innerWidth` is unavailable on the server,\n     * we default to rendering an empty component during the first mount.\n     * You might want to use a heuristic to approximate\n     * the screen width of the client browser screen width.\n     *\n     * For instance, you could be using the user-agent or the client-hints.\n     * https://caniuse.com/#search=client%20hint\n     */\n    initialWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),\n    /**\n     * @ignore\n     */\n    theme: PropTypes.object,\n    /**\n     * Bypass the width calculation logic.\n     */\n    width: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl'])\n  } : void 0;\n  if (process.env.NODE_ENV !== 'production') {\n    WithWidth.displayName = `WithWidth(${getDisplayName(Component)})`;\n  }\n  return WithWidth;\n};\nexport default withWidth;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "getDisplayName", "getThemeProps", "useTheme", "useEnhancedEffect", "useMediaQuery", "jsx", "_jsx", "breakpoint<PERSON><PERSON><PERSON>", "isWidthUp", "breakpoint", "width", "inclusive", "indexOf", "isWidthDown", "with<PERSON><PERSON><PERSON>", "options", "Component", "withTheme", "withThemeOption", "noSSR", "initialWidth", "initialWidthOption", "<PERSON><PERSON><PERSON><PERSON>", "props", "contextTheme", "theme", "_getThemeProps", "name", "other", "mountedState", "setMountedState", "useState", "keys", "breakpoints", "slice", "reverse", "widthComputed", "reduce", "output", "key", "matches", "up", "more", "undefined", "process", "env", "NODE_ENV", "propTypes", "oneOf", "object", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/Hidden/withWidth.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"initialWidth\", \"width\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport { getThemeProps } from '@mui/system/useThemeProps';\nimport useTheme from '../styles/useTheme';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useMediaQuery from '../useMediaQuery';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\n\n// By default, returns true if screen width is the same or greater than the given breakpoint.\nexport const isWidthUp = (breakpoint, width, inclusive = true) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(breakpoint) <= breakpointKeys.indexOf(width);\n  }\n  return breakpointKeys.indexOf(breakpoint) < breakpointKeys.indexOf(width);\n};\n\n// By default, returns true if screen width is less than the given breakpoint.\nexport const isWidthDown = (breakpoint, width, inclusive = false) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(width) <= breakpointKeys.indexOf(breakpoint);\n  }\n  return breakpointKeys.indexOf(width) < breakpointKeys.indexOf(breakpoint);\n};\nconst withWidth = (options = {}) => Component => {\n  const {\n    withTheme: withThemeOption = false,\n    noSSR = false,\n    initialWidth: initialWidthOption\n  } = options;\n  function WithWidth(props) {\n    const contextTheme = useTheme();\n    const theme = props.theme || contextTheme;\n    const _getThemeProps = getThemeProps({\n        theme,\n        name: 'MuiWithWidth',\n        props\n      }),\n      {\n        initialWidth,\n        width\n      } = _getThemeProps,\n      other = _objectWithoutPropertiesLoose(_getThemeProps, _excluded);\n    const [mountedState, setMountedState] = React.useState(false);\n    useEnhancedEffect(() => {\n      setMountedState(true);\n    }, []);\n\n    /**\n     * innerWidth |xs      sm      md      lg      xl\n     *            |-------|-------|-------|-------|------>\n     * width      |  xs   |  sm   |  md   |  lg   |  xl\n     */\n    const keys = theme.breakpoints.keys.slice().reverse();\n    const widthComputed = keys.reduce((output, key) => {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      const matches = useMediaQuery(theme.breakpoints.up(key));\n      return !output && matches ? key : output;\n    }, null);\n    const more = _extends({\n      width: width || (mountedState || noSSR ? widthComputed : undefined) || initialWidth || initialWidthOption\n    }, withThemeOption ? {\n      theme\n    } : {}, other);\n\n    // When rendering the component on the server,\n    // we have no idea about the client browser screen width.\n    // In order to prevent blinks and help the reconciliation of the React tree\n    // we are not rendering the child component.\n    //\n    // An alternative is to use the `initialWidth` property.\n    if (more.width === undefined) {\n      return null;\n    }\n    return /*#__PURE__*/_jsx(Component, _extends({}, more));\n  }\n  process.env.NODE_ENV !== \"production\" ? WithWidth.propTypes = {\n    /**\n     * As `window.innerWidth` is unavailable on the server,\n     * we default to rendering an empty component during the first mount.\n     * You might want to use a heuristic to approximate\n     * the screen width of the client browser screen width.\n     *\n     * For instance, you could be using the user-agent or the client-hints.\n     * https://caniuse.com/#search=client%20hint\n     */\n    initialWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),\n    /**\n     * @ignore\n     */\n    theme: PropTypes.object,\n    /**\n     * Bypass the width calculation logic.\n     */\n    width: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl'])\n  } : void 0;\n  if (process.env.NODE_ENV !== 'production') {\n    WithWidth.displayName = `WithWidth(${getDisplayName(Component)})`;\n  }\n  return WithWidth;\n};\nexport default withWidth;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;AAErD;AACA,OAAO,MAAMC,SAAS,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,SAAS,GAAG,IAAI,KAAK;EAChE,IAAIA,SAAS,EAAE;IACb,OAAOJ,cAAc,CAACK,OAAO,CAACH,UAAU,CAAC,IAAIF,cAAc,CAACK,OAAO,CAACF,KAAK,CAAC;EAC5E;EACA,OAAOH,cAAc,CAACK,OAAO,CAACH,UAAU,CAAC,GAAGF,cAAc,CAACK,OAAO,CAACF,KAAK,CAAC;AAC3E,CAAC;;AAED;AACA,OAAO,MAAMG,WAAW,GAAGA,CAACJ,UAAU,EAAEC,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAK;EACnE,IAAIA,SAAS,EAAE;IACb,OAAOJ,cAAc,CAACK,OAAO,CAACF,KAAK,CAAC,IAAIH,cAAc,CAACK,OAAO,CAACH,UAAU,CAAC;EAC5E;EACA,OAAOF,cAAc,CAACK,OAAO,CAACF,KAAK,CAAC,GAAGH,cAAc,CAACK,OAAO,CAACH,UAAU,CAAC;AAC3E,CAAC;AACD,MAAMK,SAAS,GAAGA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAKC,SAAS,IAAI;EAC/C,MAAM;IACJC,SAAS,EAAEC,eAAe,GAAG,KAAK;IAClCC,KAAK,GAAG,KAAK;IACbC,YAAY,EAAEC;EAChB,CAAC,GAAGN,OAAO;EACX,SAASO,SAASA,CAACC,KAAK,EAAE;IACxB,MAAMC,YAAY,GAAGtB,QAAQ,CAAC,CAAC;IAC/B,MAAMuB,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAID,YAAY;IACzC,MAAME,cAAc,GAAGzB,aAAa,CAAC;QACjCwB,KAAK;QACLE,IAAI,EAAE,cAAc;QACpBJ;MACF,CAAC,CAAC;MACF;QACEH,YAAY;QACZV;MACF,CAAC,GAAGgB,cAAc;MAClBE,KAAK,GAAGhC,6BAA6B,CAAC8B,cAAc,EAAE7B,SAAS,CAAC;IAClE,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGhC,KAAK,CAACiC,QAAQ,CAAC,KAAK,CAAC;IAC7D5B,iBAAiB,CAAC,MAAM;MACtB2B,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,EAAE,EAAE,CAAC;;IAEN;AACJ;AACA;AACA;AACA;IACI,MAAME,IAAI,GAAGP,KAAK,CAACQ,WAAW,CAACD,IAAI,CAACE,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACrD,MAAMC,aAAa,GAAGJ,IAAI,CAACK,MAAM,CAAC,CAACC,MAAM,EAAEC,GAAG,KAAK;MACjD;MACA,MAAMC,OAAO,GAAGpC,aAAa,CAACqB,KAAK,CAACQ,WAAW,CAACQ,EAAE,CAACF,GAAG,CAAC,CAAC;MACxD,OAAO,CAACD,MAAM,IAAIE,OAAO,GAAGD,GAAG,GAAGD,MAAM;IAC1C,CAAC,EAAE,IAAI,CAAC;IACR,MAAMI,IAAI,GAAG/C,QAAQ,CAAC;MACpBe,KAAK,EAAEA,KAAK,KAAKmB,YAAY,IAAIV,KAAK,GAAGiB,aAAa,GAAGO,SAAS,CAAC,IAAIvB,YAAY,IAAIC;IACzF,CAAC,EAAEH,eAAe,GAAG;MACnBO;IACF,CAAC,GAAG,CAAC,CAAC,EAAEG,KAAK,CAAC;;IAEd;IACA;IACA;IACA;IACA;IACA;IACA,IAAIc,IAAI,CAAChC,KAAK,KAAKiC,SAAS,EAAE;MAC5B,OAAO,IAAI;IACb;IACA,OAAO,aAAarC,IAAI,CAACU,SAAS,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAE+C,IAAI,CAAC,CAAC;EACzD;EACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,SAAS,CAACyB,SAAS,GAAG;IAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI3B,YAAY,EAAErB,SAAS,CAACiD,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7D;AACJ;AACA;IACIvB,KAAK,EAAE1B,SAAS,CAACkD,MAAM;IACvB;AACJ;AACA;IACIvC,KAAK,EAAEX,SAAS,CAACiD,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvD,CAAC,GAAG,KAAK,CAAC;EACV,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCxB,SAAS,CAAC4B,WAAW,GAAG,aAAalD,cAAc,CAACgB,SAAS,CAAC,GAAG;EACnE;EACA,OAAOM,SAAS;AAClB,CAAC;AACD,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}