import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  useTheme,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AccountBalance,
  People,
  Inventory,
  Receipt,
} from '@mui/icons-material';

import StatsCard from '../../components/Dashboard/StatsCard';
import SalesChart from '../../components/Dashboard/SalesChart';
import RecentTransactions from '../../components/Dashboard/RecentTransactions';
import TopProducts from '../../components/Dashboard/TopProducts';

const Dashboard: React.FC = () => {
  const theme = useTheme();

  const statsData = [
    {
      title: 'إجمالي المبيعات',
      value: '125,430',
      unit: 'ريال',
      change: 12.5,
      changeType: 'increase' as const,
      icon: TrendingUp,
      color: '#4caf50',
    },
    {
      title: 'إجمالي المشتريات',
      value: '89,250',
      unit: 'ريال',
      change: -5.2,
      changeType: 'decrease' as const,
      icon: TrendingDown,
      color: '#f44336',
    },
    {
      title: 'عدد العملاء',
      value: '1,234',
      unit: 'عميل',
      change: 8.1,
      changeType: 'increase' as const,
      icon: People,
      color: '#2196f3',
    },
    {
      title: 'المنتجات في المخزون',
      value: '567',
      unit: 'منتج',
      change: -2.3,
      changeType: 'decrease' as const,
      icon: Inventory,
      color: '#ff9800',
    },
  ];

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
          لوحة التحكم
        </Typography>
        <Typography variant="body1" color="text.secondary">
          نظرة عامة على أداء النشاط التجاري
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statsData.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatsCard {...stat} />
          </Grid>
        ))}
      </Grid>

      {/* Charts and Tables */}
      <Grid container spacing={3}>
        {/* Sales Chart */}
        <Grid item xs={12} lg={8}>
          <Paper
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            }}
          >
            <Typography variant="h6" gutterBottom fontWeight={600}>
              مبيعات الأشهر الأخيرة
            </Typography>
            <SalesChart />
          </Paper>
        </Grid>

        {/* Top Products */}
        <Grid item xs={12} lg={4}>
          <Paper
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              height: 400,
            }}
          >
            <Typography variant="h6" gutterBottom fontWeight={600}>
              أفضل المنتجات مبيعاً
            </Typography>
            <TopProducts />
          </Paper>
        </Grid>

        {/* Recent Transactions */}
        <Grid item xs={12}>
          <Paper
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            }}
          >
            <Typography variant="h6" gutterBottom fontWeight={600}>
              المعاملات الأخيرة
            </Typography>
            <RecentTransactions />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;