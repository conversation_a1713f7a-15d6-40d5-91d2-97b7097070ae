{"ast": null, "code": "import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ButtonGroupContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ButtonGroupContext.displayName = 'ButtonGroupContext';\n}\nexport default ButtonGroupContext;", "map": {"version": 3, "names": ["React", "ButtonGroupContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/ButtonGroup/ButtonGroupContext.js"], "sourcesContent": ["import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ButtonGroupContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ButtonGroupContext.displayName = 'ButtonGroupContext';\n}\nexport default ButtonGroupContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC/D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,kBAAkB,CAACK,WAAW,GAAG,oBAAoB;AACvD;AACA,eAAeL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}