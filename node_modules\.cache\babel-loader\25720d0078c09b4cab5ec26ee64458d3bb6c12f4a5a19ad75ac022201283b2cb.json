{"ast": null, "code": "export const version = \"5.17.1\";\nexport const major = Number(\"5\");\nexport const minor = Number(\"17\");\nexport const patch = Number(\"1\");\nexport const preReleaseLabel = undefined || null;\nexport const preReleaseNumber = Number(undefined) || null;\nexport default version;", "map": {"version": 3, "names": ["version", "major", "Number", "minor", "patch", "preReleaseLabel", "undefined", "preReleaseNumber"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/version/index.js"], "sourcesContent": ["export const version = \"5.17.1\";\nexport const major = Number(\"5\");\nexport const minor = Number(\"17\");\nexport const patch = Number(\"1\");\nexport const preReleaseLabel = undefined || null;\nexport const preReleaseNumber = Number(undefined) || null;\nexport default version;"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,QAAQ;AAC/B,OAAO,MAAMC,KAAK,GAAGC,MAAM,CAAC,GAAG,CAAC;AAChC,OAAO,MAAMC,KAAK,GAAGD,MAAM,CAAC,IAAI,CAAC;AACjC,OAAO,MAAME,KAAK,GAAGF,MAAM,CAAC,GAAG,CAAC;AAChC,OAAO,MAAMG,eAAe,GAAGC,SAAS,IAAI,IAAI;AAChD,OAAO,MAAMC,gBAAgB,GAAGL,MAAM,CAACI,SAAS,CAAC,IAAI,IAAI;AACzD,eAAeN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}