export interface PurchaseItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitCost: number;
  discount: number;
  total: number;
}

export interface Purchase {
  id: string;
  purchaseNumber: string;
  supplierId: string;
  supplierName: string;
  supplierPhone?: string;
  supplierEmail?: string;
  date: string;
  expectedDelivery?: string;
  items: PurchaseItem[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  paidAmount: number;
  remainingAmount: number;
  status: PurchaseStatus;
  paymentMethod: PaymentMethod;
  notes?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export type PurchaseStatus = 'draft' | 'ordered' | 'received' | 'cancelled' | 'partial';
export type PaymentMethod = 'cash' | 'card' | 'bank_transfer' | 'check' | 'credit';

export interface CreatePurchaseRequest {
  supplierId: string;
  items: Omit<PurchaseItem, 'id' | 'total'>[];
  discount?: number;
  paymentMethod: PaymentMethod;
  paidAmount?: number;
  expectedDelivery?: string;
  notes?: string;
}

export interface PurchasesFilters {
  status?: PurchaseStatus;
  paymentMethod?: PaymentMethod;
  supplierId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface PurchasesStats {
  totalPurchases: number;
  totalCost: number;
  pendingPurchases: number;
  receivedPurchases: number;
  todayPurchases: number;
  monthlyCost: number;
}

export const PURCHASE_STATUSES: { value: PurchaseStatus; label: string; color: 'success' | 'warning' | 'error' | 'default' | 'info' }[] = [
  { value: 'draft', label: 'مسودة', color: 'default' },
  { value: 'ordered', label: 'مطلوبة', color: 'warning' },
  { value: 'received', label: 'مستلمة', color: 'success' },
  { value: 'cancelled', label: 'ملغية', color: 'error' },
  { value: 'partial', label: 'جزئية', color: 'info' },
];

export const PURCHASE_PAYMENT_METHODS: { value: PaymentMethod; label: string }[] = [
  { value: 'cash', label: 'نقداً' },
  { value: 'card', label: 'بطاقة' },
  { value: 'bank_transfer', label: 'تحويل بنكي' },
  { value: 'check', label: 'شيك' },
  { value: 'credit', label: 'آجل' },
];
