import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  Alert,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Visibility as ViewIcon,
  Download as DownloadIcon
} from '@mui/icons-material';

// Types for Invoice
interface InvoiceItem {
  id: string;
  productName: string;
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate: number;
  subtotal: number;
  taxAmount: number;
  total: number;
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  date: string;
  dueDate: string;
  customerName: string;
  customerTaxId: string;
  customerAddress: string;
  items: InvoiceItem[];
  subtotal: number;
  totalTax: number;
  totalAmount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  notes: string;
}

// Sample data
const sampleInvoices: Invoice[] = [
  {
    id: '1',
    invoiceNumber: 'INV-202407-0001',
    date: '2024-07-05',
    dueDate: '2024-07-20',
    customerName: 'شركة التقنية المتقدمة',
    customerTaxId: '300123456789003',
    customerAddress: 'الرياض، المملكة العربية السعودية',
    items: [
      {
        id: '1',
        productName: 'لابتوب ديل XPS 13',
        description: 'لابتوب عالي الأداء للأعمال',
        quantity: 2,
        unitPrice: 4000,
        taxRate: 15,
        subtotal: 8000,
        taxAmount: 1200,
        total: 9200
      },
      {
        id: '2',
        productName: 'ماوس لوجيتك MX Master',
        description: 'ماوس لاسلكي متقدم',
        quantity: 2,
        unitPrice: 350,
        taxRate: 15,
        subtotal: 700,
        taxAmount: 105,
        total: 805
      }
    ],
    subtotal: 8700,
    totalTax: 1305,
    totalAmount: 10005,
    status: 'sent',
    notes: 'شكراً لتعاملكم معنا'
  }
];

const sampleProducts = [
  { id: '1', name: 'لابتوب ديل XPS 13', price: 4000, taxRate: 15 },
  { id: '2', name: 'ماوس لوجيتك MX Master', price: 350, taxRate: 15 },
  { id: '3', name: 'كيبورد لوجيتك MX Keys', price: 450, taxRate: 15 },
  { id: '4', name: 'شاشة سامسونج 27 بوصة', price: 1200, taxRate: 15 }
];

const sampleCustomers = [
  { id: '1', name: 'شركة التقنية المتقدمة', taxId: '300123456789003', address: 'الرياض، المملكة العربية السعودية' },
  { id: '2', name: 'مؤسسة الحلول الذكية', taxId: '300987654321003', address: 'جدة، المملكة العربية السعودية' },
  { id: '3', name: 'شركة الابتكار التقني', taxId: '300555666777003', address: 'الدمام، المملكة العربية السعودية' }
];

const InvoicesPage: React.FC = () => {
  const [invoices, setInvoices] = useState<Invoice[]>(sampleInvoices);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'create' | 'edit' | 'view'>('list');

  // Company information (should be from settings)
  const companyInfo = {
    name: 'شركة زين كود للتقنية',
    taxId: '300111222333003',
    address: 'الرياض، المملكة العربية السعودية',
    phone: '+966 11 123 4567',
    email: '<EMAIL>'
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'sent': return 'info';
      case 'overdue': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'draft': return 'مسودة';
      case 'sent': return 'مرسلة';
      case 'paid': return 'مدفوعة';
      case 'overdue': return 'متأخرة';
      default: return status;
    }
  };

  const handleCreateInvoice = () => {
    setSelectedInvoice(null);
    setViewMode('create');
  };

  const handleEditInvoice = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setViewMode('edit');
  };

  const handleViewInvoice = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setViewMode('view');
  };

  const handleBackToList = () => {
    setViewMode('list');
    setSelectedInvoice(null);
  };

  const renderInvoiceList = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
          إدارة الفواتير
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateInvoice}
          sx={{ borderRadius: 2 }}
        >
          إنشاء فاتورة جديدة
        </Button>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>متوافق مع المرحلة الثانية لضريبة القيمة المضافة</strong><br />
          جميع الفواتير تتضمن الرقم الضريبي وحساب ضريبة القيمة المضافة بنسبة 15%
        </Typography>
      </Alert>

      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell><strong>رقم الفاتورة</strong></TableCell>
              <TableCell><strong>العميل</strong></TableCell>
              <TableCell><strong>التاريخ</strong></TableCell>
              <TableCell><strong>تاريخ الاستحقاق</strong></TableCell>
              <TableCell><strong>المبلغ الإجمالي</strong></TableCell>
              <TableCell><strong>الحالة</strong></TableCell>
              <TableCell><strong>الإجراءات</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {invoices.map((invoice) => (
              <TableRow key={invoice.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight={600}>
                    {invoice.invoiceNumber}
                  </Typography>
                </TableCell>
                <TableCell>{invoice.customerName}</TableCell>
                <TableCell>{new Date(invoice.date).toLocaleDateString('ar-SA')}</TableCell>
                <TableCell>{new Date(invoice.dueDate).toLocaleDateString('ar-SA')}</TableCell>
                <TableCell>
                  <Typography variant="body2" fontWeight={600}>
                    {invoice.totalAmount.toLocaleString('ar-SA')} ر.س
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getStatusText(invoice.status)}
                    color={getStatusColor(invoice.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="عرض">
                      <IconButton
                        size="small"
                        onClick={() => handleViewInvoice(invoice)}
                        color="primary"
                      >
                        <ViewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="تعديل">
                      <IconButton
                        size="small"
                        onClick={() => handleEditInvoice(invoice)}
                        color="secondary"
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="طباعة">
                      <IconButton size="small" color="success">
                        <PrintIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="تحميل PDF">
                      <IconButton size="small" color="info">
                        <DownloadIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderInvoiceView = () => {
    if (!selectedInvoice) return null;

    return (
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Button onClick={handleBackToList} variant="outlined">
            العودة للقائمة
          </Button>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button variant="contained" startIcon={<PrintIcon />}>
              طباعة
            </Button>
            <Button variant="contained" startIcon={<DownloadIcon />}>
              تحميل PDF
            </Button>
          </Box>
        </Box>

        <Paper sx={{ p: 4, borderRadius: 2 }}>
          {/* Header */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Typography variant="h4" fontWeight={700} color="primary">
                {companyInfo.name}
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                {companyInfo.address}<br />
                هاتف: {companyInfo.phone}<br />
                البريد الإلكتروني: {companyInfo.email}<br />
                <strong>الرقم الضريبي: {companyInfo.taxId}</strong>
              </Typography>
            </Grid>
            <Grid item xs={12} md={6} sx={{ textAlign: 'right' }}>
              <Typography variant="h3" fontWeight={700} color="primary">
                فاتورة ضريبية
              </Typography>
              <Typography variant="h6" sx={{ mt: 2 }}>
                رقم الفاتورة: <strong>{selectedInvoice.invoiceNumber}</strong>
              </Typography>
              <Typography variant="body1">
                التاريخ: {new Date(selectedInvoice.date).toLocaleDateString('ar-SA')}
              </Typography>
              <Typography variant="body1">
                تاريخ الاستحقاق: {new Date(selectedInvoice.dueDate).toLocaleDateString('ar-SA')}
              </Typography>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          {/* Customer Info */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
                بيانات العميل:
              </Typography>
              <Typography variant="body1">
                <strong>{selectedInvoice.customerName}</strong><br />
                {selectedInvoice.customerAddress}<br />
                <strong>الرقم الضريبي: {selectedInvoice.customerTaxId}</strong>
              </Typography>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          {/* Items Table */}
          <TableContainer sx={{ mb: 4 }}>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                  <TableCell><strong>الصنف</strong></TableCell>
                  <TableCell align="center"><strong>الكمية</strong></TableCell>
                  <TableCell align="center"><strong>سعر الوحدة</strong></TableCell>
                  <TableCell align="center"><strong>المجموع الفرعي</strong></TableCell>
                  <TableCell align="center"><strong>ضريبة القيمة المضافة (15%)</strong></TableCell>
                  <TableCell align="center"><strong>الإجمالي</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {selectedInvoice.items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight={600}>
                        {item.productName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {item.description}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">{item.quantity}</TableCell>
                    <TableCell align="center">{item.unitPrice.toLocaleString('ar-SA')} ر.س</TableCell>
                    <TableCell align="center">{item.subtotal.toLocaleString('ar-SA')} ر.س</TableCell>
                    <TableCell align="center">{item.taxAmount.toLocaleString('ar-SA')} ر.س</TableCell>
                    <TableCell align="center">
                      <Typography fontWeight={600}>
                        {item.total.toLocaleString('ar-SA')} ر.س
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Totals */}
          <Grid container justifyContent="flex-end">
            <Grid item xs={12} md={4}>
              <Card sx={{ backgroundColor: '#f8f9fa' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>المجموع الفرعي:</Typography>
                    <Typography fontWeight={600}>
                      {selectedInvoice.subtotal.toLocaleString('ar-SA')} ر.س
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>ضريبة القيمة المضافة (15%):</Typography>
                    <Typography fontWeight={600}>
                      {selectedInvoice.totalTax.toLocaleString('ar-SA')} ر.س
                    </Typography>
                  </Box>
                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6" fontWeight={700}>
                      الإجمالي:
                    </Typography>
                    <Typography variant="h6" fontWeight={700} color="primary">
                      {selectedInvoice.totalAmount.toLocaleString('ar-SA')} ر.س
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Notes */}
          {selectedInvoice.notes && (
            <Box sx={{ mt: 4 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 1 }}>
                ملاحظات:
              </Typography>
              <Typography variant="body2">
                {selectedInvoice.notes}
              </Typography>
            </Box>
          )}

          {/* Footer */}
          <Box sx={{ mt: 4, pt: 3, borderTop: '1px solid #e0e0e0', textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              هذه فاتورة ضريبية صادرة إلكترونياً ومتوافقة مع لوائح هيئة الزكاة والضريبة والجمارك
            </Typography>
          </Box>
        </Paper>
      </Box>
    );
  };

  // Render based on view mode
  switch (viewMode) {
    case 'view':
      return renderInvoiceView();
    case 'create':
    case 'edit':
      return (
        <Box>
          <Button onClick={handleBackToList} variant="outlined" sx={{ mb: 3 }}>
            العودة للقائمة
          </Button>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h5" gutterBottom>
              {viewMode === 'create' ? 'إنشاء فاتورة جديدة' : 'تعديل الفاتورة'}
            </Typography>
            <Alert severity="info" sx={{ mb: 3 }}>
              نموذج إنشاء/تعديل الفاتورة قيد التطوير...
            </Alert>
          </Paper>
        </Box>
      );
    default:
      return renderInvoiceList();
  }
};

export default InvoicesPage;