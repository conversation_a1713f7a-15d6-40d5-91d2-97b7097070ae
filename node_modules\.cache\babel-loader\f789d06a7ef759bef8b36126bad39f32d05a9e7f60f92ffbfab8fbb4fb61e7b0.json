{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"theme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ThemeProvider as SystemThemeProvider } from '@mui/system';\nimport THEME_ID from './identifier';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProvider(_ref) {\n  let {\n      theme: themeInput\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const scopedTheme = themeInput[THEME_ID];\n  let finalTheme = scopedTheme || themeInput;\n  if (typeof themeInput !== 'function') {\n    if (scopedTheme && !scopedTheme.vars) {\n      finalTheme = _extends({}, scopedTheme, {\n        vars: null\n      });\n    } else if (themeInput && !themeInput.vars) {\n      finalTheme = _extends({}, themeInput, {\n        vars: null\n      });\n    }\n  }\n  return /*#__PURE__*/_jsx(SystemThemeProvider, _extends({}, props, {\n    themeId: scopedTheme ? THEME_ID : undefined,\n    theme: finalTheme\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * A theme object. You can provide a function to extend the outer theme.\n   */\n  theme: PropTypes.oneOfType([PropTypes.object, PropTypes.func]).isRequired\n} : void 0;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "ThemeProvider", "SystemThemeProvider", "THEME_ID", "jsx", "_jsx", "_ref", "theme", "themeInput", "props", "scopedTheme", "finalTheme", "vars", "themeId", "undefined", "process", "env", "NODE_ENV", "propTypes", "children", "node", "oneOfType", "object", "func", "isRequired"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/styles/ThemeProvider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"theme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ThemeProvider as SystemThemeProvider } from '@mui/system';\nimport THEME_ID from './identifier';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProvider(_ref) {\n  let {\n      theme: themeInput\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const scopedTheme = themeInput[THEME_ID];\n  let finalTheme = scopedTheme || themeInput;\n  if (typeof themeInput !== 'function') {\n    if (scopedTheme && !scopedTheme.vars) {\n      finalTheme = _extends({}, scopedTheme, {\n        vars: null\n      });\n    } else if (themeInput && !themeInput.vars) {\n      finalTheme = _extends({}, themeInput, {\n        vars: null\n      });\n    }\n  }\n  return /*#__PURE__*/_jsx(SystemThemeProvider, _extends({}, props, {\n    themeId: scopedTheme ? THEME_ID : undefined,\n    theme: finalTheme\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * A theme object. You can provide a function to extend the outer theme.\n   */\n  theme: PropTypes.oneOfType([PropTypes.object, PropTypes.func]).isRequired\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,CAAC;AAC3B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,aAAa,IAAIC,mBAAmB,QAAQ,aAAa;AAClE,OAAOC,QAAQ,MAAM,cAAc;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASJ,aAAaA,CAACK,IAAI,EAAE;EAC1C,IAAI;MACAC,KAAK,EAAEC;IACT,CAAC,GAAGF,IAAI;IACRG,KAAK,GAAGZ,6BAA6B,CAACS,IAAI,EAAER,SAAS,CAAC;EACxD,MAAMY,WAAW,GAAGF,UAAU,CAACL,QAAQ,CAAC;EACxC,IAAIQ,UAAU,GAAGD,WAAW,IAAIF,UAAU;EAC1C,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;IACpC,IAAIE,WAAW,IAAI,CAACA,WAAW,CAACE,IAAI,EAAE;MACpCD,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAEc,WAAW,EAAE;QACrCE,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIJ,UAAU,IAAI,CAACA,UAAU,CAACI,IAAI,EAAE;MACzCD,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAEY,UAAU,EAAE;QACpCI,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF;EACA,OAAO,aAAaP,IAAI,CAACH,mBAAmB,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEa,KAAK,EAAE;IAChEI,OAAO,EAAEH,WAAW,GAAGP,QAAQ,GAAGW,SAAS;IAC3CP,KAAK,EAAEI;EACT,CAAC,CAAC,CAAC;AACL;AACAI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhB,aAAa,CAACiB,SAAS,GAAG;EAChE;AACF;AACA;EACEC,QAAQ,EAAEnB,SAAS,CAACoB,IAAI;EACxB;AACF;AACA;EACEb,KAAK,EAAEP,SAAS,CAACqB,SAAS,CAAC,CAACrB,SAAS,CAACsB,MAAM,EAAEtB,SAAS,CAACuB,IAAI,CAAC,CAAC,CAACC;AACjE,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}