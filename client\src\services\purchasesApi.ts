import axios from 'axios';
import { Purchase, CreatePurchaseRequest, PurchasesFilters, PurchasesStats } from '../types/purchases';
import { ApiResponse } from '../types/common';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const purchasesApi = {
  // Get all purchases
  getPurchases: async (filters?: PurchasesFilters): Promise<ApiResponse<Purchase[]>> => {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.paymentMethod) params.append('paymentMethod', filters.paymentMethod);
    if (filters?.supplierId) params.append('supplierId', filters.supplierId);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`/purchases?${params.toString()}`);
    return response.data;
  },

  // Get purchase by ID
  getPurchase: async (id: string): Promise<ApiResponse<Purchase>> => {
    const response = await api.get(`/purchases/${id}`);
    return response.data;
  },

  // Create new purchase
  createPurchase: async (purchase: CreatePurchaseRequest): Promise<ApiResponse<Purchase>> => {
    const response = await api.post('/purchases', purchase);
    return response.data;
  },

  // Update purchase
  updatePurchase: async (id: string, purchase: Partial<Purchase>): Promise<ApiResponse<Purchase>> => {
    const response = await api.put(`/purchases/${id}`, purchase);
    return response.data;
  },

  // Delete purchase
  deletePurchase: async (id: string): Promise<ApiResponse<void>> => {
    const response = await api.delete(`/purchases/${id}`);
    return response.data;
  },

  // Get purchases statistics
  getPurchasesStats: async (): Promise<ApiResponse<PurchasesStats>> => {
    const response = await api.get('/purchases/stats');
    return response.data;
  },

  // Mark purchase as received
  markAsReceived: async (id: string, receivedItems?: { productId: string; quantity: number }[]): Promise<ApiResponse<Purchase>> => {
    const response = await api.post(`/purchases/${id}/receive`, { receivedItems });
    return response.data;
  },

  // Print purchase order
  printPurchaseOrder: async (id: string): Promise<Blob> => {
    const response = await api.get(`/purchases/${id}/print`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Export purchases to Excel
  exportPurchases: async (filters?: PurchasesFilters): Promise<Blob> => {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);

    const response = await api.get(`/purchases/export?${params.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Cancel purchase
  cancelPurchase: async (id: string, reason?: string): Promise<ApiResponse<Purchase>> => {
    const response = await api.post(`/purchases/${id}/cancel`, { reason });
    return response.data;
  },
};
