{"ast": null, "code": "'use client';\n\nexport { default } from './DialogActions';\nexport { default as dialogActionsClasses } from './dialogActionsClasses';\nexport * from './dialogActionsClasses';", "map": {"version": 3, "names": ["default", "dialogActionsClasses"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/DialogActions/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './DialogActions';\nexport { default as dialogActionsClasses } from './dialogActionsClasses';\nexport * from './dialogActionsClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,oBAAoB,QAAQ,wBAAwB;AACxE,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}