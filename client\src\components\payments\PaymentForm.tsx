import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Box,
  Typography,
  Divider,
} from '@mui/material';
import { Save, Cancel } from '@mui/icons-material';
import {
  Payment,
  CreatePaymentRequest,
  PAYMENT_METHODS,
  PAYMENT_TYPES,
} from '../../types/payments';

interface PaymentFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (payment: CreatePaymentRequest) => void;
  payment?: Payment;
  title?: string;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  open,
  onClose,
  onSubmit,
  payment,
  title = 'إضافة دفعة جديدة',
}) => {
  const [formData, setFormData] = useState<CreatePaymentRequest>({
    amount: payment?.amount || 0,
    paymentMethod: payment?.paymentMethod || 'cash',
    paymentDate: payment?.paymentDate || new Date().toISOString().split('T')[0],
    type: payment?.type || 'incoming',
    customerId: payment?.customerId || '',
    supplierId: payment?.supplierId || '',
    invoiceId: payment?.invoiceId || '',
    reference: payment?.reference || '',
    notes: payment?.notes || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (field: keyof CreatePaymentRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'المبلغ مطلوب ويجب أن يكون أكبر من صفر';
    }

    if (!formData.paymentMethod) {
      newErrors.paymentMethod = 'طريقة الدفع مطلوبة';
    }

    if (!formData.paymentDate) {
      newErrors.paymentDate = 'تاريخ الدفع مطلوب';
    }

    if (!formData.type) {
      newErrors.type = 'نوع الدفعة مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSubmit(formData);
      onClose();
    }
  };

  const handleClose = () => {
    setFormData({
      amount: 0,
      paymentMethod: 'cash',
      paymentDate: new Date().toISOString().split('T')[0],
      type: 'incoming',
      customerId: '',
      supplierId: '',
      invoiceId: '',
      reference: '',
      notes: '',
    });
    setErrors({});
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6" fontWeight={600}>
          {title}
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Grid container spacing={3}>
            {/* Payment Type */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.type}>
                <InputLabel>نوع الدفعة</InputLabel>
                <Select
                  value={formData.type}
                  label="نوع الدفعة"
                  onChange={(e) => handleChange('type', e.target.value)}
                >
                  {PAYMENT_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
                {errors.type && (
                  <Typography variant="caption" color="error">
                    {errors.type}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            {/* Amount */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="المبلغ"
                type="number"
                value={formData.amount}
                onChange={(e) => handleChange('amount', parseFloat(e.target.value) || 0)}
                error={!!errors.amount}
                helperText={errors.amount}
                InputProps={{
                  endAdornment: <Typography variant="body2">ر.س</Typography>,
                }}
              />
            </Grid>

            {/* Payment Method */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.paymentMethod}>
                <InputLabel>طريقة الدفع</InputLabel>
                <Select
                  value={formData.paymentMethod}
                  label="طريقة الدفع"
                  onChange={(e) => handleChange('paymentMethod', e.target.value)}
                >
                  {PAYMENT_METHODS.map((method) => (
                    <MenuItem key={method.value} value={method.value}>
                      {method.label}
                    </MenuItem>
                  ))}
                </Select>
                {errors.paymentMethod && (
                  <Typography variant="caption" color="error">
                    {errors.paymentMethod}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            {/* Payment Date */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="تاريخ الدفع"
                type="date"
                value={formData.paymentDate}
                onChange={(e) => handleChange('paymentDate', e.target.value)}
                error={!!errors.paymentDate}
                helperText={errors.paymentDate}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
            </Grid>

            {/* Customer/Supplier Info */}
            {formData.type === 'incoming' ? (
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="معرف العميل"
                  value={formData.customerId}
                  onChange={(e) => handleChange('customerId', e.target.value)}
                  placeholder="اختياري"
                />
              </Grid>
            ) : (
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="معرف المورد"
                  value={formData.supplierId}
                  onChange={(e) => handleChange('supplierId', e.target.value)}
                  placeholder="اختياري"
                />
              </Grid>
            )}

            {/* Invoice ID */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="رقم الفاتورة"
                value={formData.invoiceId}
                onChange={(e) => handleChange('invoiceId', e.target.value)}
                placeholder="اختياري"
              />
            </Grid>

            {/* Reference */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="رقم المرجع"
                value={formData.reference}
                onChange={(e) => handleChange('reference', e.target.value)}
                placeholder="رقم الشيك، رقم التحويل، إلخ"
              />
            </Grid>

            {/* Notes */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="ملاحظات"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => handleChange('notes', e.target.value)}
                placeholder="ملاحظات إضافية..."
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button
          onClick={handleClose}
          startIcon={<Cancel />}
          color="inherit"
        >
          إلغاء
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          startIcon={<Save />}
        >
          حفظ
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentForm;
