{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSnackbarContentUtilityClass(slot) {\n  return generateUtilityClass('MuiSnackbarContent', slot);\n}\nconst snackbarContentClasses = generateUtilityClasses('MuiSnackbarContent', ['root', 'message', 'action']);\nexport default snackbarContentClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getSnackbarContentUtilityClass", "slot", "snackbarContentClasses"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/SnackbarContent/snackbarContentClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSnackbarContentUtilityClass(slot) {\n  return generateUtilityClass('MuiSnackbarContent', slot);\n}\nconst snackbarContentClasses = generateUtilityClasses('MuiSnackbarContent', ['root', 'message', 'action']);\nexport default snackbarContentClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,8BAA8BA,CAACC,IAAI,EAAE;EACnD,OAAOF,oBAAoB,CAAC,oBAAoB,EAAEE,IAAI,CAAC;AACzD;AACA,MAAMC,sBAAsB,GAAGJ,sBAAsB,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC1G,eAAeI,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}