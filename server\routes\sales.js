const express = require('express');
const router = express.Router();

// Mock data for sales
let sales = [
  {
    id: '1',
    invoiceNumber: 'INV-2024-001',
    customerId: '1',
    customerName: 'أحمد محمد',
    customerPhone: '+966501234567',
    customerEmail: '<EMAIL>',
    date: '2024-01-15',
    dueDate: '2024-02-15',
    items: [
      {
        id: '1',
        productId: '1',
        productName: 'منتج أ',
        quantity: 2,
        unitPrice: 7500,
        discount: 0,
        total: 15000
      }
    ],
    subtotal: 15000,
    discount: 0,
    tax: 2250,
    total: 17250,
    paidAmount: 17250,
    remainingAmount: 0,
    status: 'completed',
    paymentMethod: 'cash',
    notes: '',
    createdBy: 'admin',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    invoiceNumber: 'INV-2024-002',
    customerId: '2',
    customerName: 'فاطمة علي',
    customerPhone: '+966507654321',
    customerEmail: '<EMAIL>',
    date: '2024-01-16',
    dueDate: '2024-02-16',
    items: [
      {
        id: '2',
        productId: '2',
        productName: 'منتج ب',
        quantity: 1,
        unitPrice: 8500,
        discount: 500,
        total: 8000
      }
    ],
    subtotal: 8000,
    discount: 500,
    tax: 1200,
    total: 8700,
    paidAmount: 5000,
    remainingAmount: 3700,
    status: 'pending',
    paymentMethod: 'card',
    notes: 'دفعة جزئية',
    createdBy: 'admin',
    createdAt: '2024-01-16T11:00:00Z',
    updatedAt: '2024-01-16T11:00:00Z'
  }
];

// GET /api/sales - Get all sales
router.get('/', (req, res) => {
  try {
    const { status, paymentMethod, customerId, dateFrom, dateTo, search } = req.query;

    let filteredSales = [...sales];

    // Apply filters
    if (status) {
      filteredSales = filteredSales.filter(sale => sale.status === status);
    }

    if (paymentMethod) {
      filteredSales = filteredSales.filter(sale => sale.paymentMethod === paymentMethod);
    }

    if (customerId) {
      filteredSales = filteredSales.filter(sale => sale.customerId === customerId);
    }

    if (dateFrom) {
      filteredSales = filteredSales.filter(sale => new Date(sale.date) >= new Date(dateFrom));
    }

    if (dateTo) {
      filteredSales = filteredSales.filter(sale => new Date(sale.date) <= new Date(dateTo));
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredSales = filteredSales.filter(sale =>
        sale.invoiceNumber.toLowerCase().includes(searchLower) ||
        sale.customerName.toLowerCase().includes(searchLower)
      );
    }

    res.json({
      success: true,
      message: 'Sales retrieved successfully',
      data: filteredSales
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving sales',
      data: null
    });
  }
});

// GET /api/sales/stats - Get sales statistics
router.get('/stats', (req, res) => {
  try {
    const totalSales = sales.length;
    const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);
    const pendingSales = sales.filter(sale => sale.status === 'pending').length;
    const completedSales = sales.filter(sale => sale.status === 'completed').length;

    const today = new Date().toISOString().split('T')[0];
    const todaySales = sales.filter(sale => sale.date === today).length;

    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const monthlyRevenue = sales
      .filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate.getMonth() === currentMonth && saleDate.getFullYear() === currentYear;
      })
      .reduce((sum, sale) => sum + sale.total, 0);

    res.json({
      success: true,
      message: 'Sales statistics retrieved successfully',
      data: {
        totalSales,
        totalRevenue,
        pendingSales,
        completedSales,
        todaySales,
        monthlyRevenue
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving sales statistics',
      data: null
    });
  }
});

// GET /api/sales/:id - Get sale by ID
router.get('/:id', (req, res) => {
  try {
    const sale = sales.find(s => s.id === req.params.id);

    if (!sale) {
      return res.status(404).json({
        success: false,
        message: 'Sale not found',
        data: null
      });
    }

    res.json({
      success: true,
      message: 'Sale retrieved successfully',
      data: sale
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving sale',
      data: null
    });
  }
});

// POST /api/sales - Create new sale
router.post('/', (req, res) => {
  try {
    const { customerId, items, discount = 0, paymentMethod, paidAmount = 0, notes = '' } = req.body;

    // Validate required fields
    if (!customerId || !items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID and items are required',
        data: null
      });
    }

    // Calculate totals
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice - item.discount), 0);
    const tax = subtotal * 0.15; // 15% tax
    const total = subtotal - discount + tax;

    // Generate new sale
    const newSale = {
      id: (sales.length + 1).toString(),
      invoiceNumber: `INV-2024-${String(sales.length + 1).padStart(3, '0')}`,
      customerId,
      customerName: 'عميل جديد', // In real app, fetch from customers table
      date: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      items: items.map((item, index) => ({
        ...item,
        id: (index + 1).toString(),
        total: item.quantity * item.unitPrice - item.discount
      })),
      subtotal,
      discount,
      tax,
      total,
      paidAmount,
      remainingAmount: total - paidAmount,
      status: paidAmount >= total ? 'completed' : paidAmount > 0 ? 'pending' : 'draft',
      paymentMethod,
      notes,
      createdBy: 'admin',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    sales.push(newSale);

    res.status(201).json({
      success: true,
      message: 'Sale created successfully',
      data: newSale
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error creating sale',
      data: null
    });
  }
});

module.exports = router;