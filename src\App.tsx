import React, { useState } from 'react';
import {
  Box,
  AppBar,
  Too<PERSON>bar,
  Typography,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CssBaseline,
  Container,
  Grid,
  Card,
  CardContent,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  AccountBalance,
  People,
  Business,
  Inventory,
  ShoppingCart,
  Receipt,
  Payment,
  Assessment,
  Settings,

  Menu as MenuIcon,
  AccountCircle
} from '@mui/icons-material';

const drawerWidth = 240;

// Simple Dashboard Component
const Dashboard: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h4" gutterBottom>
            لوحة التحكم الرئيسية
          </Typography>
        </Grid>

        {/* Stats Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                إجمالي المبيعات
              </Typography>
              <Typography variant="h5" component="div">
                ₪ 125,000
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                العملاء
              </Typography>
              <Typography variant="h5" component="div">
                342
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                المنتجات
              </Typography>
              <Typography variant="h5" component="div">
                1,234
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                الطلبات المعلقة
              </Typography>
              <Typography variant="h5" component="div">
                23
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                النشاط الأخير
              </Typography>
              <Typography variant="body2">
                • تم إضافة عميل جديد: أحمد محمد
              </Typography>
              <Typography variant="body2">
                • تم إنشاء فاتورة جديدة #1234
              </Typography>
              <Typography variant="body2">
                • تم استلام دفعة بقيمة ₪ 5,000
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

// Simple Page Component
const SimplePage: React.FC<{ title: string; description: string }> = ({ title, description }) => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Card>
        <CardContent sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom color="primary">
            {title}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {description}
          </Typography>
          <Typography variant="body2" sx={{ mt: 2 }}>
            هذا القسم قيد التطوير وسيتم إضافة المزيد من الميزات قريباً
          </Typography>
        </CardContent>
      </Card>
    </Container>
  );
};

const App: React.FC = () => {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const menuItems = [
    { id: 'dashboard', label: 'لوحة التحكم', icon: <DashboardIcon /> },
    { id: 'accounts', label: 'الحسابات', icon: <AccountBalance /> },
    { id: 'customers', label: 'العملاء', icon: <People /> },
    { id: 'suppliers', label: 'الموردين', icon: <Business /> },
    { id: 'products', label: 'المنتجات', icon: <Inventory /> },
    { id: 'inventory', label: 'المخزون', icon: <Inventory /> },
    { id: 'sales', label: 'المبيعات', icon: <ShoppingCart /> },
    { id: 'purchases', label: 'المشتريات', icon: <ShoppingCart /> },
    { id: 'invoices', label: 'الفواتير', icon: <Receipt /> },
    { id: 'payments', label: 'المدفوعات', icon: <Payment /> },
    { id: 'reports', label: 'التقارير', icon: <Assessment /> },
    { id: 'settings', label: 'الإعدادات', icon: <Settings /> },
  ];

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'accounts':
        return <SimplePage title="إدارة الحسابات" description="إدارة حسابات الشركة والأرصدة المالية" />;
      case 'customers':
        return <SimplePage title="إدارة العملاء" description="إدارة بيانات العملاء والتواصل معهم" />;
      case 'suppliers':
        return <SimplePage title="إدارة الموردين" description="إدارة بيانات الموردين والتعاملات التجارية" />;
      case 'products':
        return <SimplePage title="إدارة المنتجات" description="إدارة كتالوج المنتجات والأسعار" />;
      case 'inventory':
        return <SimplePage title="إدارة المخزون" description="متابعة المخزون والكميات المتاحة" />;
      case 'sales':
        return <SimplePage title="إدارة المبيعات" description="إدارة عمليات البيع والطلبات" />;
      case 'purchases':
        return <SimplePage title="إدارة المشتريات" description="إدارة عمليات الشراء من الموردين" />;
      case 'invoices':
        return <SimplePage title="إدارة الفواتير" description="إنشاء وإدارة الفواتير" />;
      case 'payments':
        return <SimplePage title="إدارة المدفوعات" description="متابعة المدفوعات والمستحقات" />;
      case 'reports':
        return <SimplePage title="التقارير" description="تقارير مالية وإحصائيات مفصلة" />;
      case 'settings':
        return <SimplePage title="الإعدادات" description="إعدادات النظام والتخصيص" />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />

      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            نظام زين كود المحاسبي
          </Typography>
          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenuClick}
            color="inherit"
          >
            <AccountCircle />
          </IconButton>
          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleMenuClose}>الملف الشخصي</MenuItem>
            <MenuItem onClick={handleMenuClose}>تسجيل الخروج</MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          [`& .MuiDrawer-paper`]: { width: drawerWidth, boxSizing: 'border-box' },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            {menuItems.map((item) => (
              <ListItem
                button
                key={item.id}
                selected={currentPage === item.id}
                onClick={() => setCurrentPage(item.id)}
              >
                <ListItemIcon>{item.icon}</ListItemIcon>
                <ListItemText primary={item.label} />
              </ListItem>
            ))}
          </List>
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />
        {renderPage()}
      </Box>
    </Box>
  );
};

export default App;
