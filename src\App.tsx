import React from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';

const App: React.FC = () => {
  return (
    <Box sx={{ p: 4, minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <Typography variant="h3" gutterBottom sx={{ textAlign: 'center', mb: 4 }}>
        نظام زين كود المحاسبي
      </Typography>
      
      <Card sx={{ maxWidth: 600, mx: 'auto' }}>
        <CardContent sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom color="primary">
            مرحباً بك في نظام زين كود
          </Typography>
          <Typography variant="body1" color="text.secondary">
            النظام يعمل بنجاح! 🎉
          </Typography>
          <Typography variant="body2" sx={{ mt: 2 }}>
            تم حل مشكلة التحميل وأصبح النظام جاهزاً للاستخدام
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default App;
