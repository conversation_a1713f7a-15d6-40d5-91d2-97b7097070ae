{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"fontFamily\", \"fontSize\", \"fontWeightLight\", \"fontWeightRegular\", \"fontWeightMedium\", \"fontWeightBold\", \"htmlFontSize\", \"allVariants\", \"pxToRem\"];\nimport deepmerge from '@mui/utils/deepmerge';\nfunction round(value) {\n  return Math.round(value * 1e5) / 1e5;\n}\nconst caseAllCaps = {\n  textTransform: 'uppercase'\n};\nconst defaultFontFamily = '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif';\n\n/**\n * @see @link{https://m2.material.io/design/typography/the-type-system.html}\n * @see @link{https://m2.material.io/design/typography/understanding-typography.html}\n */\nexport default function createTypography(palette, typography) {\n  const _ref = typeof typography === 'function' ? typography(palette) : typography,\n    {\n      fontFamily = defaultFontFamily,\n      // The default font size of the Material Specification.\n      fontSize = 14,\n      // px\n      fontWeightLight = 300,\n      fontWeightRegular = 400,\n      fontWeightMedium = 500,\n      fontWeightBold = 700,\n      // Tell MUI what's the font-size on the html element.\n      // 16px is the default font-size used by browsers.\n      htmlFontSize = 16,\n      // Apply the CSS properties to all the variants.\n      allVariants,\n      pxToRem: pxToRem2\n    } = _ref,\n    other = _objectWithoutPropertiesLoose(_ref, _excluded);\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof fontSize !== 'number') {\n      console.error('MUI: `fontSize` is required to be a number.');\n    }\n    if (typeof htmlFontSize !== 'number') {\n      console.error('MUI: `htmlFontSize` is required to be a number.');\n    }\n  }\n  const coef = fontSize / 14;\n  const pxToRem = pxToRem2 || (size => `${size / htmlFontSize * coef}rem`);\n  const buildVariant = (fontWeight, size, lineHeight, letterSpacing, casing) => _extends({\n    fontFamily,\n    fontWeight,\n    fontSize: pxToRem(size),\n    // Unitless following https://meyerweb.com/eric/thoughts/2006/02/08/unitless-line-heights/\n    lineHeight\n  }, fontFamily === defaultFontFamily ? {\n    letterSpacing: `${round(letterSpacing / size)}em`\n  } : {}, casing, allVariants);\n  const variants = {\n    h1: buildVariant(fontWeightLight, 96, 1.167, -1.5),\n    h2: buildVariant(fontWeightLight, 60, 1.2, -0.5),\n    h3: buildVariant(fontWeightRegular, 48, 1.167, 0),\n    h4: buildVariant(fontWeightRegular, 34, 1.235, 0.25),\n    h5: buildVariant(fontWeightRegular, 24, 1.334, 0),\n    h6: buildVariant(fontWeightMedium, 20, 1.6, 0.15),\n    subtitle1: buildVariant(fontWeightRegular, 16, 1.75, 0.15),\n    subtitle2: buildVariant(fontWeightMedium, 14, 1.57, 0.1),\n    body1: buildVariant(fontWeightRegular, 16, 1.5, 0.15),\n    body2: buildVariant(fontWeightRegular, 14, 1.43, 0.15),\n    button: buildVariant(fontWeightMedium, 14, 1.75, 0.4, caseAllCaps),\n    caption: buildVariant(fontWeightRegular, 12, 1.66, 0.4),\n    overline: buildVariant(fontWeightRegular, 12, 2.66, 1, caseAllCaps),\n    // TODO v6: Remove handling of 'inherit' variant from the theme as it is already handled in Material UI's Typography component. Also, remember to remove the associated types.\n    inherit: {\n      fontFamily: 'inherit',\n      fontWeight: 'inherit',\n      fontSize: 'inherit',\n      lineHeight: 'inherit',\n      letterSpacing: 'inherit'\n    }\n  };\n  return deepmerge(_extends({\n    htmlFontSize,\n    pxToRem,\n    fontFamily,\n    fontSize,\n    fontWeightLight,\n    fontWeightRegular,\n    fontWeightMedium,\n    fontWeightBold\n  }, variants), other, {\n    clone: false // No need to clone deep\n  });\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "deepmerge", "round", "value", "Math", "caseAllCaps", "textTransform", "defaultFontFamily", "createTypography", "palette", "typography", "_ref", "fontFamily", "fontSize", "fontWeightLight", "fontWeightRegular", "fontWeightMedium", "fontWeightBold", "htmlFontSize", "allVariants", "pxToRem", "pxToRem2", "other", "process", "env", "NODE_ENV", "console", "error", "coef", "size", "buildVariant", "fontWeight", "lineHeight", "letterSpacing", "casing", "variants", "h1", "h2", "h3", "h4", "h5", "h6", "subtitle1", "subtitle2", "body1", "body2", "button", "caption", "overline", "inherit", "clone"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/styles/createTypography.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"fontFamily\", \"fontSize\", \"fontWeightLight\", \"fontWeightRegular\", \"fontWeightMedium\", \"fontWeightBold\", \"htmlFontSize\", \"allVariants\", \"pxToRem\"];\nimport deepmerge from '@mui/utils/deepmerge';\nfunction round(value) {\n  return Math.round(value * 1e5) / 1e5;\n}\nconst caseAllCaps = {\n  textTransform: 'uppercase'\n};\nconst defaultFontFamily = '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif';\n\n/**\n * @see @link{https://m2.material.io/design/typography/the-type-system.html}\n * @see @link{https://m2.material.io/design/typography/understanding-typography.html}\n */\nexport default function createTypography(palette, typography) {\n  const _ref = typeof typography === 'function' ? typography(palette) : typography,\n    {\n      fontFamily = defaultFontFamily,\n      // The default font size of the Material Specification.\n      fontSize = 14,\n      // px\n      fontWeightLight = 300,\n      fontWeightRegular = 400,\n      fontWeightMedium = 500,\n      fontWeightBold = 700,\n      // Tell MUI what's the font-size on the html element.\n      // 16px is the default font-size used by browsers.\n      htmlFontSize = 16,\n      // Apply the CSS properties to all the variants.\n      allVariants,\n      pxToRem: pxToRem2\n    } = _ref,\n    other = _objectWithoutPropertiesLoose(_ref, _excluded);\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof fontSize !== 'number') {\n      console.error('MUI: `fontSize` is required to be a number.');\n    }\n    if (typeof htmlFontSize !== 'number') {\n      console.error('MUI: `htmlFontSize` is required to be a number.');\n    }\n  }\n  const coef = fontSize / 14;\n  const pxToRem = pxToRem2 || (size => `${size / htmlFontSize * coef}rem`);\n  const buildVariant = (fontWeight, size, lineHeight, letterSpacing, casing) => _extends({\n    fontFamily,\n    fontWeight,\n    fontSize: pxToRem(size),\n    // Unitless following https://meyerweb.com/eric/thoughts/2006/02/08/unitless-line-heights/\n    lineHeight\n  }, fontFamily === defaultFontFamily ? {\n    letterSpacing: `${round(letterSpacing / size)}em`\n  } : {}, casing, allVariants);\n  const variants = {\n    h1: buildVariant(fontWeightLight, 96, 1.167, -1.5),\n    h2: buildVariant(fontWeightLight, 60, 1.2, -0.5),\n    h3: buildVariant(fontWeightRegular, 48, 1.167, 0),\n    h4: buildVariant(fontWeightRegular, 34, 1.235, 0.25),\n    h5: buildVariant(fontWeightRegular, 24, 1.334, 0),\n    h6: buildVariant(fontWeightMedium, 20, 1.6, 0.15),\n    subtitle1: buildVariant(fontWeightRegular, 16, 1.75, 0.15),\n    subtitle2: buildVariant(fontWeightMedium, 14, 1.57, 0.1),\n    body1: buildVariant(fontWeightRegular, 16, 1.5, 0.15),\n    body2: buildVariant(fontWeightRegular, 14, 1.43, 0.15),\n    button: buildVariant(fontWeightMedium, 14, 1.75, 0.4, caseAllCaps),\n    caption: buildVariant(fontWeightRegular, 12, 1.66, 0.4),\n    overline: buildVariant(fontWeightRegular, 12, 2.66, 1, caseAllCaps),\n    // TODO v6: Remove handling of 'inherit' variant from the theme as it is already handled in Material UI's Typography component. Also, remember to remove the associated types.\n    inherit: {\n      fontFamily: 'inherit',\n      fontWeight: 'inherit',\n      fontSize: 'inherit',\n      lineHeight: 'inherit',\n      letterSpacing: 'inherit'\n    }\n  };\n  return deepmerge(_extends({\n    htmlFontSize,\n    pxToRem,\n    fontFamily,\n    fontSize,\n    fontWeightLight,\n    fontWeightRegular,\n    fontWeightMedium,\n    fontWeightBold\n  }, variants), other, {\n    clone: false // No need to clone deep\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,CAAC;AACpK,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,OAAOC,IAAI,CAACF,KAAK,CAACC,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;AACtC;AACA,MAAME,WAAW,GAAG;EAClBC,aAAa,EAAE;AACjB,CAAC;AACD,MAAMC,iBAAiB,GAAG,4CAA4C;;AAEtE;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,UAAU,EAAE;EAC5D,MAAMC,IAAI,GAAG,OAAOD,UAAU,KAAK,UAAU,GAAGA,UAAU,CAACD,OAAO,CAAC,GAAGC,UAAU;IAC9E;MACEE,UAAU,GAAGL,iBAAiB;MAC9B;MACAM,QAAQ,GAAG,EAAE;MACb;MACAC,eAAe,GAAG,GAAG;MACrBC,iBAAiB,GAAG,GAAG;MACvBC,gBAAgB,GAAG,GAAG;MACtBC,cAAc,GAAG,GAAG;MACpB;MACA;MACAC,YAAY,GAAG,EAAE;MACjB;MACAC,WAAW;MACXC,OAAO,EAAEC;IACX,CAAC,GAAGV,IAAI;IACRW,KAAK,GAAGvB,6BAA6B,CAACY,IAAI,EAAEX,SAAS,CAAC;EACxD,IAAIuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,OAAOZ,QAAQ,KAAK,QAAQ,EAAE;MAChCa,OAAO,CAACC,KAAK,CAAC,6CAA6C,CAAC;IAC9D;IACA,IAAI,OAAOT,YAAY,KAAK,QAAQ,EAAE;MACpCQ,OAAO,CAACC,KAAK,CAAC,iDAAiD,CAAC;IAClE;EACF;EACA,MAAMC,IAAI,GAAGf,QAAQ,GAAG,EAAE;EAC1B,MAAMO,OAAO,GAAGC,QAAQ,KAAKQ,IAAI,IAAI,GAAGA,IAAI,GAAGX,YAAY,GAAGU,IAAI,KAAK,CAAC;EACxE,MAAME,YAAY,GAAGA,CAACC,UAAU,EAAEF,IAAI,EAAEG,UAAU,EAAEC,aAAa,EAAEC,MAAM,KAAKpC,QAAQ,CAAC;IACrFc,UAAU;IACVmB,UAAU;IACVlB,QAAQ,EAAEO,OAAO,CAACS,IAAI,CAAC;IACvB;IACAG;EACF,CAAC,EAAEpB,UAAU,KAAKL,iBAAiB,GAAG;IACpC0B,aAAa,EAAE,GAAG/B,KAAK,CAAC+B,aAAa,GAAGJ,IAAI,CAAC;EAC/C,CAAC,GAAG,CAAC,CAAC,EAAEK,MAAM,EAAEf,WAAW,CAAC;EAC5B,MAAMgB,QAAQ,GAAG;IACfC,EAAE,EAAEN,YAAY,CAAChB,eAAe,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;IAClDuB,EAAE,EAAEP,YAAY,CAAChB,eAAe,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;IAChDwB,EAAE,EAAER,YAAY,CAACf,iBAAiB,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;IACjDwB,EAAE,EAAET,YAAY,CAACf,iBAAiB,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC;IACpDyB,EAAE,EAAEV,YAAY,CAACf,iBAAiB,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;IACjD0B,EAAE,EAAEX,YAAY,CAACd,gBAAgB,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC;IACjD0B,SAAS,EAAEZ,YAAY,CAACf,iBAAiB,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1D4B,SAAS,EAAEb,YAAY,CAACd,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC;IACxD4B,KAAK,EAAEd,YAAY,CAACf,iBAAiB,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC;IACrD8B,KAAK,EAAEf,YAAY,CAACf,iBAAiB,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;IACtD+B,MAAM,EAAEhB,YAAY,CAACd,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,EAAEX,WAAW,CAAC;IAClE0C,OAAO,EAAEjB,YAAY,CAACf,iBAAiB,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC;IACvDiC,QAAQ,EAAElB,YAAY,CAACf,iBAAiB,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAEV,WAAW,CAAC;IACnE;IACA4C,OAAO,EAAE;MACPrC,UAAU,EAAE,SAAS;MACrBmB,UAAU,EAAE,SAAS;MACrBlB,QAAQ,EAAE,SAAS;MACnBmB,UAAU,EAAE,SAAS;MACrBC,aAAa,EAAE;IACjB;EACF,CAAC;EACD,OAAOhC,SAAS,CAACH,QAAQ,CAAC;IACxBoB,YAAY;IACZE,OAAO;IACPR,UAAU;IACVC,QAAQ;IACRC,eAAe;IACfC,iBAAiB;IACjBC,gBAAgB;IAChBC;EACF,CAAC,EAAEkB,QAAQ,CAAC,EAAEb,KAAK,EAAE;IACnB4B,KAAK,EAAE,KAAK,CAAC;EACf,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}