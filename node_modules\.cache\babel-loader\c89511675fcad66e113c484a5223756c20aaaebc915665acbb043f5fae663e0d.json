{"ast": null, "code": "'use client';\n\n// do not remove the following import (https://github.com/microsoft/TypeScript/issues/29808#issuecomment-**********)\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// @ts-ignore\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { unstable_createCssVarsProvider as createCssVarsProvider } from '@mui/system';\nimport styleFunctionSx from '@mui/system/styleFunctionSx';\nimport experimental_extendTheme from './experimental_extendTheme';\nimport createTypography from './createTypography';\nimport excludeVariablesFromRoot from './excludeVariablesFromRoot';\nimport THEME_ID from './identifier';\nimport { defaultConfig } from '../InitColorSchemeScript/InitColorSchemeScript';\nconst defaultTheme = experimental_extendTheme();\nconst {\n  CssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript: getInitColorSchemeScriptSystem\n} = createCssVarsProvider({\n  themeId: THEME_ID,\n  theme: defaultTheme,\n  attribute: defaultConfig.attribute,\n  colorSchemeStorageKey: defaultConfig.colorSchemeStorageKey,\n  modeStorageKey: defaultConfig.modeStorageKey,\n  defaultColorScheme: {\n    light: defaultConfig.defaultLightColorScheme,\n    dark: defaultConfig.defaultDarkColorScheme\n  },\n  resolveTheme: theme => {\n    const newTheme = _extends({}, theme, {\n      typography: createTypography(theme.palette, theme.typography)\n    });\n    newTheme.unstable_sx = function sx(props) {\n      return styleFunctionSx({\n        sx: props,\n        theme: this\n      });\n    };\n    return newTheme;\n  },\n  excludeVariablesFromRoot\n});\n\n/**\n * @deprecated Use `InitColorSchemeScript` instead\n * ```diff\n * - import { getInitColorSchemeScript } from '@mui/material/styles';\n * + import InitColorSchemeScript from '@mui/material/InitColorSchemeScript';\n *\n * - getInitColorSchemeScript();\n * + <InitColorSchemeScript />;\n * ```\n */\nexport const getInitColorSchemeScript = getInitColorSchemeScriptSystem;\nexport { useColorScheme, CssVarsProvider as Experimental_CssVarsProvider };", "map": {"version": 3, "names": ["_extends", "unstable_createCssVarsProvider", "createCssVarsProvider", "styleFunctionSx", "experimental_extendTheme", "createTypography", "excludeVariablesFromRoot", "THEME_ID", "defaultConfig", "defaultTheme", "CssVarsProvider", "useColorScheme", "getInitColorSchemeScript", "getInitColorSchemeScriptSystem", "themeId", "theme", "attribute", "colorSchemeStorageKey", "modeStorageKey", "defaultColorScheme", "light", "defaultLightColorScheme", "dark", "defaultDarkColorScheme", "resolveTheme", "newTheme", "typography", "palette", "unstable_sx", "sx", "props", "Experimental_CssVarsProvider"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/styles/CssVarsProvider.js"], "sourcesContent": ["'use client';\n\n// do not remove the following import (https://github.com/microsoft/TypeScript/issues/29808#issuecomment-**********)\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// @ts-ignore\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { unstable_createCssVarsProvider as createCssVarsProvider } from '@mui/system';\nimport styleFunctionSx from '@mui/system/styleFunctionSx';\nimport experimental_extendTheme from './experimental_extendTheme';\nimport createTypography from './createTypography';\nimport excludeVariablesFromRoot from './excludeVariablesFromRoot';\nimport THEME_ID from './identifier';\nimport { defaultConfig } from '../InitColorSchemeScript/InitColorSchemeScript';\nconst defaultTheme = experimental_extendTheme();\nconst {\n  CssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript: getInitColorSchemeScriptSystem\n} = createCssVarsProvider({\n  themeId: THEME_ID,\n  theme: defaultTheme,\n  attribute: defaultConfig.attribute,\n  colorSchemeStorageKey: defaultConfig.colorSchemeStorageKey,\n  modeStorageKey: defaultConfig.modeStorageKey,\n  defaultColorScheme: {\n    light: defaultConfig.defaultLightColorScheme,\n    dark: defaultConfig.defaultDarkColorScheme\n  },\n  resolveTheme: theme => {\n    const newTheme = _extends({}, theme, {\n      typography: createTypography(theme.palette, theme.typography)\n    });\n    newTheme.unstable_sx = function sx(props) {\n      return styleFunctionSx({\n        sx: props,\n        theme: this\n      });\n    };\n    return newTheme;\n  },\n  excludeVariablesFromRoot\n});\n\n/**\n * @deprecated Use `InitColorSchemeScript` instead\n * ```diff\n * - import { getInitColorSchemeScript } from '@mui/material/styles';\n * + import InitColorSchemeScript from '@mui/material/InitColorSchemeScript';\n *\n * - getInitColorSchemeScript();\n * + <InitColorSchemeScript />;\n * ```\n */\nexport const getInitColorSchemeScript = getInitColorSchemeScriptSystem;\nexport { useColorScheme, CssVarsProvider as Experimental_CssVarsProvider };"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,8BAA8B,IAAIC,qBAAqB,QAAQ,aAAa;AACrF,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,QAAQ,MAAM,cAAc;AACnC,SAASC,aAAa,QAAQ,gDAAgD;AAC9E,MAAMC,YAAY,GAAGL,wBAAwB,CAAC,CAAC;AAC/C,MAAM;EACJM,eAAe;EACfC,cAAc;EACdC,wBAAwB,EAAEC;AAC5B,CAAC,GAAGX,qBAAqB,CAAC;EACxBY,OAAO,EAAEP,QAAQ;EACjBQ,KAAK,EAAEN,YAAY;EACnBO,SAAS,EAAER,aAAa,CAACQ,SAAS;EAClCC,qBAAqB,EAAET,aAAa,CAACS,qBAAqB;EAC1DC,cAAc,EAAEV,aAAa,CAACU,cAAc;EAC5CC,kBAAkB,EAAE;IAClBC,KAAK,EAAEZ,aAAa,CAACa,uBAAuB;IAC5CC,IAAI,EAAEd,aAAa,CAACe;EACtB,CAAC;EACDC,YAAY,EAAET,KAAK,IAAI;IACrB,MAAMU,QAAQ,GAAGzB,QAAQ,CAAC,CAAC,CAAC,EAAEe,KAAK,EAAE;MACnCW,UAAU,EAAErB,gBAAgB,CAACU,KAAK,CAACY,OAAO,EAAEZ,KAAK,CAACW,UAAU;IAC9D,CAAC,CAAC;IACFD,QAAQ,CAACG,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;MACxC,OAAO3B,eAAe,CAAC;QACrB0B,EAAE,EAAEC,KAAK;QACTf,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC;IACD,OAAOU,QAAQ;EACjB,CAAC;EACDnB;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,wBAAwB,GAAGC,8BAA8B;AACtE,SAASF,cAAc,EAAED,eAAe,IAAIqB,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}