import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';
import { LoginRequest, LoginResponse, RegisterRequest, UpdateProfileRequest, ChangePasswordRequest } from '../types/auth';
import { ApiResponse, PaginationParams } from '../types/common';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
      toast.error('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى');
    } else if (error.response?.status === 403) {
      toast.error('ليس لديك صلاحية للوصول إلى هذا المورد');
    } else if (error.response?.status >= 500) {
      toast.error('خطأ في الخادم، يرجى المحاولة لاحقاً');
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (data: LoginRequest): Promise<AxiosResponse<LoginResponse>> =>
    api.post('/auth/login', data),
  
  register: (data: RegisterRequest): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/register', data),
  
  logout: (): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/logout'),
  
  getProfile: (): Promise<AxiosResponse<ApiResponse<any>>> =>
    api.get('/auth/profile'),
  
  updateProfile: (data: UpdateProfileRequest): Promise<AxiosResponse<ApiResponse>> =>
    api.put('/auth/profile', data),
  
  changePassword: (data: ChangePasswordRequest): Promise<AxiosResponse<ApiResponse>> =>
    api.put('/auth/change-password', data),
};

// Sales API
export const salesAPI = {
  getAll: (params?: any): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/sales', { params }),
  
  getStats: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/sales/stats'),
  
  getById: (id: string): Promise<AxiosResponse<ApiResponse>> =>
    api.get(`/sales/${id}`),
  
  create: (data: any): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/sales', data),
  
  update: (id: string, data: any): Promise<AxiosResponse<ApiResponse>> =>
    api.put(`/sales/${id}`, data),
  
  delete: (id: string): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/sales/${id}`),
};

// Purchases API
export const purchasesAPI = {
  getAll: (params?: any): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/purchases', { params }),
  
  getStats: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/purchases/stats'),
  
  getById: (id: string): Promise<AxiosResponse<ApiResponse>> =>
    api.get(`/purchases/${id}`),
  
  create: (data: any): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/purchases', data),
  
  update: (id: string, data: any): Promise<AxiosResponse<ApiResponse>> =>
    api.put(`/purchases/${id}`, data),
  
  delete: (id: string): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/purchases/${id}`),
};

// Settings API
export const settingsAPI = {
  getAll: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/settings'),
  
  updateSection: (section: string, data: any): Promise<AxiosResponse<ApiResponse>> =>
    api.put(`/settings/${section}`, data),
  
  testEmail: (email: string): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/settings/test-email', { email }),
  
  getSystemInfo: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/settings/system-info'),
};

// Other APIs
export const customersAPI = {
  getAll: (params?: PaginationParams): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/customers', { params }),
};

export const productsAPI = {
  getAll: (params?: PaginationParams): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/products', { params }),
};

export const reportsAPI = {
  getDashboard: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/reports/dashboard'),
};

export const paymentsAPI = {
  getAll: (params?: PaginationParams): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/payments', { params }),
};

export default api;
