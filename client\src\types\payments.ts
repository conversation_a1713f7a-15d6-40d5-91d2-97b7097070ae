export interface Payment {
  id: string;
  invoiceId?: string;
  invoiceNumber?: string;
  customerId?: string;
  customerName?: string;
  supplierId?: string;
  supplierName?: string;
  amount: number;
  paidAmount: number;
  remainingAmount: number;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  dueDate?: string;
  status: PaymentStatus;
  type: PaymentType;
  reference?: string;
  notes?: string;
  attachments?: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
export type PaymentMethod = 'cash' | 'card' | 'bank_transfer' | 'check' | 'installment' | 'credit';
export type PaymentType = 'incoming' | 'outgoing';

export interface PaymentTransaction {
  id: string;
  paymentId: string;
  amount: number;
  paymentMethod: PaymentMethod;
  transactionDate: string;
  reference?: string;
  status: 'success' | 'failed' | 'pending';
  notes?: string;
  createdAt: string;
}

export interface CreatePaymentRequest {
  invoiceId?: string;
  customerId?: string;
  supplierId?: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  type: PaymentType;
  reference?: string;
  notes?: string;
}

export interface PaymentFilters {
  status?: PaymentStatus;
  paymentMethod?: PaymentMethod;
  type?: PaymentType;
  customerId?: string;
  supplierId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface PaymentStats {
  totalIncoming: number;
  totalOutgoing: number;
  pendingPayments: number;
  completedPayments: number;
  todayPayments: number;
  monthlyIncoming: number;
  monthlyOutgoing: number;
  overduePayments: number;
}

export const PAYMENT_STATUSES: { value: PaymentStatus; label: string; color: 'success' | 'warning' | 'error' | 'default' | 'info' }[] = [
  { value: 'pending', label: 'في الانتظار', color: 'warning' },
  { value: 'completed', label: 'مكتملة', color: 'success' },
  { value: 'failed', label: 'فاشلة', color: 'error' },
  { value: 'cancelled', label: 'ملغية', color: 'default' },
  { value: 'refunded', label: 'مسترد', color: 'info' },
];

export const PAYMENT_METHODS: { value: PaymentMethod; label: string }[] = [
  { value: 'cash', label: 'نقداً' },
  { value: 'card', label: 'بطاقة' },
  { value: 'bank_transfer', label: 'تحويل بنكي' },
  { value: 'check', label: 'شيك' },
  { value: 'installment', label: 'أقساط' },
  { value: 'credit', label: 'آجل' },
];

export const PAYMENT_TYPES: { value: PaymentType; label: string; color: string }[] = [
  { value: 'incoming', label: 'وارد', color: '#4caf50' },
  { value: 'outgoing', label: 'صادر', color: '#f44336' },
];
