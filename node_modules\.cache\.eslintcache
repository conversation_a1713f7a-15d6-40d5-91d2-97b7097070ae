[{"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\zencod\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\zencod\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\zencod\\src\\App.simple.tsx": "3"}, {"size": 735, "mtime": 1751727272016, "results": "4", "hashOfConfig": "5"}, {"size": 9650, "mtime": 1751729357749, "results": "6", "hashOfConfig": "5"}, {"size": 1039, "mtime": 1750257433581, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13i131b", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\zencod\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\zencod\\src\\App.tsx", ["17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37"], [], "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\zencod\\src\\App.simple.tsx", [], [], {"ruleId": "38", "severity": 1, "message": "39", "line": 20, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 20, "endColumn": 9}, {"ruleId": "38", "severity": 1, "message": "42", "line": 21, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 21, "endColumn": 12}, {"ruleId": "38", "severity": 1, "message": "43", "line": 22, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 22, "endColumn": 8}, {"ruleId": "38", "severity": 1, "message": "44", "line": 23, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 23, "endColumn": 12}, {"ruleId": "38", "severity": 1, "message": "45", "line": 24, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 24, "endColumn": 12}, {"ruleId": "38", "severity": 1, "message": "46", "line": 25, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 25, "endColumn": 17}, {"ruleId": "38", "severity": 1, "message": "47", "line": 26, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 26, "endColumn": 12}, {"ruleId": "38", "severity": 1, "message": "48", "line": 27, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 27, "endColumn": 11}, {"ruleId": "38", "severity": 1, "message": "49", "line": 28, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 28, "endColumn": 8}, {"ruleId": "38", "severity": 1, "message": "50", "line": 29, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 29, "endColumn": 9}, {"ruleId": "38", "severity": 1, "message": "51", "line": 30, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 30, "endColumn": 14}, {"ruleId": "38", "severity": 1, "message": "52", "line": 31, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 31, "endColumn": 16}, {"ruleId": "38", "severity": 1, "message": "53", "line": 32, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 32, "endColumn": 16}, {"ruleId": "38", "severity": 1, "message": "54", "line": 33, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 33, "endColumn": 14}, {"ruleId": "38", "severity": 1, "message": "55", "line": 34, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 34, "endColumn": 13}, {"ruleId": "38", "severity": 1, "message": "56", "line": 35, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 35, "endColumn": 9}, {"ruleId": "38", "severity": 1, "message": "57", "line": 36, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 36, "endColumn": 15}, {"ruleId": "38", "severity": 1, "message": "58", "line": 37, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 37, "endColumn": 10}, {"ruleId": "38", "severity": 1, "message": "59", "line": 38, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 38, "endColumn": 7}, {"ruleId": "38", "severity": 1, "message": "60", "line": 39, "column": 3, "nodeType": "40", "messageId": "41", "endLine": 39, "endColumn": 8}, {"ruleId": "61", "severity": 2, "message": "62", "line": 214, "column": 17, "nodeType": "63", "messageId": "64", "endLine": 214, "endColumn": 34}, "@typescript-eslint/no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'TextField' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Paper' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'Autocomplete' is defined but never used.", "'Divider' is defined but never used.", "'Chip' is defined but never used.", "'Alert' is defined but never used.", "react/jsx-no-undef", "'InvoiceManagement' is not defined.", "JSXIdentifier", "undefined"]