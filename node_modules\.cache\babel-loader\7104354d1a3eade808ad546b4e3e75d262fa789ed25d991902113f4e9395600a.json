{"ast": null, "code": "import ClassNameGenerator from '../ClassNameGenerator';\nexport const globalStateClasses = {\n  active: 'active',\n  checked: 'checked',\n  completed: 'completed',\n  disabled: 'disabled',\n  error: 'error',\n  expanded: 'expanded',\n  focused: 'focused',\n  focusVisible: 'focusVisible',\n  open: 'open',\n  readOnly: 'readOnly',\n  required: 'required',\n  selected: 'selected'\n};\nexport default function generateUtilityClass(componentName, slot, globalStatePrefix = 'Mui') {\n  const globalStateClass = globalStateClasses[slot];\n  return globalStateClass ? `${globalStatePrefix}-${globalStateClass}` : `${ClassNameGenerator.generate(componentName)}-${slot}`;\n}\nexport function isGlobalState(slot) {\n  return globalStateClasses[slot] !== undefined;\n}", "map": {"version": 3, "names": ["ClassNameGenerator", "globalStateClasses", "active", "checked", "completed", "disabled", "error", "expanded", "focused", "focusVisible", "open", "readOnly", "required", "selected", "generateUtilityClass", "componentName", "slot", "globalStatePrefix", "globalStateClass", "generate", "isGlobalState", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/utils/esm/generateUtilityClass/generateUtilityClass.js"], "sourcesContent": ["import ClassNameGenerator from '../ClassNameGenerator';\nexport const globalStateClasses = {\n  active: 'active',\n  checked: 'checked',\n  completed: 'completed',\n  disabled: 'disabled',\n  error: 'error',\n  expanded: 'expanded',\n  focused: 'focused',\n  focusVisible: 'focusVisible',\n  open: 'open',\n  readOnly: 'readOnly',\n  required: 'required',\n  selected: 'selected'\n};\nexport default function generateUtilityClass(componentName, slot, globalStatePrefix = 'Mui') {\n  const globalStateClass = globalStateClasses[slot];\n  return globalStateClass ? `${globalStatePrefix}-${globalStateClass}` : `${ClassNameGenerator.generate(componentName)}-${slot}`;\n}\nexport function isGlobalState(slot) {\n  return globalStateClasses[slot] !== undefined;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,uBAAuB;AACtD,OAAO,MAAMC,kBAAkB,GAAG;EAChCC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,cAAc;EAC5BC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;AACZ,CAAC;AACD,eAAe,SAASC,oBAAoBA,CAACC,aAAa,EAAEC,IAAI,EAAEC,iBAAiB,GAAG,KAAK,EAAE;EAC3F,MAAMC,gBAAgB,GAAGjB,kBAAkB,CAACe,IAAI,CAAC;EACjD,OAAOE,gBAAgB,GAAG,GAAGD,iBAAiB,IAAIC,gBAAgB,EAAE,GAAG,GAAGlB,kBAAkB,CAACmB,QAAQ,CAACJ,aAAa,CAAC,IAAIC,IAAI,EAAE;AAChI;AACA,OAAO,SAASI,aAAaA,CAACJ,IAAI,EAAE;EAClC,OAAOf,kBAAkB,CAACe,IAAI,CAAC,KAAKK,SAAS;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}