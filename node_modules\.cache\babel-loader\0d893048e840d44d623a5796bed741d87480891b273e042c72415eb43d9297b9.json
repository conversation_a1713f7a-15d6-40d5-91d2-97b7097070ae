{"ast": null, "code": "'use client';\n\nexport { default } from './Link';\nexport { default as linkClasses } from './linkClasses';\nexport * from './linkClasses';", "map": {"version": 3, "names": ["default", "linkClasses"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/Link/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Link';\nexport { default as linkClasses } from './linkClasses';\nexport * from './linkClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,QAAQ;AAChC,SAASA,OAAO,IAAIC,WAAW,QAAQ,eAAe;AACtD,cAAc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}