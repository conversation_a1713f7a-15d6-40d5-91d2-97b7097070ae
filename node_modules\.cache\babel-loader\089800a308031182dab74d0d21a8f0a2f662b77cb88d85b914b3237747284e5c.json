{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport clsx from 'clsx';\nimport extractEventHandlers from '../extractEventHandlers';\nimport omitEventHandlers from '../omitEventHandlers';\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(additionalProps == null ? void 0 : additionalProps.className, className, externalForwardedProps == null ? void 0 : externalForwardedProps.className, externalSlotProps == null ? void 0 : externalSlotProps.className);\n    const mergedStyle = _extends({}, additionalProps == null ? void 0 : additionalProps.style, externalForwardedProps == null ? void 0 : externalForwardedProps.style, externalSlotProps == null ? void 0 : externalSlotProps.style);\n    const props = _extends({}, additionalProps, externalForwardedProps, externalSlotProps);\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers(_extends({}, externalForwardedProps, externalSlotProps));\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps == null ? void 0 : internalSlotProps.className, additionalProps == null ? void 0 : additionalProps.className, className, externalForwardedProps == null ? void 0 : externalForwardedProps.className, externalSlotProps == null ? void 0 : externalSlotProps.className);\n  const mergedStyle = _extends({}, internalSlotProps == null ? void 0 : internalSlotProps.style, additionalProps == null ? void 0 : additionalProps.style, externalForwardedProps == null ? void 0 : externalForwardedProps.style, externalSlotProps == null ? void 0 : externalSlotProps.style);\n  const props = _extends({}, internalSlotProps, additionalProps, otherPropsWithoutEventHandlers, componentsPropsWithoutEventHandlers);\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nexport default mergeSlotProps;", "map": {"version": 3, "names": ["_extends", "clsx", "extractEventHandlers", "omitEventHandlers", "mergeSlotProps", "parameters", "getSlotProps", "additionalProps", "externalSlotProps", "externalForwardedProps", "className", "joinedClasses", "mergedStyle", "style", "props", "length", "Object", "keys", "internalRef", "undefined", "eventHandlers", "componentsPropsWithoutEventHandlers", "otherPropsWithoutEventHandlers", "internalSlotProps", "ref"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/utils/esm/mergeSlotProps/mergeSlotProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport clsx from 'clsx';\nimport extractEventHandlers from '../extractEventHandlers';\nimport omitEventHandlers from '../omitEventHandlers';\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(additionalProps == null ? void 0 : additionalProps.className, className, externalForwardedProps == null ? void 0 : externalForwardedProps.className, externalSlotProps == null ? void 0 : externalSlotProps.className);\n    const mergedStyle = _extends({}, additionalProps == null ? void 0 : additionalProps.style, externalForwardedProps == null ? void 0 : externalForwardedProps.style, externalSlotProps == null ? void 0 : externalSlotProps.style);\n    const props = _extends({}, additionalProps, externalForwardedProps, externalSlotProps);\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers(_extends({}, externalForwardedProps, externalSlotProps));\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps == null ? void 0 : internalSlotProps.className, additionalProps == null ? void 0 : additionalProps.className, className, externalForwardedProps == null ? void 0 : externalForwardedProps.className, externalSlotProps == null ? void 0 : externalSlotProps.className);\n  const mergedStyle = _extends({}, internalSlotProps == null ? void 0 : internalSlotProps.style, additionalProps == null ? void 0 : additionalProps.style, externalForwardedProps == null ? void 0 : externalForwardedProps.style, externalSlotProps == null ? void 0 : externalSlotProps.style);\n  const props = _extends({}, internalSlotProps, additionalProps, otherPropsWithoutEventHandlers, componentsPropsWithoutEventHandlers);\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nexport default mergeSlotProps;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAClC,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,sBAAsB;IACtBC;EACF,CAAC,GAAGL,UAAU;EACd,IAAI,CAACC,YAAY,EAAE;IACjB;IACA;IACA,MAAMK,aAAa,GAAGV,IAAI,CAACM,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACG,SAAS,EAAEA,SAAS,EAAED,sBAAsB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACC,SAAS,EAAEF,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACE,SAAS,CAAC;IACjP,MAAME,WAAW,GAAGZ,QAAQ,CAAC,CAAC,CAAC,EAAEO,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACM,KAAK,EAAEJ,sBAAsB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACI,KAAK,EAAEL,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACK,KAAK,CAAC;IAChO,MAAMC,KAAK,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEO,eAAe,EAAEE,sBAAsB,EAAED,iBAAiB,CAAC;IACtF,IAAIG,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;MAC5BD,KAAK,CAACJ,SAAS,GAAGC,aAAa;IACjC;IACA,IAAIK,MAAM,CAACC,IAAI,CAACL,WAAW,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;MACvCD,KAAK,CAACD,KAAK,GAAGD,WAAW;IAC3B;IACA,OAAO;MACLE,KAAK;MACLI,WAAW,EAAEC;IACf,CAAC;EACH;;EAEA;EACA;;EAEA,MAAMC,aAAa,GAAGlB,oBAAoB,CAACF,QAAQ,CAAC,CAAC,CAAC,EAAES,sBAAsB,EAAED,iBAAiB,CAAC,CAAC;EACnG,MAAMa,mCAAmC,GAAGlB,iBAAiB,CAACK,iBAAiB,CAAC;EAChF,MAAMc,8BAA8B,GAAGnB,iBAAiB,CAACM,sBAAsB,CAAC;EAChF,MAAMc,iBAAiB,GAAGjB,YAAY,CAACc,aAAa,CAAC;;EAErD;EACA;EACA;EACA;EACA,MAAMT,aAAa,GAAGV,IAAI,CAACsB,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACb,SAAS,EAAEH,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACG,SAAS,EAAEA,SAAS,EAAED,sBAAsB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACC,SAAS,EAAEF,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACE,SAAS,CAAC;EACnT,MAAME,WAAW,GAAGZ,QAAQ,CAAC,CAAC,CAAC,EAAEuB,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACV,KAAK,EAAEN,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACM,KAAK,EAAEJ,sBAAsB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACI,KAAK,EAAEL,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACK,KAAK,CAAC;EAC9R,MAAMC,KAAK,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEuB,iBAAiB,EAAEhB,eAAe,EAAEe,8BAA8B,EAAED,mCAAmC,CAAC;EACnI,IAAIV,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;IAC5BD,KAAK,CAACJ,SAAS,GAAGC,aAAa;EACjC;EACA,IAAIK,MAAM,CAACC,IAAI,CAACL,WAAW,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;IACvCD,KAAK,CAACD,KAAK,GAAGD,WAAW;EAC3B;EACA,OAAO;IACLE,KAAK;IACLI,WAAW,EAAEK,iBAAiB,CAACC;EACjC,CAAC;AACH;AACA,eAAepB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}