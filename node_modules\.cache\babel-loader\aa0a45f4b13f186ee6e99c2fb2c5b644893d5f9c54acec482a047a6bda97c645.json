{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\zencod\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { Box, Typography, Card, CardContent } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 4,\n      minHeight: '100vh',\n      backgroundColor: '#f5f5f5'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h3\",\n      gutterBottom: true,\n      sx: {\n        textAlign: 'center',\n        mb: 4\n      },\n      children: \"\\u0646\\u0638\\u0627\\u0645 \\u0632\\u064A\\u0646 \\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u064A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        maxWidth: 600,\n        mx: 'auto'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 4,\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          color: \"primary\",\n          children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0646\\u0638\\u0627\\u0645 \\u0632\\u064A\\u0646 \\u0643\\u0648\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"\\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u064A\\u0639\\u0645\\u0644 \\u0628\\u0646\\u062C\\u0627\\u062D! \\uD83C\\uDF89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 2\n          },\n          children: \"\\u062A\\u0645 \\u062D\\u0644 \\u0645\\u0634\\u0643\\u0644\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0645\\u064A\\u0644 \\u0648\\u0623\\u0635\\u0628\\u062D \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u062C\\u0627\\u0647\\u0632\\u0627\\u064B \\u0644\\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "sx", "p", "minHeight", "backgroundColor", "children", "variant", "gutterBottom", "textAlign", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "mx", "color", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Card, CardContent } from '@mui/material';\n\nconst App: React.FC = () => {\n  return (\n    <Box sx={{ p: 4, minHeight: '100vh', backgroundColor: '#f5f5f5' }}>\n      <Typography variant=\"h3\" gutterBottom sx={{ textAlign: 'center', mb: 4 }}>\n        نظام زين كود المحاسبي\n      </Typography>\n      \n      <Card sx={{ maxWidth: 600, mx: 'auto' }}>\n        <CardContent sx={{ p: 4, textAlign: 'center' }}>\n          <Typography variant=\"h5\" gutterBottom color=\"primary\">\n            مرحباً بك في نظام زين كود\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            النظام يعمل بنجاح! 🎉\n          </Typography>\n          <Typography variant=\"body2\" sx={{ mt: 2 }}>\n            تم حل مشكلة التحميل وأصبح النظام جاهزاً للاستخدام\n          </Typography>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA,CAACL,GAAG;IAACO,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,SAAS,EAAE,OAAO;MAAEC,eAAe,EAAE;IAAU,CAAE;IAAAC,QAAA,gBAChEN,OAAA,CAACJ,UAAU;MAACW,OAAO,EAAC,IAAI;MAACC,YAAY;MAACN,EAAE,EAAE;QAAEO,SAAS,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAC;IAE1E;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbd,OAAA,CAACH,IAAI;MAACK,EAAE,EAAE;QAAEa,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAO,CAAE;MAAAV,QAAA,eACtCN,OAAA,CAACF,WAAW;QAACI,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEM,SAAS,EAAE;QAAS,CAAE;QAAAH,QAAA,gBAC7CN,OAAA,CAACJ,UAAU;UAACW,OAAO,EAAC,IAAI;UAACC,YAAY;UAACS,KAAK,EAAC,SAAS;UAAAX,QAAA,EAAC;QAEtD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbd,OAAA,CAACJ,UAAU;UAACW,OAAO,EAAC,OAAO;UAACU,KAAK,EAAC,gBAAgB;UAAAX,QAAA,EAAC;QAEnD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbd,OAAA,CAACJ,UAAU;UAACW,OAAO,EAAC,OAAO;UAACL,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE,CAAE;UAAAZ,QAAA,EAAC;QAE3C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACK,EAAA,GAtBIlB,GAAa;AAwBnB,eAAeA,GAAG;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}