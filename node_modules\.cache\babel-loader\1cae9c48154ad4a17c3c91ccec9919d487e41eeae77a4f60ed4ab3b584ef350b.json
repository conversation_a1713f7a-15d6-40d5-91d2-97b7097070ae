{"ast": null, "code": "import requirePropFactory from '@mui/utils/requirePropFactory';\nexport default requirePropFactory;", "map": {"version": 3, "names": ["requirePropFactory"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/utils/requirePropFactory.js"], "sourcesContent": ["import requirePropFactory from '@mui/utils/requirePropFactory';\nexport default requirePropFactory;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,+BAA+B;AAC9D,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}