{"ast": null, "code": "'use client';\n\nexport { default } from './TablePagination';\nexport { default as tablePaginationClasses } from './tablePaginationClasses';\nexport * from './tablePaginationClasses';", "map": {"version": 3, "names": ["default", "tablePaginationClasses"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/TablePagination/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TablePagination';\nexport { default as tablePaginationClasses } from './tablePaginationClasses';\nexport * from './tablePaginationClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASA,OAAO,IAAIC,sBAAsB,QAAQ,0BAA0B;AAC5E,cAAc,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}