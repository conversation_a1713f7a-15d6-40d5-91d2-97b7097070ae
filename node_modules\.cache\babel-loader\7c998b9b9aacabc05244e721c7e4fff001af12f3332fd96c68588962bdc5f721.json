{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport styled from '@mui/styled-engine';\nimport styleFunctionSx, { extendSxProp } from './styleFunctionSx';\nimport useTheme from './useTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createBox(options = {}) {\n  const {\n    themeId,\n    defaultTheme,\n    defaultClassName = 'MuiBox-root',\n    generateClassName\n  } = options;\n  const BoxRoot = styled('div', {\n    shouldForwardProp: prop => prop !== 'theme' && prop !== 'sx' && prop !== 'as'\n  })(styleFunctionSx);\n  const Box = /*#__PURE__*/React.forwardRef(function Box(inProps, ref) {\n    const theme = useTheme(defaultTheme);\n    const _extendSxProp = extendSxProp(inProps),\n      {\n        className,\n        component = 'div'\n      } = _extendSxProp,\n      other = _objectWithoutPropertiesLoose(_extendSxProp, _excluded);\n    return /*#__PURE__*/_jsx(BoxRoot, _extends({\n      as: component,\n      ref: ref,\n      className: clsx(className, generateClassName ? generateClassName(defaultClassName) : defaultClassName),\n      theme: themeId ? theme[themeId] || theme : theme\n    }, other));\n  });\n  return Box;\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "styled", "styleFunctionSx", "extendSxProp", "useTheme", "jsx", "_jsx", "createBox", "options", "themeId", "defaultTheme", "defaultClassName", "generateClassName", "BoxRoot", "shouldForwardProp", "prop", "Box", "forwardRef", "inProps", "ref", "theme", "_extendSxProp", "className", "component", "other", "as"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/system/esm/createBox.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport styled from '@mui/styled-engine';\nimport styleFunctionSx, { extendSxProp } from './styleFunctionSx';\nimport useTheme from './useTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createBox(options = {}) {\n  const {\n    themeId,\n    defaultTheme,\n    defaultClassName = 'MuiBox-root',\n    generateClassName\n  } = options;\n  const BoxRoot = styled('div', {\n    shouldForwardProp: prop => prop !== 'theme' && prop !== 'sx' && prop !== 'as'\n  })(styleFunctionSx);\n  const Box = /*#__PURE__*/React.forwardRef(function Box(inProps, ref) {\n    const theme = useTheme(defaultTheme);\n    const _extendSxProp = extendSxProp(inProps),\n      {\n        className,\n        component = 'div'\n      } = _extendSxProp,\n      other = _objectWithoutPropertiesLoose(_extendSxProp, _excluded);\n    return /*#__PURE__*/_jsx(BoxRoot, _extends({\n      as: component,\n      ref: ref,\n      className: clsx(className, generateClassName ? generateClassName(defaultClassName) : defaultClassName),\n      theme: themeId ? theme[themeId] || theme : theme\n    }, other));\n  });\n  return Box;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC;AAC5C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,eAAe,IAAIC,YAAY,QAAQ,mBAAmB;AACjE,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,SAASA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC9C,MAAM;IACJC,OAAO;IACPC,YAAY;IACZC,gBAAgB,GAAG,aAAa;IAChCC;EACF,CAAC,GAAGJ,OAAO;EACX,MAAMK,OAAO,GAAGZ,MAAM,CAAC,KAAK,EAAE;IAC5Ba,iBAAiB,EAAEC,IAAI,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK;EAC3E,CAAC,CAAC,CAACb,eAAe,CAAC;EACnB,MAAMc,GAAG,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,SAASD,GAAGA,CAACE,OAAO,EAAEC,GAAG,EAAE;IACnE,MAAMC,KAAK,GAAGhB,QAAQ,CAACM,YAAY,CAAC;IACpC,MAAMW,aAAa,GAAGlB,YAAY,CAACe,OAAO,CAAC;MACzC;QACEI,SAAS;QACTC,SAAS,GAAG;MACd,CAAC,GAAGF,aAAa;MACjBG,KAAK,GAAG3B,6BAA6B,CAACwB,aAAa,EAAEvB,SAAS,CAAC;IACjE,OAAO,aAAaQ,IAAI,CAACO,OAAO,EAAEjB,QAAQ,CAAC;MACzC6B,EAAE,EAAEF,SAAS;MACbJ,GAAG,EAAEA,GAAG;MACRG,SAAS,EAAEtB,IAAI,CAACsB,SAAS,EAAEV,iBAAiB,GAAGA,iBAAiB,CAACD,gBAAgB,CAAC,GAAGA,gBAAgB,CAAC;MACtGS,KAAK,EAAEX,OAAO,GAAGW,KAAK,CAACX,OAAO,CAAC,IAAIW,KAAK,GAAGA;IAC7C,CAAC,EAAEI,KAAK,CAAC,CAAC;EACZ,CAAC,CAAC;EACF,OAAOR,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}