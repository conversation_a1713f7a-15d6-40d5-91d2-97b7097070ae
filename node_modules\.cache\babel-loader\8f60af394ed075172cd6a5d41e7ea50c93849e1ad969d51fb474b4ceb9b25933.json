{"ast": null, "code": "/**\n * @internal These variables should not appear in the :root stylesheet when the `defaultMode=\"dark\"`\n */\nconst excludeVariablesFromRoot = cssVarPrefix => [...[...Array(24)].map((_, index) => `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}overlays-${index + 1}`), `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkBg`, `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkColor`];\nexport default excludeVariablesFromRoot;", "map": {"version": 3, "names": ["excludeVariablesFromRoot", "cssVarPrefix", "Array", "map", "_", "index"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/styles/excludeVariablesFromRoot.js"], "sourcesContent": ["/**\n * @internal These variables should not appear in the :root stylesheet when the `defaultMode=\"dark\"`\n */\nconst excludeVariablesFromRoot = cssVarPrefix => [...[...Array(24)].map((_, index) => `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}overlays-${index + 1}`), `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkBg`, `--${cssVarPrefix ? `${cssVarPrefix}-` : ''}palette-AppBar-darkColor`];\nexport default excludeVariablesFromRoot;"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,wBAAwB,GAAGC,YAAY,IAAI,CAAC,GAAG,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK,KAAKJ,YAAY,GAAG,GAAGA,YAAY,GAAG,GAAG,EAAE,YAAYI,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,KAAKJ,YAAY,GAAG,GAAGA,YAAY,GAAG,GAAG,EAAE,uBAAuB,EAAE,KAAKA,YAAY,GAAG,GAAGA,YAAY,GAAG,GAAG,EAAE,0BAA0B,CAAC;AACrS,eAAeD,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}