{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useTimeout as useTimeout } from '@mui/utils';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\n/**\n * The basic building block for creating custom snackbar.\n *\n * Demos:\n *\n * - [Snackbar](https://mui.com/base-ui/react-snackbar/#hook)\n *\n * API:\n *\n * - [useSnackbar API](https://mui.com/base-ui/react-snackbar/hooks-api/#use-snackbar)\n */\nfunction useSnackbar(parameters = {}) {\n  const {\n    autoHideDuration = null,\n    disableWindowBlurListener = false,\n    onClose,\n    open,\n    resumeHideDuration\n  } = parameters;\n  const timerAutoHide = useTimeout();\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      if (!nativeEvent.defaultPrevented) {\n        // IE11, Edge (prior to using Blink?) use 'Esc'\n        if (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc') {\n          // not calling `preventDefault` since we don't know if people may ignore this event e.g. a permanently open snackbar\n          onClose == null || onClose(nativeEvent, 'escapeKeyDown');\n        }\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [open, onClose]);\n  const handleClose = useEventCallback((event, reason) => {\n    onClose == null || onClose(event, reason);\n  });\n  const setAutoHideTimer = useEventCallback(autoHideDurationParam => {\n    if (!onClose || autoHideDurationParam == null) {\n      return;\n    }\n    timerAutoHide.start(autoHideDurationParam, () => {\n      handleClose(null, 'timeout');\n    });\n  });\n  React.useEffect(() => {\n    if (open) {\n      setAutoHideTimer(autoHideDuration);\n    }\n    return timerAutoHide.clear;\n  }, [open, autoHideDuration, setAutoHideTimer, timerAutoHide]);\n  const handleClickAway = event => {\n    onClose == null || onClose(event, 'clickaway');\n  };\n\n  // Pause the timer when the user is interacting with the Snackbar\n  // or when the user hide the window.\n  const handlePause = timerAutoHide.clear;\n\n  // Restart the timer when the user is no longer interacting with the Snackbar\n  // or when the window is shown back.\n  const handleResume = React.useCallback(() => {\n    if (autoHideDuration != null) {\n      setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n    }\n  }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n  const createHandleBlur = otherHandlers => event => {\n    const onBlurCallback = otherHandlers.onBlur;\n    onBlurCallback == null || onBlurCallback(event);\n    handleResume();\n  };\n  const createHandleFocus = otherHandlers => event => {\n    const onFocusCallback = otherHandlers.onFocus;\n    onFocusCallback == null || onFocusCallback(event);\n    handlePause();\n  };\n  const createMouseEnter = otherHandlers => event => {\n    const onMouseEnterCallback = otherHandlers.onMouseEnter;\n    onMouseEnterCallback == null || onMouseEnterCallback(event);\n    handlePause();\n  };\n  const createMouseLeave = otherHandlers => event => {\n    const onMouseLeaveCallback = otherHandlers.onMouseLeave;\n    onMouseLeaveCallback == null || onMouseLeaveCallback(event);\n    handleResume();\n  };\n  React.useEffect(() => {\n    // TODO: window global should be refactored here\n    if (!disableWindowBlurListener && open) {\n      window.addEventListener('focus', handleResume);\n      window.addEventListener('blur', handlePause);\n      return () => {\n        window.removeEventListener('focus', handleResume);\n        window.removeEventListener('blur', handlePause);\n      };\n    }\n    return undefined;\n  }, [disableWindowBlurListener, open, handleResume, handlePause]);\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));\n    return _extends({\n      // ClickAwayListener adds an `onClick` prop which results in the alert not being announced.\n      // See https://github.com/mui/material-ui/issues/29080\n      role: 'presentation'\n    }, externalProps, externalEventHandlers, {\n      onBlur: createHandleBlur(externalEventHandlers),\n      onFocus: createHandleFocus(externalEventHandlers),\n      onMouseEnter: createMouseEnter(externalEventHandlers),\n      onMouseLeave: createMouseLeave(externalEventHandlers)\n    });\n  };\n  return {\n    getRootProps,\n    onClickAway: handleClickAway\n  };\n}\nexport default useSnackbar;", "map": {"version": 3, "names": ["_extends", "React", "unstable_useEventCallback", "useEventCallback", "unstable_useTimeout", "useTimeout", "extractEventHandlers", "useSnackbar", "parameters", "autoHideDuration", "disableWindowBlurListener", "onClose", "open", "resumeHideDuration", "timerAutoHide", "useEffect", "undefined", "handleKeyDown", "nativeEvent", "defaultPrevented", "key", "document", "addEventListener", "removeEventListener", "handleClose", "event", "reason", "setAutoHideTimer", "autoHideDurationParam", "start", "clear", "handleClickAway", "handlePause", "handleResume", "useCallback", "createHandleBlur", "otherHandlers", "onBlurCallback", "onBlur", "createHandleFocus", "onFocusCallback", "onFocus", "createMouseEnter", "onMouseEnterCallback", "onMouseEnter", "createMouseLeave", "onMouseLeaveCallback", "onMouseLeave", "window", "getRootProps", "externalProps", "externalEventHandlers", "role", "onClickAway"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/Snackbar/useSnackbar.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useTimeout as useTimeout } from '@mui/utils';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\n/**\n * The basic building block for creating custom snackbar.\n *\n * Demos:\n *\n * - [Snackbar](https://mui.com/base-ui/react-snackbar/#hook)\n *\n * API:\n *\n * - [useSnackbar API](https://mui.com/base-ui/react-snackbar/hooks-api/#use-snackbar)\n */\nfunction useSnackbar(parameters = {}) {\n  const {\n    autoHideDuration = null,\n    disableWindowBlurListener = false,\n    onClose,\n    open,\n    resumeHideDuration\n  } = parameters;\n  const timerAutoHide = useTimeout();\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      if (!nativeEvent.defaultPrevented) {\n        // IE11, Edge (prior to using Blink?) use 'Esc'\n        if (nativeEvent.key === 'Escape' || nativeEvent.key === 'Esc') {\n          // not calling `preventDefault` since we don't know if people may ignore this event e.g. a permanently open snackbar\n          onClose == null || onClose(nativeEvent, 'escapeKeyDown');\n        }\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [open, onClose]);\n  const handleClose = useEventCallback((event, reason) => {\n    onClose == null || onClose(event, reason);\n  });\n  const setAutoHideTimer = useEventCallback(autoHideDurationParam => {\n    if (!onClose || autoHideDurationParam == null) {\n      return;\n    }\n    timerAutoHide.start(autoHideDurationParam, () => {\n      handleClose(null, 'timeout');\n    });\n  });\n  React.useEffect(() => {\n    if (open) {\n      setAutoHideTimer(autoHideDuration);\n    }\n    return timerAutoHide.clear;\n  }, [open, autoHideDuration, setAutoHideTimer, timerAutoHide]);\n  const handleClickAway = event => {\n    onClose == null || onClose(event, 'clickaway');\n  };\n\n  // Pause the timer when the user is interacting with the Snackbar\n  // or when the user hide the window.\n  const handlePause = timerAutoHide.clear;\n\n  // Restart the timer when the user is no longer interacting with the Snackbar\n  // or when the window is shown back.\n  const handleResume = React.useCallback(() => {\n    if (autoHideDuration != null) {\n      setAutoHideTimer(resumeHideDuration != null ? resumeHideDuration : autoHideDuration * 0.5);\n    }\n  }, [autoHideDuration, resumeHideDuration, setAutoHideTimer]);\n  const createHandleBlur = otherHandlers => event => {\n    const onBlurCallback = otherHandlers.onBlur;\n    onBlurCallback == null || onBlurCallback(event);\n    handleResume();\n  };\n  const createHandleFocus = otherHandlers => event => {\n    const onFocusCallback = otherHandlers.onFocus;\n    onFocusCallback == null || onFocusCallback(event);\n    handlePause();\n  };\n  const createMouseEnter = otherHandlers => event => {\n    const onMouseEnterCallback = otherHandlers.onMouseEnter;\n    onMouseEnterCallback == null || onMouseEnterCallback(event);\n    handlePause();\n  };\n  const createMouseLeave = otherHandlers => event => {\n    const onMouseLeaveCallback = otherHandlers.onMouseLeave;\n    onMouseLeaveCallback == null || onMouseLeaveCallback(event);\n    handleResume();\n  };\n  React.useEffect(() => {\n    // TODO: window global should be refactored here\n    if (!disableWindowBlurListener && open) {\n      window.addEventListener('focus', handleResume);\n      window.addEventListener('blur', handlePause);\n      return () => {\n        window.removeEventListener('focus', handleResume);\n        window.removeEventListener('blur', handlePause);\n      };\n    }\n    return undefined;\n  }, [disableWindowBlurListener, open, handleResume, handlePause]);\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));\n    return _extends({\n      // ClickAwayListener adds an `onClick` prop which results in the alert not being announced.\n      // See https://github.com/mui/material-ui/issues/29080\n      role: 'presentation'\n    }, externalProps, externalEventHandlers, {\n      onBlur: createHandleBlur(externalEventHandlers),\n      onFocus: createHandleFocus(externalEventHandlers),\n      onMouseEnter: createMouseEnter(externalEventHandlers),\n      onMouseLeave: createMouseLeave(externalEventHandlers)\n    });\n  };\n  return {\n    getRootProps,\n    onClickAway: handleClickAway\n  };\n}\nexport default useSnackbar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,IAAIC,gBAAgB,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC7G,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;EACpC,MAAM;IACJC,gBAAgB,GAAG,IAAI;IACvBC,yBAAyB,GAAG,KAAK;IACjCC,OAAO;IACPC,IAAI;IACJC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,aAAa,GAAGT,UAAU,CAAC,CAAC;EAClCJ,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,IAAI,CAACH,IAAI,EAAE;MACT,OAAOI,SAAS;IAClB;;IAEA;AACJ;AACA;IACI,SAASC,aAAaA,CAACC,WAAW,EAAE;MAClC,IAAI,CAACA,WAAW,CAACC,gBAAgB,EAAE;QACjC;QACA,IAAID,WAAW,CAACE,GAAG,KAAK,QAAQ,IAAIF,WAAW,CAACE,GAAG,KAAK,KAAK,EAAE;UAC7D;UACAT,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACO,WAAW,EAAE,eAAe,CAAC;QAC1D;MACF;IACF;IACAG,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEL,aAAa,CAAC;IACnD,OAAO,MAAM;MACXI,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACL,IAAI,EAAED,OAAO,CAAC,CAAC;EACnB,MAAMa,WAAW,GAAGrB,gBAAgB,CAAC,CAACsB,KAAK,EAAEC,MAAM,KAAK;IACtDf,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACc,KAAK,EAAEC,MAAM,CAAC;EAC3C,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGxB,gBAAgB,CAACyB,qBAAqB,IAAI;IACjE,IAAI,CAACjB,OAAO,IAAIiB,qBAAqB,IAAI,IAAI,EAAE;MAC7C;IACF;IACAd,aAAa,CAACe,KAAK,CAACD,qBAAqB,EAAE,MAAM;MAC/CJ,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;EACFvB,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,IAAIH,IAAI,EAAE;MACRe,gBAAgB,CAAClB,gBAAgB,CAAC;IACpC;IACA,OAAOK,aAAa,CAACgB,KAAK;EAC5B,CAAC,EAAE,CAAClB,IAAI,EAAEH,gBAAgB,EAAEkB,gBAAgB,EAAEb,aAAa,CAAC,CAAC;EAC7D,MAAMiB,eAAe,GAAGN,KAAK,IAAI;IAC/Bd,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACc,KAAK,EAAE,WAAW,CAAC;EAChD,CAAC;;EAED;EACA;EACA,MAAMO,WAAW,GAAGlB,aAAa,CAACgB,KAAK;;EAEvC;EACA;EACA,MAAMG,YAAY,GAAGhC,KAAK,CAACiC,WAAW,CAAC,MAAM;IAC3C,IAAIzB,gBAAgB,IAAI,IAAI,EAAE;MAC5BkB,gBAAgB,CAACd,kBAAkB,IAAI,IAAI,GAAGA,kBAAkB,GAAGJ,gBAAgB,GAAG,GAAG,CAAC;IAC5F;EACF,CAAC,EAAE,CAACA,gBAAgB,EAAEI,kBAAkB,EAAEc,gBAAgB,CAAC,CAAC;EAC5D,MAAMQ,gBAAgB,GAAGC,aAAa,IAAIX,KAAK,IAAI;IACjD,MAAMY,cAAc,GAAGD,aAAa,CAACE,MAAM;IAC3CD,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACZ,KAAK,CAAC;IAC/CQ,YAAY,CAAC,CAAC;EAChB,CAAC;EACD,MAAMM,iBAAiB,GAAGH,aAAa,IAAIX,KAAK,IAAI;IAClD,MAAMe,eAAe,GAAGJ,aAAa,CAACK,OAAO;IAC7CD,eAAe,IAAI,IAAI,IAAIA,eAAe,CAACf,KAAK,CAAC;IACjDO,WAAW,CAAC,CAAC;EACf,CAAC;EACD,MAAMU,gBAAgB,GAAGN,aAAa,IAAIX,KAAK,IAAI;IACjD,MAAMkB,oBAAoB,GAAGP,aAAa,CAACQ,YAAY;IACvDD,oBAAoB,IAAI,IAAI,IAAIA,oBAAoB,CAAClB,KAAK,CAAC;IAC3DO,WAAW,CAAC,CAAC;EACf,CAAC;EACD,MAAMa,gBAAgB,GAAGT,aAAa,IAAIX,KAAK,IAAI;IACjD,MAAMqB,oBAAoB,GAAGV,aAAa,CAACW,YAAY;IACvDD,oBAAoB,IAAI,IAAI,IAAIA,oBAAoB,CAACrB,KAAK,CAAC;IAC3DQ,YAAY,CAAC,CAAC;EAChB,CAAC;EACDhC,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACL,yBAAyB,IAAIE,IAAI,EAAE;MACtCoC,MAAM,CAAC1B,gBAAgB,CAAC,OAAO,EAAEW,YAAY,CAAC;MAC9Ce,MAAM,CAAC1B,gBAAgB,CAAC,MAAM,EAAEU,WAAW,CAAC;MAC5C,OAAO,MAAM;QACXgB,MAAM,CAACzB,mBAAmB,CAAC,OAAO,EAAEU,YAAY,CAAC;QACjDe,MAAM,CAACzB,mBAAmB,CAAC,MAAM,EAAES,WAAW,CAAC;MACjD,CAAC;IACH;IACA,OAAOhB,SAAS;EAClB,CAAC,EAAE,CAACN,yBAAyB,EAAEE,IAAI,EAAEqB,YAAY,EAAED,WAAW,CAAC,CAAC;EAChE,MAAMiB,YAAY,GAAGA,CAACC,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAMC,qBAAqB,GAAGnD,QAAQ,CAAC,CAAC,CAAC,EAAEM,oBAAoB,CAACE,UAAU,CAAC,EAAEF,oBAAoB,CAAC4C,aAAa,CAAC,CAAC;IACjH,OAAOlD,QAAQ,CAAC;MACd;MACA;MACAoD,IAAI,EAAE;IACR,CAAC,EAAEF,aAAa,EAAEC,qBAAqB,EAAE;MACvCb,MAAM,EAAEH,gBAAgB,CAACgB,qBAAqB,CAAC;MAC/CV,OAAO,EAAEF,iBAAiB,CAACY,qBAAqB,CAAC;MACjDP,YAAY,EAAEF,gBAAgB,CAACS,qBAAqB,CAAC;MACrDJ,YAAY,EAAEF,gBAAgB,CAACM,qBAAqB;IACtD,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLF,YAAY;IACZI,WAAW,EAAEtB;EACf,CAAC;AACH;AACA,eAAexB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}