{"ast": null, "code": "'use client';\n\nexport { default } from './FormGroup';\nexport { default as formGroupClasses } from './formGroupClasses';\nexport * from './formGroupClasses';", "map": {"version": 3, "names": ["default", "formGroupClasses"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/FormGroup/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './FormGroup';\nexport { default as formGroupClasses } from './formGroupClasses';\nexport * from './formGroupClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,oBAAoB;AAChE,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}