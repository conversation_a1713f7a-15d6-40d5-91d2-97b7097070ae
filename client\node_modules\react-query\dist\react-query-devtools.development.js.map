{"version": 3, "file": "react-query-devtools.development.js", "sources": ["../node_modules/@babel/runtime/helpers/esm/extends.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../node_modules/match-sorter/node_modules/@babel/runtime/helpers/esm/extends.js", "../node_modules/remove-accents/index.js", "../node_modules/match-sorter/dist/match-sorter.esm.js", "../src/devtools/useLocalStorage.ts", "../src/devtools/theme.tsx", "../src/devtools/useMediaQuery.ts", "../src/devtools/utils.ts", "../src/devtools/styledComponents.ts", "../src/devtools/Explorer.tsx", "../src/devtools/Logo.tsx", "../src/core/utils.ts", "../src/devtools/devtools.tsx"], "sourcesContent": ["export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "export default function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}", "export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "var characterMap = {\r\n\t\"À\": \"A\",\r\n\t\"Á\": \"A\",\r\n\t\"Â\": \"A\",\r\n\t\"Ã\": \"A\",\r\n\t\"Ä\": \"A\",\r\n\t\"Å\": \"A\",\r\n\t\"Ấ\": \"A\",\r\n\t\"Ắ\": \"A\",\r\n\t\"Ẳ\": \"A\",\r\n\t\"Ẵ\": \"A\",\r\n\t\"Ặ\": \"A\",\r\n\t\"Æ\": \"AE\",\r\n\t\"Ầ\": \"A\",\r\n\t\"Ằ\": \"A\",\r\n\t\"Ȃ\": \"A\",\r\n\t\"Ç\": \"C\",\r\n\t\"Ḉ\": \"C\",\r\n\t\"È\": \"E\",\r\n\t\"É\": \"E\",\r\n\t\"Ê\": \"E\",\r\n\t\"Ë\": \"E\",\r\n\t\"Ế\": \"E\",\r\n\t\"Ḗ\": \"E\",\r\n\t\"Ề\": \"E\",\r\n\t\"Ḕ\": \"E\",\r\n\t\"Ḝ\": \"E\",\r\n\t\"Ȇ\": \"E\",\r\n\t\"Ì\": \"I\",\r\n\t\"Í\": \"I\",\r\n\t\"Î\": \"I\",\r\n\t\"Ï\": \"I\",\r\n\t\"Ḯ\": \"I\",\r\n\t\"Ȋ\": \"I\",\r\n\t\"Ð\": \"D\",\r\n\t\"Ñ\": \"N\",\r\n\t\"Ò\": \"O\",\r\n\t\"Ó\": \"O\",\r\n\t\"Ô\": \"O\",\r\n\t\"Õ\": \"O\",\r\n\t\"Ö\": \"O\",\r\n\t\"Ø\": \"O\",\r\n\t\"Ố\": \"O\",\r\n\t\"Ṍ\": \"O\",\r\n\t\"Ṓ\": \"O\",\r\n\t\"Ȏ\": \"O\",\r\n\t\"Ù\": \"U\",\r\n\t\"Ú\": \"U\",\r\n\t\"Û\": \"U\",\r\n\t\"Ü\": \"U\",\r\n\t\"Ý\": \"Y\",\r\n\t\"à\": \"a\",\r\n\t\"á\": \"a\",\r\n\t\"â\": \"a\",\r\n\t\"ã\": \"a\",\r\n\t\"ä\": \"a\",\r\n\t\"å\": \"a\",\r\n\t\"ấ\": \"a\",\r\n\t\"ắ\": \"a\",\r\n\t\"ẳ\": \"a\",\r\n\t\"ẵ\": \"a\",\r\n\t\"ặ\": \"a\",\r\n\t\"æ\": \"ae\",\r\n\t\"ầ\": \"a\",\r\n\t\"ằ\": \"a\",\r\n\t\"ȃ\": \"a\",\r\n\t\"ç\": \"c\",\r\n\t\"ḉ\": \"c\",\r\n\t\"è\": \"e\",\r\n\t\"é\": \"e\",\r\n\t\"ê\": \"e\",\r\n\t\"ë\": \"e\",\r\n\t\"ế\": \"e\",\r\n\t\"ḗ\": \"e\",\r\n\t\"ề\": \"e\",\r\n\t\"ḕ\": \"e\",\r\n\t\"ḝ\": \"e\",\r\n\t\"ȇ\": \"e\",\r\n\t\"ì\": \"i\",\r\n\t\"í\": \"i\",\r\n\t\"î\": \"i\",\r\n\t\"ï\": \"i\",\r\n\t\"ḯ\": \"i\",\r\n\t\"ȋ\": \"i\",\r\n\t\"ð\": \"d\",\r\n\t\"ñ\": \"n\",\r\n\t\"ò\": \"o\",\r\n\t\"ó\": \"o\",\r\n\t\"ô\": \"o\",\r\n\t\"õ\": \"o\",\r\n\t\"ö\": \"o\",\r\n\t\"ø\": \"o\",\r\n\t\"ố\": \"o\",\r\n\t\"ṍ\": \"o\",\r\n\t\"ṓ\": \"o\",\r\n\t\"ȏ\": \"o\",\r\n\t\"ù\": \"u\",\r\n\t\"ú\": \"u\",\r\n\t\"û\": \"u\",\r\n\t\"ü\": \"u\",\r\n\t\"ý\": \"y\",\r\n\t\"ÿ\": \"y\",\r\n\t\"Ā\": \"A\",\r\n\t\"ā\": \"a\",\r\n\t\"Ă\": \"A\",\r\n\t\"ă\": \"a\",\r\n\t\"Ą\": \"A\",\r\n\t\"ą\": \"a\",\r\n\t\"Ć\": \"C\",\r\n\t\"ć\": \"c\",\r\n\t\"Ĉ\": \"C\",\r\n\t\"ĉ\": \"c\",\r\n\t\"Ċ\": \"C\",\r\n\t\"ċ\": \"c\",\r\n\t\"Č\": \"C\",\r\n\t\"č\": \"c\",\r\n\t\"C̆\": \"C\",\r\n\t\"c̆\": \"c\",\r\n\t\"Ď\": \"D\",\r\n\t\"ď\": \"d\",\r\n\t\"Đ\": \"D\",\r\n\t\"đ\": \"d\",\r\n\t\"Ē\": \"E\",\r\n\t\"ē\": \"e\",\r\n\t\"Ĕ\": \"E\",\r\n\t\"ĕ\": \"e\",\r\n\t\"Ė\": \"E\",\r\n\t\"ė\": \"e\",\r\n\t\"Ę\": \"E\",\r\n\t\"ę\": \"e\",\r\n\t\"Ě\": \"E\",\r\n\t\"ě\": \"e\",\r\n\t\"Ĝ\": \"G\",\r\n\t\"Ǵ\": \"G\",\r\n\t\"ĝ\": \"g\",\r\n\t\"ǵ\": \"g\",\r\n\t\"Ğ\": \"G\",\r\n\t\"ğ\": \"g\",\r\n\t\"Ġ\": \"G\",\r\n\t\"ġ\": \"g\",\r\n\t\"Ģ\": \"G\",\r\n\t\"ģ\": \"g\",\r\n\t\"Ĥ\": \"H\",\r\n\t\"ĥ\": \"h\",\r\n\t\"Ħ\": \"H\",\r\n\t\"ħ\": \"h\",\r\n\t\"Ḫ\": \"H\",\r\n\t\"ḫ\": \"h\",\r\n\t\"Ĩ\": \"I\",\r\n\t\"ĩ\": \"i\",\r\n\t\"Ī\": \"I\",\r\n\t\"ī\": \"i\",\r\n\t\"Ĭ\": \"I\",\r\n\t\"ĭ\": \"i\",\r\n\t\"Į\": \"I\",\r\n\t\"į\": \"i\",\r\n\t\"İ\": \"I\",\r\n\t\"ı\": \"i\",\r\n\t\"Ĳ\": \"IJ\",\r\n\t\"ĳ\": \"ij\",\r\n\t\"Ĵ\": \"J\",\r\n\t\"ĵ\": \"j\",\r\n\t\"Ķ\": \"K\",\r\n\t\"ķ\": \"k\",\r\n\t\"Ḱ\": \"K\",\r\n\t\"ḱ\": \"k\",\r\n\t\"K̆\": \"K\",\r\n\t\"k̆\": \"k\",\r\n\t\"Ĺ\": \"L\",\r\n\t\"ĺ\": \"l\",\r\n\t\"Ļ\": \"L\",\r\n\t\"ļ\": \"l\",\r\n\t\"Ľ\": \"L\",\r\n\t\"ľ\": \"l\",\r\n\t\"Ŀ\": \"L\",\r\n\t\"ŀ\": \"l\",\r\n\t\"Ł\": \"l\",\r\n\t\"ł\": \"l\",\r\n\t\"Ḿ\": \"M\",\r\n\t\"ḿ\": \"m\",\r\n\t\"M̆\": \"M\",\r\n\t\"m̆\": \"m\",\r\n\t\"Ń\": \"N\",\r\n\t\"ń\": \"n\",\r\n\t\"Ņ\": \"N\",\r\n\t\"ņ\": \"n\",\r\n\t\"Ň\": \"N\",\r\n\t\"ň\": \"n\",\r\n\t\"ŉ\": \"n\",\r\n\t\"N̆\": \"N\",\r\n\t\"n̆\": \"n\",\r\n\t\"Ō\": \"O\",\r\n\t\"ō\": \"o\",\r\n\t\"Ŏ\": \"O\",\r\n\t\"ŏ\": \"o\",\r\n\t\"Ő\": \"O\",\r\n\t\"ő\": \"o\",\r\n\t\"Œ\": \"OE\",\r\n\t\"œ\": \"oe\",\r\n\t\"P̆\": \"P\",\r\n\t\"p̆\": \"p\",\r\n\t\"Ŕ\": \"R\",\r\n\t\"ŕ\": \"r\",\r\n\t\"Ŗ\": \"R\",\r\n\t\"ŗ\": \"r\",\r\n\t\"Ř\": \"R\",\r\n\t\"ř\": \"r\",\r\n\t\"R̆\": \"R\",\r\n\t\"r̆\": \"r\",\r\n\t\"Ȓ\": \"R\",\r\n\t\"ȓ\": \"r\",\r\n\t\"Ś\": \"S\",\r\n\t\"ś\": \"s\",\r\n\t\"Ŝ\": \"S\",\r\n\t\"ŝ\": \"s\",\r\n\t\"Ş\": \"S\",\r\n\t\"Ș\": \"S\",\r\n\t\"ș\": \"s\",\r\n\t\"ş\": \"s\",\r\n\t\"Š\": \"S\",\r\n\t\"š\": \"s\",\r\n\t\"Ţ\": \"T\",\r\n\t\"ţ\": \"t\",\r\n\t\"ț\": \"t\",\r\n\t\"Ț\": \"T\",\r\n\t\"Ť\": \"T\",\r\n\t\"ť\": \"t\",\r\n\t\"Ŧ\": \"T\",\r\n\t\"ŧ\": \"t\",\r\n\t\"T̆\": \"T\",\r\n\t\"t̆\": \"t\",\r\n\t\"Ũ\": \"U\",\r\n\t\"ũ\": \"u\",\r\n\t\"Ū\": \"U\",\r\n\t\"ū\": \"u\",\r\n\t\"Ŭ\": \"U\",\r\n\t\"ŭ\": \"u\",\r\n\t\"Ů\": \"U\",\r\n\t\"ů\": \"u\",\r\n\t\"Ű\": \"U\",\r\n\t\"ű\": \"u\",\r\n\t\"Ų\": \"U\",\r\n\t\"ų\": \"u\",\r\n\t\"Ȗ\": \"U\",\r\n\t\"ȗ\": \"u\",\r\n\t\"V̆\": \"V\",\r\n\t\"v̆\": \"v\",\r\n\t\"Ŵ\": \"W\",\r\n\t\"ŵ\": \"w\",\r\n\t\"Ẃ\": \"W\",\r\n\t\"ẃ\": \"w\",\r\n\t\"X̆\": \"X\",\r\n\t\"x̆\": \"x\",\r\n\t\"Ŷ\": \"Y\",\r\n\t\"ŷ\": \"y\",\r\n\t\"Ÿ\": \"Y\",\r\n\t\"Y̆\": \"Y\",\r\n\t\"y̆\": \"y\",\r\n\t\"Ź\": \"Z\",\r\n\t\"ź\": \"z\",\r\n\t\"Ż\": \"Z\",\r\n\t\"ż\": \"z\",\r\n\t\"Ž\": \"Z\",\r\n\t\"ž\": \"z\",\r\n\t\"ſ\": \"s\",\r\n\t\"ƒ\": \"f\",\r\n\t\"Ơ\": \"O\",\r\n\t\"ơ\": \"o\",\r\n\t\"Ư\": \"U\",\r\n\t\"ư\": \"u\",\r\n\t\"Ǎ\": \"A\",\r\n\t\"ǎ\": \"a\",\r\n\t\"Ǐ\": \"I\",\r\n\t\"ǐ\": \"i\",\r\n\t\"Ǒ\": \"O\",\r\n\t\"ǒ\": \"o\",\r\n\t\"Ǔ\": \"U\",\r\n\t\"ǔ\": \"u\",\r\n\t\"Ǖ\": \"U\",\r\n\t\"ǖ\": \"u\",\r\n\t\"Ǘ\": \"U\",\r\n\t\"ǘ\": \"u\",\r\n\t\"Ǚ\": \"U\",\r\n\t\"ǚ\": \"u\",\r\n\t\"Ǜ\": \"U\",\r\n\t\"ǜ\": \"u\",\r\n\t\"Ứ\": \"U\",\r\n\t\"ứ\": \"u\",\r\n\t\"Ṹ\": \"U\",\r\n\t\"ṹ\": \"u\",\r\n\t\"Ǻ\": \"A\",\r\n\t\"ǻ\": \"a\",\r\n\t\"Ǽ\": \"AE\",\r\n\t\"ǽ\": \"ae\",\r\n\t\"Ǿ\": \"O\",\r\n\t\"ǿ\": \"o\",\r\n\t\"Þ\": \"TH\",\r\n\t\"þ\": \"th\",\r\n\t\"Ṕ\": \"P\",\r\n\t\"ṕ\": \"p\",\r\n\t\"Ṥ\": \"S\",\r\n\t\"ṥ\": \"s\",\r\n\t\"X́\": \"X\",\r\n\t\"x́\": \"x\",\r\n\t\"Ѓ\": \"Г\",\r\n\t\"ѓ\": \"г\",\r\n\t\"Ќ\": \"К\",\r\n\t\"ќ\": \"к\",\r\n\t\"A̋\": \"A\",\r\n\t\"a̋\": \"a\",\r\n\t\"E̋\": \"E\",\r\n\t\"e̋\": \"e\",\r\n\t\"I̋\": \"I\",\r\n\t\"i̋\": \"i\",\r\n\t\"Ǹ\": \"N\",\r\n\t\"ǹ\": \"n\",\r\n\t\"Ồ\": \"O\",\r\n\t\"ồ\": \"o\",\r\n\t\"Ṑ\": \"O\",\r\n\t\"ṑ\": \"o\",\r\n\t\"Ừ\": \"U\",\r\n\t\"ừ\": \"u\",\r\n\t\"Ẁ\": \"W\",\r\n\t\"ẁ\": \"w\",\r\n\t\"Ỳ\": \"Y\",\r\n\t\"ỳ\": \"y\",\r\n\t\"Ȁ\": \"A\",\r\n\t\"ȁ\": \"a\",\r\n\t\"Ȅ\": \"E\",\r\n\t\"ȅ\": \"e\",\r\n\t\"Ȉ\": \"I\",\r\n\t\"ȉ\": \"i\",\r\n\t\"Ȍ\": \"O\",\r\n\t\"ȍ\": \"o\",\r\n\t\"Ȑ\": \"R\",\r\n\t\"ȑ\": \"r\",\r\n\t\"Ȕ\": \"U\",\r\n\t\"ȕ\": \"u\",\r\n\t\"B̌\": \"B\",\r\n\t\"b̌\": \"b\",\r\n\t\"Č̣\": \"C\",\r\n\t\"č̣\": \"c\",\r\n\t\"Ê̌\": \"E\",\r\n\t\"ê̌\": \"e\",\r\n\t\"F̌\": \"F\",\r\n\t\"f̌\": \"f\",\r\n\t\"Ǧ\": \"G\",\r\n\t\"ǧ\": \"g\",\r\n\t\"Ȟ\": \"H\",\r\n\t\"ȟ\": \"h\",\r\n\t\"J̌\": \"J\",\r\n\t\"ǰ\": \"j\",\r\n\t\"Ǩ\": \"K\",\r\n\t\"ǩ\": \"k\",\r\n\t\"M̌\": \"M\",\r\n\t\"m̌\": \"m\",\r\n\t\"P̌\": \"P\",\r\n\t\"p̌\": \"p\",\r\n\t\"Q̌\": \"Q\",\r\n\t\"q̌\": \"q\",\r\n\t\"Ř̩\": \"R\",\r\n\t\"ř̩\": \"r\",\r\n\t\"Ṧ\": \"S\",\r\n\t\"ṧ\": \"s\",\r\n\t\"V̌\": \"V\",\r\n\t\"v̌\": \"v\",\r\n\t\"W̌\": \"W\",\r\n\t\"w̌\": \"w\",\r\n\t\"X̌\": \"X\",\r\n\t\"x̌\": \"x\",\r\n\t\"Y̌\": \"Y\",\r\n\t\"y̌\": \"y\",\r\n\t\"A̧\": \"A\",\r\n\t\"a̧\": \"a\",\r\n\t\"B̧\": \"B\",\r\n\t\"b̧\": \"b\",\r\n\t\"Ḑ\": \"D\",\r\n\t\"ḑ\": \"d\",\r\n\t\"Ȩ\": \"E\",\r\n\t\"ȩ\": \"e\",\r\n\t\"Ɛ̧\": \"E\",\r\n\t\"ɛ̧\": \"e\",\r\n\t\"Ḩ\": \"H\",\r\n\t\"ḩ\": \"h\",\r\n\t\"I̧\": \"I\",\r\n\t\"i̧\": \"i\",\r\n\t\"Ɨ̧\": \"I\",\r\n\t\"ɨ̧\": \"i\",\r\n\t\"M̧\": \"M\",\r\n\t\"m̧\": \"m\",\r\n\t\"O̧\": \"O\",\r\n\t\"o̧\": \"o\",\r\n\t\"Q̧\": \"Q\",\r\n\t\"q̧\": \"q\",\r\n\t\"U̧\": \"U\",\r\n\t\"u̧\": \"u\",\r\n\t\"X̧\": \"X\",\r\n\t\"x̧\": \"x\",\r\n\t\"Z̧\": \"Z\",\r\n\t\"z̧\": \"z\",\r\n};\r\n\r\nvar chars = Object.keys(characterMap).join('|');\r\nvar allAccents = new RegExp(chars, 'g');\r\nvar firstAccent = new RegExp(chars, '');\r\n\r\nvar removeAccents = function(string) {\t\r\n\treturn string.replace(allAccents, function(match) {\r\n\t\treturn characterMap[match];\r\n\t});\r\n};\r\n\r\nvar hasAccents = function(string) {\r\n\treturn !!string.match(firstAccent);\r\n};\r\n\r\nmodule.exports = removeAccents;\r\nmodule.exports.has = hasAccents;\r\nmodule.exports.remove = removeAccents;\r\n", "import _extends from '@babel/runtime/helpers/esm/extends';\nimport removeAccents from 'remove-accents';\n\nvar rankings = {\n  CASE_SENSITIVE_EQUAL: 7,\n  EQUAL: 6,\n  STARTS_WITH: 5,\n  WORD_STARTS_WITH: 4,\n  CONTAINS: 3,\n  ACRONYM: 2,\n  MATCHES: 1,\n  NO_MATCH: 0\n};\nmatchSorter.rankings = rankings;\n\nvar defaultBaseSortFn = function (a, b) {\n  return String(a.rankedValue).localeCompare(String(b.rankedValue));\n};\n/**\n * Takes an array of items and a value and returns a new array with the items that match the given value\n * @param {Array} items - the items to sort\n * @param {String} value - the value to use for ranking\n * @param {Object} options - Some options to configure the sorter\n * @return {Array} - the new sorted array\n */\n\n\nfunction matchSorter(items, value, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      keys = _options.keys,\n      _options$threshold = _options.threshold,\n      threshold = _options$threshold === void 0 ? rankings.MATCHES : _options$threshold,\n      _options$baseSort = _options.baseSort,\n      baseSort = _options$baseSort === void 0 ? defaultBaseSortFn : _options$baseSort;\n  var matchedItems = items.reduce(reduceItemsToRanked, []);\n  return matchedItems.sort(function (a, b) {\n    return sortRankedValues(a, b, baseSort);\n  }).map(function (_ref) {\n    var item = _ref.item;\n    return item;\n  });\n\n  function reduceItemsToRanked(matches, item, index) {\n    var rankingInfo = getHighestRanking(item, keys, value, options);\n    var rank = rankingInfo.rank,\n        _rankingInfo$keyThres = rankingInfo.keyThreshold,\n        keyThreshold = _rankingInfo$keyThres === void 0 ? threshold : _rankingInfo$keyThres;\n\n    if (rank >= keyThreshold) {\n      matches.push(_extends({}, rankingInfo, {\n        item: item,\n        index: index\n      }));\n    }\n\n    return matches;\n  }\n}\n/**\n * Gets the highest ranking for value for the given item based on its values for the given keys\n * @param {*} item - the item to rank\n * @param {Array} keys - the keys to get values from the item for the ranking\n * @param {String} value - the value to rank against\n * @param {Object} options - options to control the ranking\n * @return {{rank: Number, keyIndex: Number, keyThreshold: Number}} - the highest ranking\n */\n\n\nfunction getHighestRanking(item, keys, value, options) {\n  if (!keys) {\n    // if keys is not specified, then we assume the item given is ready to be matched\n    var stringItem = item;\n    return {\n      // ends up being duplicate of 'item' in matches but consistent\n      rankedValue: stringItem,\n      rank: getMatchRanking(stringItem, value, options),\n      keyIndex: -1,\n      keyThreshold: options.threshold\n    };\n  }\n\n  var valuesToRank = getAllValuesToRank(item, keys);\n  return valuesToRank.reduce(function (_ref2, _ref3, i) {\n    var rank = _ref2.rank,\n        rankedValue = _ref2.rankedValue,\n        keyIndex = _ref2.keyIndex,\n        keyThreshold = _ref2.keyThreshold;\n    var itemValue = _ref3.itemValue,\n        attributes = _ref3.attributes;\n    var newRank = getMatchRanking(itemValue, value, options);\n    var newRankedValue = rankedValue;\n    var minRanking = attributes.minRanking,\n        maxRanking = attributes.maxRanking,\n        threshold = attributes.threshold;\n\n    if (newRank < minRanking && newRank >= rankings.MATCHES) {\n      newRank = minRanking;\n    } else if (newRank > maxRanking) {\n      newRank = maxRanking;\n    }\n\n    if (newRank > rank) {\n      rank = newRank;\n      keyIndex = i;\n      keyThreshold = threshold;\n      newRankedValue = itemValue;\n    }\n\n    return {\n      rankedValue: newRankedValue,\n      rank: rank,\n      keyIndex: keyIndex,\n      keyThreshold: keyThreshold\n    };\n  }, {\n    rankedValue: item,\n    rank: rankings.NO_MATCH,\n    keyIndex: -1,\n    keyThreshold: options.threshold\n  });\n}\n/**\n * Gives a rankings score based on how well the two strings match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @param {Object} options - options for the match (like keepDiacritics for comparison)\n * @returns {Number} the ranking for how well stringToRank matches testString\n */\n\n\nfunction getMatchRanking(testString, stringToRank, options) {\n  testString = prepareValueForComparison(testString, options);\n  stringToRank = prepareValueForComparison(stringToRank, options); // too long\n\n  if (stringToRank.length > testString.length) {\n    return rankings.NO_MATCH;\n  } // case sensitive equals\n\n\n  if (testString === stringToRank) {\n    return rankings.CASE_SENSITIVE_EQUAL;\n  } // Lower casing before further comparison\n\n\n  testString = testString.toLowerCase();\n  stringToRank = stringToRank.toLowerCase(); // case insensitive equals\n\n  if (testString === stringToRank) {\n    return rankings.EQUAL;\n  } // starts with\n\n\n  if (testString.startsWith(stringToRank)) {\n    return rankings.STARTS_WITH;\n  } // word starts with\n\n\n  if (testString.includes(\" \" + stringToRank)) {\n    return rankings.WORD_STARTS_WITH;\n  } // contains\n\n\n  if (testString.includes(stringToRank)) {\n    return rankings.CONTAINS;\n  } else if (stringToRank.length === 1) {\n    // If the only character in the given stringToRank\n    //   isn't even contained in the testString, then\n    //   it's definitely not a match.\n    return rankings.NO_MATCH;\n  } // acronym\n\n\n  if (getAcronym(testString).includes(stringToRank)) {\n    return rankings.ACRONYM;\n  } // will return a number between rankings.MATCHES and\n  // rankings.MATCHES + 1 depending  on how close of a match it is.\n\n\n  return getClosenessRanking(testString, stringToRank);\n}\n/**\n * Generates an acronym for a string.\n *\n * @param {String} string the string for which to produce the acronym\n * @returns {String} the acronym\n */\n\n\nfunction getAcronym(string) {\n  var acronym = '';\n  var wordsInString = string.split(' ');\n  wordsInString.forEach(function (wordInString) {\n    var splitByHyphenWords = wordInString.split('-');\n    splitByHyphenWords.forEach(function (splitByHyphenWord) {\n      acronym += splitByHyphenWord.substr(0, 1);\n    });\n  });\n  return acronym;\n}\n/**\n * Returns a score based on how spread apart the\n * characters from the stringToRank are within the testString.\n * A number close to rankings.MATCHES represents a loose match. A number close\n * to rankings.MATCHES + 1 represents a tighter match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @returns {Number} the number between rankings.MATCHES and\n * rankings.MATCHES + 1 for how well stringToRank matches testString\n */\n\n\nfunction getClosenessRanking(testString, stringToRank) {\n  var matchingInOrderCharCount = 0;\n  var charNumber = 0;\n\n  function findMatchingCharacter(matchChar, string, index) {\n    for (var j = index; j < string.length; j++) {\n      var stringChar = string[j];\n\n      if (stringChar === matchChar) {\n        matchingInOrderCharCount += 1;\n        return j + 1;\n      }\n    }\n\n    return -1;\n  }\n\n  function getRanking(spread) {\n    var inOrderPercentage = matchingInOrderCharCount / stringToRank.length;\n    var ranking = rankings.MATCHES + inOrderPercentage * (1 / spread);\n    return ranking;\n  }\n\n  var firstIndex = findMatchingCharacter(stringToRank[0], testString, 0);\n\n  if (firstIndex < 0) {\n    return rankings.NO_MATCH;\n  }\n\n  charNumber = firstIndex;\n\n  for (var i = 1; i < stringToRank.length; i++) {\n    var matchChar = stringToRank[i];\n    charNumber = findMatchingCharacter(matchChar, testString, charNumber);\n    var found = charNumber > -1;\n\n    if (!found) {\n      return rankings.NO_MATCH;\n    }\n  }\n\n  var spread = charNumber - firstIndex;\n  return getRanking(spread);\n}\n/**\n * Sorts items that have a rank, index, and keyIndex\n * @param {Object} a - the first item to sort\n * @param {Object} b - the second item to sort\n * @return {Number} -1 if a should come first, 1 if b should come first, 0 if equal\n */\n\n\nfunction sortRankedValues(a, b, baseSort) {\n  var aFirst = -1;\n  var bFirst = 1;\n  var aRank = a.rank,\n      aKeyIndex = a.keyIndex;\n  var bRank = b.rank,\n      bKeyIndex = b.keyIndex;\n\n  if (aRank === bRank) {\n    if (aKeyIndex === bKeyIndex) {\n      // use the base sort function as a tie-breaker\n      return baseSort(a, b);\n    } else {\n      return aKeyIndex < bKeyIndex ? aFirst : bFirst;\n    }\n  } else {\n    return aRank > bRank ? aFirst : bFirst;\n  }\n}\n/**\n * Prepares value for comparison by stringifying it, removing diacritics (if specified)\n * @param {String} value - the value to clean\n * @param {Object} options - {keepDiacritics: whether to remove diacritics}\n * @return {String} the prepared value\n */\n\n\nfunction prepareValueForComparison(value, _ref4) {\n  var keepDiacritics = _ref4.keepDiacritics;\n  // value might not actually be a string at this point (we don't get to choose)\n  // so part of preparing the value for comparison is ensure that it is a string\n  value = \"\" + value; // toString\n\n  if (!keepDiacritics) {\n    value = removeAccents(value);\n  }\n\n  return value;\n}\n/**\n * Gets value for key in item at arbitrarily nested keypath\n * @param {Object} item - the item\n * @param {Object|Function} key - the potentially nested keypath or property callback\n * @return {Array} - an array containing the value(s) at the nested keypath\n */\n\n\nfunction getItemValues(item, key) {\n  if (typeof key === 'object') {\n    key = key.key;\n  }\n\n  var value;\n\n  if (typeof key === 'function') {\n    value = key(item); // eslint-disable-next-line no-negated-condition\n  } else {\n    value = getNestedValue(key, item);\n  }\n\n  // concat because `value` can be a string or an array\n  // eslint-disable-next-line\n  return value != null ? [].concat(value) : null;\n}\n/**\n * Given key: \"foo.bar.baz\"\n * And obj: {foo: {bar: {baz: 'buzz'}}}\n *   -> 'buzz'\n * @param key a dot-separated set of keys\n * @param obj the object to get the value from\n */\n\n\nfunction getNestedValue(key, obj) {\n  // @ts-expect-error really have no idea how to type this properly...\n  return key.split('.').reduce(function (itemObj, nestedKey) {\n    // @ts-expect-error lost on this one as well...\n    return itemObj ? itemObj[nestedKey] : null;\n  }, obj);\n}\n/**\n * Gets all the values for the given keys in the given item and returns an array of those values\n * @param item - the item from which the values will be retrieved\n * @param keys - the keys to use to retrieve the values\n * @return objects with {itemValue, attributes}\n */\n\n\nfunction getAllValuesToRank(item, keys) {\n  return keys.reduce(function (allVals, key) {\n    var values = getItemValues(item, key);\n\n    if (values) {\n      values.forEach(function (itemValue) {\n        allVals.push({\n          itemValue: itemValue,\n          attributes: getKeyAttributes(key)\n        });\n      });\n    }\n\n    return allVals;\n  }, []);\n}\n\nvar defaultKeyAttributes = {\n  maxRanking: Infinity,\n  minRanking: -Infinity\n};\n/**\n * Gets all the attributes for the given key\n * @param key - the key from which the attributes will be retrieved\n * @return object containing the key's attributes\n */\n\nfunction getKeyAttributes(key) {\n  if (typeof key === 'string') {\n    return defaultKeyAttributes;\n  }\n\n  return _extends({}, defaultKeyAttributes, key);\n}\n\nexport { defaultBaseSortFn, matchSorter, rankings };\n", "import React from 'react'\n\nconst getItem = (key: string): unknown => {\n  try {\n    const itemValue = localStorage.getItem(key)\n    if (typeof itemValue === 'string') {\n      return JSON.parse(itemValue)\n    }\n    return undefined\n  } catch {\n    return undefined\n  }\n}\n\nexport default function useLocalStorage<T>(\n  key: string,\n  defaultValue: T | undefined\n): [T | undefined, (newVal: T | ((prevVal: T) => T)) => void] {\n  const [value, setValue] = React.useState<T>()\n\n  React.useEffect(() => {\n    const initialValue = getItem(key) as T | undefined\n\n    if (typeof initialValue === 'undefined' || initialValue === null) {\n      setValue(\n        typeof defaultValue === 'function' ? defaultValue() : defaultValue\n      )\n    } else {\n      setValue(initialValue)\n    }\n  }, [defaultValue, key])\n\n  const setter = React.useCallback(\n    updater => {\n      setValue(old => {\n        let newVal = updater\n\n        if (typeof updater == 'function') {\n          newVal = updater(old)\n        }\n        try {\n          localStorage.setItem(key, JSON.stringify(newVal))\n        } catch {}\n\n        return newVal\n      })\n    },\n    [key]\n  )\n\n  return [value, setter]\n}\n", "import React from 'react'\n\nexport const defaultTheme = {\n  background: '#0b1521',\n  backgroundAlt: '#132337',\n  foreground: 'white',\n  gray: '#3f4e60',\n  grayAlt: '#222e3e',\n  inputBackgroundColor: '#fff',\n  inputTextColor: '#000',\n  success: '#00ab52',\n  danger: '#ff0085',\n  active: '#006bff',\n  warning: '#ffb200',\n} as const\n\nexport type Theme = typeof defaultTheme\ninterface ProviderProps {\n  theme: Theme\n  children?: React.ReactNode\n}\n\nconst ThemeContext = React.createContext(defaultTheme)\n\nexport function ThemeProvider({ theme, ...rest }: ProviderProps) {\n  return <ThemeContext.Provider value={theme} {...rest} />\n}\n\nexport function useTheme() {\n  return React.useContext(ThemeContext)\n}\n", "import React from 'react'\n\nexport default function useMediaQuery(query: string): boolean | undefined {\n  // Keep track of the preference in state, start with the current match\n  const [isMatch, setIsMatch] = React.useState(() => {\n    if (typeof window !== 'undefined') {\n      return window.matchMedia && window.matchMedia(query).matches\n    }\n  })\n\n  // Watch for changes\n  React.useEffect(() => {\n    if (typeof window !== 'undefined') {\n      if (!window.matchMedia) {\n        return\n      }\n\n      // Create a matcher\n      const matcher = window.matchMedia(query)\n\n      // Create our handler\n      const onChange = ({ matches }: { matches: boolean }) =>\n        setIsMatch(matches)\n\n      // Listen for changes\n      matcher.addListener(onChange)\n\n      return () => {\n        // Stop listening for changes\n        matcher.removeListener(onChange)\n      }\n    }\n  }, [isMatch, query, setIsMatch])\n\n  return isMatch\n}\n", "import React from 'react'\nimport { Query } from '../core'\n\nimport { Theme, useTheme } from './theme'\nimport useMediaQuery from './useMediaQuery'\n\nexport const isServer = typeof window === 'undefined'\n\ntype StyledComponent<T> = T extends 'button'\n  ? React.DetailedHTMLProps<\n      React.ButtonHTMLAttributes<HTMLButtonElement>,\n      HTMLButtonElement\n    >\n  : T extends 'input'\n  ? React.DetailedHTMLProps<\n      React.InputHTMLAttributes<HTMLInputElement>,\n      HTMLInputElement\n    >\n  : T extends 'select'\n  ? React.DetailedHTMLProps<\n      React.SelectHTMLAttributes<HTMLSelectElement>,\n      HTMLSelectElement\n    >\n  : T extends keyof HTMLElementTagNameMap\n  ? React.HTMLAttributes<HTMLElementTagNameMap[T]>\n  : never\n\nexport function getQueryStatusColor(query: Query, theme: Theme) {\n  return query.state.isFetching\n    ? theme.active\n    : !query.getObserversCount()\n    ? theme.gray\n    : query.isStale()\n    ? theme.warning\n    : theme.success\n}\n\nexport function getQueryStatusLabel(query: Query) {\n  return query.state.isFetching\n    ? 'fetching'\n    : !query.getObserversCount()\n    ? 'inactive'\n    : query.isStale()\n    ? 'stale'\n    : 'fresh'\n}\n\ntype Styles =\n  | React.CSSProperties\n  | ((props: Record<string, any>, theme: Theme) => React.CSSProperties)\n\nexport function styled<T extends keyof HTMLElementTagNameMap>(\n  type: T,\n  newStyles: Styles,\n  queries: Record<string, Styles> = {}\n) {\n  return React.forwardRef<HTMLElementTagNameMap[T], StyledComponent<T>>(\n    ({ style, ...rest }, ref) => {\n      const theme = useTheme()\n\n      const mediaStyles = Object.entries(queries).reduce(\n        (current, [key, value]) => {\n          // eslint-disable-next-line react-hooks/rules-of-hooks\n          return useMediaQuery(key)\n            ? {\n                ...current,\n                ...(typeof value === 'function' ? value(rest, theme) : value),\n              }\n            : current\n        },\n        {}\n      )\n\n      return React.createElement(type, {\n        ...rest,\n        style: {\n          ...(typeof newStyles === 'function'\n            ? newStyles(rest, theme)\n            : newStyles),\n          ...style,\n          ...mediaStyles,\n        },\n        ref,\n      })\n    }\n  )\n}\n\nexport function useIsMounted() {\n  const mountedRef = React.useRef(false)\n  const isMounted = React.useCallback(() => mountedRef.current, [])\n\n  React[isServer ? 'useEffect' : 'useLayoutEffect'](() => {\n    mountedRef.current = true\n    return () => {\n      mountedRef.current = false\n    }\n  }, [])\n\n  return isMounted\n}\n\n/**\n * This hook is a safe useState version which schedules state updates in microtasks\n * to prevent updating a component state while React is rendering different components\n * or when the component is not mounted anymore.\n */\nexport function useSafeState<T>(initialState: T): [T, (value: T) => void] {\n  const isMounted = useIsMounted()\n  const [state, setState] = React.useState(initialState)\n\n  const safeSetState = React.useCallback(\n    (value: T) => {\n      scheduleMicrotask(() => {\n        if (isMounted()) {\n          setState(value)\n        }\n      })\n    },\n    [isMounted]\n  )\n\n  return [state, safeSetState]\n}\n\n/**\n * Displays a string regardless the type of the data\n * @param {unknown} value Value to be stringified\n */\nexport const displayValue = (value: unknown) => {\n  const name = Object.getOwnPropertyNames(Object(value))\n  const newValue = typeof value === 'bigint' ? `${value.toString()}n` : value\n\n  return JSON.stringify(newValue, name)\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nfunction scheduleMicrotask(callback: () => void) {\n  Promise.resolve()\n    .then(callback)\n    .catch(error =>\n      setTimeout(() => {\n        throw error\n      })\n    )\n}\n", "import { styled } from './utils'\n\nexport const Panel = styled(\n  'div',\n  (_props, theme) => ({\n    fontSize: 'clamp(12px, 1.5vw, 14px)',\n    fontFamily: `sans-serif`,\n    display: 'flex',\n    backgroundColor: theme.background,\n    color: theme.foreground,\n  }),\n  {\n    '(max-width: 700px)': {\n      flexDirection: 'column',\n    },\n    '(max-width: 600px)': {\n      fontSize: '.9em',\n      // flexDirection: 'column',\n    },\n  }\n)\n\nexport const ActiveQueryPanel = styled(\n  'div',\n  () => ({\n    flex: '1 1 500px',\n    display: 'flex',\n    flexDirection: 'column',\n    overflow: 'auto',\n    height: '100%',\n  }),\n  {\n    '(max-width: 700px)': (_props, theme) => ({\n      borderTop: `2px solid ${theme.gray}`,\n    }),\n  }\n)\n\nexport const Button = styled('button', (props, theme) => ({\n  appearance: 'none',\n  fontSize: '.9em',\n  fontWeight: 'bold',\n  background: theme.gray,\n  border: '0',\n  borderRadius: '.3em',\n  color: 'white',\n  padding: '.5em',\n  opacity: props.disabled ? '.5' : undefined,\n  cursor: 'pointer',\n}))\n\nexport const QueryKeys = styled('span', {\n  display: 'inline-block',\n  fontSize: '0.9em',\n})\n\nexport const QueryKey = styled('span', {\n  display: 'inline-flex',\n  alignItems: 'center',\n  padding: '.2em .4em',\n  fontWeight: 'bold',\n  textShadow: '0 0 10px black',\n  borderRadius: '.2em',\n})\n\nexport const Code = styled('code', {\n  fontSize: '.9em',\n  color: 'inherit',\n  background: 'inherit',\n})\n\nexport const Input = styled('input', (_props, theme) => ({\n  backgroundColor: theme.inputBackgroundColor,\n  border: 0,\n  borderRadius: '.2em',\n  color: theme.inputTextColor,\n  fontSize: '.9em',\n  lineHeight: `1.3`,\n  padding: '.3em .4em',\n}))\n\nexport const Select = styled(\n  'select',\n  (_props, theme) => ({\n    display: `inline-block`,\n    fontSize: `.9em`,\n    fontFamily: `sans-serif`,\n    fontWeight: 'normal',\n    lineHeight: `1.3`,\n    padding: `.3em 1.5em .3em .5em`,\n    height: 'auto',\n    border: 0,\n    borderRadius: `.2em`,\n    appearance: `none`,\n    WebkitAppearance: 'none',\n    backgroundColor: theme.inputBackgroundColor,\n    backgroundImage: `url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>\")`,\n    backgroundRepeat: `no-repeat`,\n    backgroundPosition: `right .55em center`,\n    backgroundSize: `.65em auto, 100%`,\n    color: theme.inputTextColor,\n  }),\n  {\n    '(max-width: 500px)': {\n      display: 'none',\n    },\n  }\n)\n", "import React from 'react'\n\nimport { displayValue, styled } from './utils'\n\nexport const Entry = styled('div', {\n  fontFamily: 'Menlo, monospace',\n  fontSize: '1em',\n  lineHeight: '1.7',\n  outline: 'none',\n  wordBreak: 'break-word',\n})\n\nexport const Label = styled('span', {\n  color: 'white',\n})\n\nexport const LabelButton = styled('button', {\n  cursor: 'pointer',\n  color: 'white',\n})\n\nexport const ExpandButton = styled('button', {\n  cursor: 'pointer',\n  color: 'inherit',\n  font: 'inherit',\n  outline: 'inherit',\n  background: 'transparent',\n  border: 'none',\n  padding: 0,\n})\n\nexport const Value = styled('span', (_props, theme) => ({\n  color: theme.danger,\n}))\n\nexport const SubEntries = styled('div', {\n  marginLeft: '.1em',\n  paddingLeft: '1em',\n  borderLeft: '2px solid rgba(0,0,0,.15)',\n})\n\nexport const Info = styled('span', {\n  color: 'grey',\n  fontSize: '.7em',\n})\n\ntype ExpanderProps = {\n  expanded: boolean\n  style?: React.CSSProperties\n}\n\nexport const Expander = ({ expanded, style = {} }: ExpanderProps) => (\n  <span\n    style={{\n      display: 'inline-block',\n      transition: 'all .1s ease',\n      transform: `rotate(${expanded ? 90 : 0}deg) ${style.transform || ''}`,\n      ...style,\n    }}\n  >\n    ▶\n  </span>\n)\n\ntype Entry = {\n  label: string\n}\n\ntype RendererProps = {\n  HandleEntry: HandleEntryComponent\n  label?: string\n  value: unknown\n  subEntries: Entry[]\n  subEntryPages: Entry[][]\n  type: string\n  expanded: boolean\n  toggleExpanded: () => void\n  pageSize: number\n}\n\n/**\n * Chunk elements in the array by size\n *\n * when the array cannot be chunked evenly by size, the last chunk will be\n * filled with the remaining elements\n *\n * @example\n * chunkArray(['a','b', 'c', 'd', 'e'], 2) // returns [['a','b'], ['c', 'd'], ['e']]\n */\nexport function chunkArray<T>(array: T[], size: number): T[][] {\n  if (size < 1) return []\n  let i = 0\n  const result: T[][] = []\n  while (i < array.length) {\n    result.push(array.slice(i, i + size))\n    i = i + size\n  }\n  return result\n}\n\ntype Renderer = (props: RendererProps) => JSX.Element\n\nexport const DefaultRenderer: Renderer = ({\n  HandleEntry,\n  label,\n  value,\n  subEntries = [],\n  subEntryPages = [],\n  type,\n  expanded = false,\n  toggleExpanded,\n  pageSize,\n}) => {\n  const [expandedPages, setExpandedPages] = React.useState<number[]>([])\n\n  return (\n    <Entry key={label}>\n      {subEntryPages?.length ? (\n        <>\n          <ExpandButton onClick={() => toggleExpanded()}>\n            <Expander expanded={expanded} /> {label}{' '}\n            <Info>\n              {String(type).toLowerCase() === 'iterable' ? '(Iterable) ' : ''}\n              {subEntries.length} {subEntries.length > 1 ? `items` : `item`}\n            </Info>\n          </ExpandButton>\n          {expanded ? (\n            subEntryPages.length === 1 ? (\n              <SubEntries>\n                {subEntries.map(entry => (\n                  <HandleEntry key={entry.label} entry={entry} />\n                ))}\n              </SubEntries>\n            ) : (\n              <SubEntries>\n                {subEntryPages.map((entries, index) => (\n                  <div key={index}>\n                    <Entry>\n                      <LabelButton\n                        onClick={() =>\n                          setExpandedPages(old =>\n                            old.includes(index)\n                              ? old.filter(d => d !== index)\n                              : [...old, index]\n                          )\n                        }\n                      >\n                        <Expander expanded={expanded} /> [{index * pageSize} ...{' '}\n                        {index * pageSize + pageSize - 1}]\n                      </LabelButton>\n                      {expandedPages.includes(index) ? (\n                        <SubEntries>\n                          {entries.map(entry => (\n                            <HandleEntry key={entry.label} entry={entry} />\n                          ))}\n                        </SubEntries>\n                      ) : null}\n                    </Entry>\n                  </div>\n                ))}\n              </SubEntries>\n            )\n          ) : null}\n        </>\n      ) : (\n        <>\n          <Label>{label}:</Label> <Value>{displayValue(value)}</Value>\n        </>\n      )}\n    </Entry>\n  )\n}\n\ntype HandleEntryComponent = (props: { entry: Entry }) => JSX.Element\n\ntype ExplorerProps = Partial<RendererProps> & {\n  renderer?: Renderer\n  defaultExpanded?: true | Record<string, boolean>\n}\n\ntype Property = {\n  defaultExpanded?: boolean | Record<string, boolean>\n  label: string\n  value: unknown\n}\n\nfunction isIterable(x: any): x is Iterable<unknown> {\n  return Symbol.iterator in x\n}\n\nexport default function Explorer({\n  value,\n  defaultExpanded,\n  renderer = DefaultRenderer,\n  pageSize = 100,\n  ...rest\n}: ExplorerProps) {\n  const [expanded, setExpanded] = React.useState(Boolean(defaultExpanded))\n  const toggleExpanded = React.useCallback(() => setExpanded(old => !old), [])\n\n  let type: string = typeof value\n  let subEntries: Property[] = []\n\n  const makeProperty = (sub: { label: string; value: unknown }): Property => {\n    const subDefaultExpanded =\n      defaultExpanded === true\n        ? { [sub.label]: true }\n        : defaultExpanded?.[sub.label]\n    return {\n      ...sub,\n      defaultExpanded: subDefaultExpanded,\n    }\n  }\n\n  if (Array.isArray(value)) {\n    type = 'array'\n    subEntries = value.map((d, i) =>\n      makeProperty({\n        label: i.toString(),\n        value: d,\n      })\n    )\n  } else if (\n    value !== null &&\n    typeof value === 'object' &&\n    isIterable(value) &&\n    typeof value[Symbol.iterator] === 'function'\n  ) {\n    type = 'Iterable'\n    subEntries = Array.from(value, (val, i) =>\n      makeProperty({\n        label: i.toString(),\n        value: val,\n      })\n    )\n  } else if (typeof value === 'object' && value !== null) {\n    type = 'object'\n    subEntries = Object.entries(value).map(([key, val]) =>\n      makeProperty({\n        label: key,\n        value: val,\n      })\n    )\n  }\n\n  const subEntryPages = chunkArray(subEntries, pageSize)\n\n  return renderer({\n    HandleEntry: ({ entry }) => (\n      <Explorer value={value} renderer={renderer} {...rest} {...entry} />\n    ),\n    type,\n    subEntries,\n    subEntryPages,\n    value,\n    expanded,\n    toggleExpanded,\n    pageSize,\n    ...rest,\n  })\n}\n", "import * as React from 'react'\n\nexport default function Logo(props: any) {\n  return (\n    <svg\n      width=\"40px\"\n      height=\"40px\"\n      viewBox=\"0 0 190 190\"\n      version=\"1.1\"\n      {...props}\n    >\n      <g stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n        <g transform=\"translate(-33.000000, 0.000000)\">\n          <path\n            d=\"M72.7239712,61.3436237 C69.631224,46.362877 68.9675112,34.8727722 70.9666331,26.5293551 C72.1555965,21.5671678 74.3293088,17.5190846 77.6346064,14.5984631 C81.1241394,11.5150478 85.5360327,10.0020122 90.493257,10.0020122 C98.6712013,10.0020122 107.26826,13.7273214 116.455725,20.8044264 C120.20312,23.6910458 124.092437,27.170411 128.131651,31.2444746 C128.45314,30.8310265 128.816542,30.4410453 129.22143,30.0806152 C140.64098,19.9149716 150.255245,13.5989272 158.478408,11.1636507 C163.367899,9.715636 167.958526,9.57768202 172.138936,10.983031 C176.551631,12.4664684 180.06766,15.5329489 182.548314,19.8281091 C186.642288,26.9166735 187.721918,36.2310983 186.195595,47.7320243 C185.573451,52.4199112 184.50985,57.5263831 183.007094,63.0593153 C183.574045,63.1277086 184.142416,63.2532808 184.705041,63.4395297 C199.193932,68.2358678 209.453582,73.3937462 215.665021,79.2882839 C219.360669,82.7953831 221.773972,86.6998434 222.646365,91.0218204 C223.567176,95.5836746 222.669313,100.159332 220.191548,104.451297 C216.105211,111.529614 208.591643,117.11221 197.887587,121.534031 C193.589552,123.309539 188.726579,124.917559 183.293259,126.363748 C183.541176,126.92292 183.733521,127.516759 183.862138,128.139758 C186.954886,143.120505 187.618598,154.61061 185.619477,162.954027 C184.430513,167.916214 182.256801,171.964297 178.951503,174.884919 C175.46197,177.968334 171.050077,179.48137 166.092853,179.48137 C157.914908,179.48137 149.31785,175.756061 140.130385,168.678956 C136.343104,165.761613 132.410866,162.238839 128.325434,158.108619 C127.905075,158.765474 127.388968,159.376011 126.77857,159.919385 C115.35902,170.085028 105.744755,176.401073 97.5215915,178.836349 C92.6321009,180.284364 88.0414736,180.422318 83.8610636,179.016969 C79.4483686,177.533532 75.9323404,174.467051 73.4516862,170.171891 C69.3577116,163.083327 68.2780823,153.768902 69.8044053,142.267976 C70.449038,137.410634 71.56762,132.103898 73.1575891,126.339009 C72.5361041,126.276104 71.9120754,126.144816 71.2949591,125.940529 C56.8060684,121.144191 46.5464184,115.986312 40.3349789,110.091775 C36.6393312,106.584675 34.2260275,102.680215 33.3536352,98.3582381 C32.4328237,93.7963839 33.3306866,89.2207269 35.8084524,84.9287618 C39.8947886,77.8504443 47.4083565,72.2678481 58.1124133,67.8460273 C62.5385143,66.0176154 67.5637208,64.366822 73.1939394,62.8874674 C72.9933393,62.3969171 72.8349374,61.8811235 72.7239712,61.3436237 Z\"\n            fill=\"#002C4B\"\n            fillRule=\"nonzero\"\n            transform=\"translate(128.000000, 95.000000) scale(-1, 1) translate(-128.000000, -95.000000) \"\n          ></path>\n          <path\n            d=\"M113.396882,64 L142.608177,64 C144.399254,64 146.053521,64.958025 146.944933,66.5115174 L161.577138,92.0115174 C162.461464,93.5526583 162.461464,95.4473417 161.577138,96.9884826 L146.944933,122.488483 C146.053521,124.041975 144.399254,125 142.608177,125 L113.396882,125 C111.605806,125 109.951539,124.041975 109.060126,122.488483 L94.4279211,96.9884826 C93.543596,95.4473417 93.543596,93.5526583 94.4279211,92.0115174 L109.060126,66.5115174 C109.951539,64.958025 111.605806,64 113.396882,64 Z M138.987827,70.2765273 C140.779849,70.2765273 142.434839,71.2355558 143.325899,72.7903404 L154.343038,92.0138131 C155.225607,93.5537825 155.225607,95.4462175 154.343038,96.9861869 L143.325899,116.20966 C142.434839,117.764444 140.779849,118.723473 138.987827,118.723473 L117.017233,118.723473 C115.225211,118.723473 113.570221,117.764444 112.67916,116.20966 L101.662022,96.9861869 C100.779452,95.4462175 100.779452,93.5537825 101.662022,92.0138131 L112.67916,72.7903404 C113.570221,71.2355558 115.225211,70.2765273 117.017233,70.2765273 L138.987827,70.2765273 Z M135.080648,77.1414791 L120.924411,77.1414791 C119.134228,77.1414791 117.480644,78.0985567 116.5889,79.6508285 L116.5889,79.6508285 L109.489217,92.0093494 C108.603232,93.5515958 108.603232,95.4484042 109.489217,96.9906506 L109.489217,96.9906506 L116.5889,109.349172 C117.480644,110.901443 119.134228,111.858521 120.924411,111.858521 L120.924411,111.858521 L135.080648,111.858521 C136.870831,111.858521 138.524416,110.901443 139.41616,109.349172 L139.41616,109.349172 L146.515843,96.9906506 C147.401828,95.4484042 147.401828,93.5515958 146.515843,92.0093494 L146.515843,92.0093494 L139.41616,79.6508285 C138.524416,78.0985567 136.870831,77.1414791 135.080648,77.1414791 L135.080648,77.1414791 Z M131.319186,83.7122186 C133.108028,83.7122186 134.760587,84.6678753 135.652827,86.2183156 L138.983552,92.0060969 C139.87203,93.5500005 139.87203,95.4499995 138.983552,96.9939031 L135.652827,102.781684 C134.760587,104.332125 133.108028,105.287781 131.319186,105.287781 L124.685874,105.287781 C122.897032,105.287781 121.244473,104.332125 120.352233,102.781684 L117.021508,96.9939031 C116.13303,95.4499995 116.13303,93.5500005 117.021508,92.0060969 L120.352233,86.2183156 C121.244473,84.6678753 122.897032,83.7122186 124.685874,83.7122186 L131.319186,83.7122186 Z M128.003794,90.1848875 C126.459294,90.1848875 125.034382,91.0072828 124.263005,92.3424437 C123.491732,93.6774232 123.491732,95.3225768 124.263005,96.6575563 C125.034382,97.9927172 126.459294,98.8151125 128.001266,98.8151125 L128.001266,98.8151125 C129.545766,98.8151125 130.970678,97.9927172 131.742055,96.6575563 C132.513327,95.3225768 132.513327,93.6774232 131.742055,92.3424437 C130.970678,91.0072828 129.545766,90.1848875 128.003794,90.1848875 L128.003794,90.1848875 Z M93,94.5009646 L100.767764,94.5009646\"\n            fill=\"#FFD94C\"\n          ></path>\n          <path\n            d=\"M87.8601729,108.357758 C89.1715224,107.608286 90.8360246,108.074601 91.5779424,109.399303 L91.5779424,109.399303 L92.0525843,110.24352 C95.8563392,116.982993 99.8190116,123.380176 103.940602,129.435068 C108.807881,136.585427 114.28184,143.82411 120.362479,151.151115 C121.316878,152.30114 121.184944,154.011176 120.065686,154.997937 L120.065686,154.997937 L119.454208,155.534625 C99.3465389,173.103314 86.2778188,176.612552 80.2480482,166.062341 C74.3500652,155.742717 76.4844915,136.982888 86.6513274,109.782853 C86.876818,109.179582 87.3045861,108.675291 87.8601729,108.357758 Z M173.534177,129.041504 C174.986131,128.785177 176.375496,129.742138 176.65963,131.194242 L176.65963,131.194242 L176.812815,131.986376 C181.782365,157.995459 178.283348,171 166.315764,171 C154.609745,171 139.708724,159.909007 121.612702,137.727022 C121.211349,137.235047 120.994572,136.617371 121,135.981509 C121.013158,134.480686 122.235785,133.274651 123.730918,133.287756 L123.730918,133.287756 L124.684654,133.294531 C132.305698,133.335994 139.714387,133.071591 146.910723,132.501323 C155.409039,131.82788 164.283523,130.674607 173.534177,129.041504 Z M180.408726,73.8119663 C180.932139,72.4026903 182.508386,71.6634537 183.954581,72.149012 L183.954581,72.149012 L184.742552,72.4154854 C210.583763,81.217922 220.402356,90.8916805 214.198332,101.436761 C208.129904,111.751366 190.484347,119.260339 161.26166,123.963678 C160.613529,124.067994 159.948643,123.945969 159.382735,123.618843 C158.047025,122.846729 157.602046,121.158214 158.388848,119.847438 L158.388848,119.847438 L158.889328,119.0105 C162.877183,112.31633 166.481358,105.654262 169.701854,99.0242957 C173.50501,91.1948179 177.073967,82.7907081 180.408726,73.8119663 Z M94.7383398,66.0363218 C95.3864708,65.9320063 96.0513565,66.0540315 96.6172646,66.3811573 C97.9529754,67.153271 98.3979538,68.8417862 97.6111517,70.1525615 L97.6111517,70.1525615 L97.1106718,70.9895001 C93.1228168,77.6836699 89.5186416,84.3457379 86.2981462,90.9757043 C82.49499,98.8051821 78.9260328,107.209292 75.5912744,116.188034 C75.0678608,117.59731 73.4916142,118.336546 72.045419,117.850988 L72.045419,117.850988 L71.2574475,117.584515 C45.4162372,108.782078 35.597644,99.1083195 41.8016679,88.5632391 C47.8700957,78.2486335 65.515653,70.7396611 94.7383398,66.0363218 Z M136.545792,34.4653746 C156.653461,16.8966864 169.722181,13.3874478 175.751952,23.9376587 C181.649935,34.2572826 179.515508,53.0171122 169.348673,80.2171474 C169.123182,80.8204179 168.695414,81.324709 168.139827,81.6422422 C166.828478,82.3917144 165.163975,81.9253986 164.422058,80.6006966 L164.422058,80.6006966 L163.947416,79.7564798 C160.143661,73.0170065 156.180988,66.6198239 152.059398,60.564932 C147.192119,53.4145727 141.71816,46.1758903 135.637521,38.8488847 C134.683122,37.6988602 134.815056,35.9888243 135.934314,35.0020629 L135.934314,35.0020629 Z M90.6842361,18 C102.390255,18 117.291276,29.0909926 135.387298,51.2729777 C135.788651,51.7649527 136.005428,52.3826288 136,53.0184911 C135.986842,54.5193144 134.764215,55.7253489 133.269082,55.7122445 L133.269082,55.7122445 L132.315346,55.7054689 C124.694302,55.6640063 117.285613,55.9284091 110.089277,56.4986773 C101.590961,57.17212 92.7164767,58.325393 83.4658235,59.9584962 C82.0138691,60.2148231 80.6245044,59.2578618 80.3403697,57.805758 L80.3403697,57.805758 L80.1871846,57.0136235 C75.2176347,31.0045412 78.7166519,18 90.6842361,18 Z\"\n            fill=\"#FF4154\"\n          ></path>\n        </g>\n      </g>\n    </svg>\n  )\n}\n", "import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport { EnsuredQueryKey } from './types'\nimport type {\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Include or exclude active queries\n   */\n  active?: boolean\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include or exclude inactive queries\n   */\n  inactive?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include or exclude fetching queries\n   */\n  fetching?: boolean\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryStatusFilter = 'all' | 'active' | 'inactive' | 'none'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined'\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function ensureQueryKeyArray<T extends QueryKey>(\n  value: T\n): EnsuredQueryKey<T> {\n  return (Array.isArray(value)\n    ? value\n    : ([value] as unknown)) as EnsuredQueryKey<T>\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter(x => array2.indexOf(x) === -1)\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions\n): [TFilters, TOptions | undefined] {\n  return (isQueryKey(arg1)\n    ? [{ ...arg2, queryKey: arg1 }, arg3]\n    : [arg1 || {}, arg2]) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs(\n  arg1?: QueryKey | MutationFilters,\n  arg2?: MutationFilters\n): MutationFilters | undefined {\n  return isQueryKey(arg1) ? { ...arg2, mutationKey: arg1 } : arg1\n}\n\nexport function mapQueryStatusFilter(\n  active?: boolean,\n  inactive?: boolean\n): QueryStatusFilter {\n  if (\n    (active === true && inactive === true) ||\n    (active == null && inactive == null)\n  ) {\n    return 'all'\n  } else if (active === false && inactive === false) {\n    return 'none'\n  } else {\n    // At this point, active|inactive can only be true|false or false|true\n    // so, when only one value is provided, the missing one has to be the negated value\n    const isActive = active ?? !inactive\n    return isActive ? 'active' : 'inactive'\n  }\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>\n): boolean {\n  const {\n    active,\n    exact,\n    fetching,\n    inactive,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  const queryStatusFilter = mapQueryStatusFilter(active, inactive)\n\n  if (queryStatusFilter === 'none') {\n    return false\n  } else if (queryStatusFilter !== 'all') {\n    const isActive = query.isActive()\n    if (queryStatusFilter === 'active' && !isActive) {\n      return false\n    }\n    if (queryStatusFilter === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (typeof fetching === 'boolean' && query.isFetching() !== fetching) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  const asArray = ensureQueryKeyArray(queryKey)\n  return stableValueHash(asArray)\n}\n\n/**\n * Hashes the value into a stable hash.\n */\nexport function stableValueHash(value: any): string {\n  return JSON.stringify(value, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(ensureQueryKeyArray(a), ensureQueryKeyArray(b))\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(key => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = Array.isArray(a) && Array.isArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: any): value is QueryKey {\n  return typeof value === 'string' || Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void): void {\n  Promise.resolve()\n    .then(callback)\n    .catch(error =>\n      setTimeout(() => {\n        throw error\n      })\n    )\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n}\n", "import React from 'react'\n\nimport { Query, useQueryClient } from 'react-query'\nimport { matchSorter } from 'match-sorter'\nimport useLocalStorage from './useLocalStorage'\nimport { useIsMounted, useSafeState } from './utils'\n\nimport {\n  Panel,\n  QueryKeys,\n  QueryKey,\n  Button,\n  Code,\n  Input,\n  Select,\n  ActiveQueryPanel,\n} from './styledComponents'\nimport { ThemeProvider, defaultTheme as theme } from './theme'\nimport { getQueryStatusLabel, getQueryStatusColor } from './utils'\nimport Explorer from './Explorer'\nimport Logo from './Logo'\nimport { noop } from '../core/utils'\n\ninterface DevtoolsOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * Use this to add props to the panel. For example, you can add className, style (merge and override default style), etc.\n   */\n  panelProps?: React.DetailedHTMLProps<\n    React.HTMLAttributes<HTMLDivElement>,\n    HTMLDivElement\n  >\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.DetailedHTMLProps<\n    React.ButtonHTMLAttributes<HTMLButtonElement>,\n    HTMLButtonElement\n  >\n  /**\n   * Use this to add props to the toggle button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  toggleButtonProps?: React.DetailedHTMLProps<\n    React.ButtonHTMLAttributes<HTMLButtonElement>,\n    HTMLButtonElement\n  >\n  /**\n   * The position of the React Query logo to open and close the devtools panel.\n   * Defaults to 'bottom-left'.\n   */\n  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\n  /**\n   * Use this to render the devtools inside a different type of container element for a11y purposes.\n   * Any string which corresponds to a valid intrinsic JSX element is allowed.\n   * Defaults to 'aside'.\n   */\n  containerElement?: string | any\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n}\n\ninterface DevtoolsPanelOptions {\n  /**\n   * The standard React style object used to style a component with inline styles\n   */\n  style?: React.CSSProperties\n  /**\n   * The standard React className property used to style a component with classes\n   */\n  className?: string\n  /**\n   * A boolean variable indicating whether the panel is open or closed\n   */\n  isOpen?: boolean\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n  /**\n   * A function that toggles the open and close state of the panel\n   */\n  setIsOpen: (isOpen: boolean) => void\n  /**\n   * Handles the opening and closing the devtools panel\n   */\n  handleDragStart: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void\n}\n\nconst isServer = typeof window === 'undefined'\n\nexport function ReactQueryDevtools({\n  initialIsOpen,\n  panelProps = {},\n  closeButtonProps = {},\n  toggleButtonProps = {},\n  position = 'bottom-left',\n  containerElement: Container = 'aside',\n  styleNonce,\n}: DevtoolsOptions): React.ReactElement | null {\n  const rootRef = React.useRef<HTMLDivElement>(null)\n  const panelRef = React.useRef<HTMLDivElement>(null)\n  const [isOpen, setIsOpen] = useLocalStorage(\n    'reactQueryDevtoolsOpen',\n    initialIsOpen\n  )\n  const [devtoolsHeight, setDevtoolsHeight] = useLocalStorage<number | null>(\n    'reactQueryDevtoolsHeight',\n    null\n  )\n  const [isResolvedOpen, setIsResolvedOpen] = useSafeState(false)\n  const [isResizing, setIsResizing] = useSafeState(false)\n  const isMounted = useIsMounted()\n\n  const handleDragStart = (\n    panelElement: HTMLDivElement | null,\n    startEvent: React.MouseEvent<HTMLDivElement, MouseEvent>\n  ) => {\n    if (startEvent.button !== 0) return // Only allow left click for drag\n\n    setIsResizing(true)\n\n    const dragInfo = {\n      originalHeight: panelElement?.getBoundingClientRect().height ?? 0,\n      pageY: startEvent.pageY,\n    }\n\n    const run = (moveEvent: MouseEvent) => {\n      const delta = dragInfo.pageY - moveEvent.pageY\n      const newHeight = dragInfo?.originalHeight + delta\n\n      setDevtoolsHeight(newHeight)\n\n      if (newHeight < 70) {\n        setIsOpen(false)\n      } else {\n        setIsOpen(true)\n      }\n    }\n\n    const unsub = () => {\n      setIsResizing(false)\n      document.removeEventListener('mousemove', run)\n      document.removeEventListener('mouseUp', unsub)\n    }\n\n    document.addEventListener('mousemove', run)\n    document.addEventListener('mouseup', unsub)\n  }\n\n  React.useEffect(() => {\n    setIsResolvedOpen(isOpen ?? false)\n  }, [isOpen, isResolvedOpen, setIsResolvedOpen])\n\n  // Toggle panel visibility before/after transition (depending on direction).\n  // Prevents focusing in a closed panel.\n  React.useEffect(() => {\n    const ref = panelRef.current\n    if (ref) {\n      const handlePanelTransitionStart = () => {\n        if (ref && isResolvedOpen) {\n          ref.style.visibility = 'visible'\n        }\n      }\n\n      const handlePanelTransitionEnd = () => {\n        if (ref && !isResolvedOpen) {\n          ref.style.visibility = 'hidden'\n        }\n      }\n\n      ref.addEventListener('transitionstart', handlePanelTransitionStart)\n      ref.addEventListener('transitionend', handlePanelTransitionEnd)\n\n      return () => {\n        ref.removeEventListener('transitionstart', handlePanelTransitionStart)\n        ref.removeEventListener('transitionend', handlePanelTransitionEnd)\n      }\n    }\n  }, [isResolvedOpen])\n\n  React[isServer ? 'useEffect' : 'useLayoutEffect'](() => {\n    if (isResolvedOpen) {\n      const previousValue = rootRef.current?.parentElement?.style.paddingBottom\n\n      const run = () => {\n        const containerHeight = panelRef.current?.getBoundingClientRect().height\n        if (rootRef.current?.parentElement) {\n          rootRef.current.parentElement.style.paddingBottom = `${containerHeight}px`\n        }\n      }\n\n      run()\n\n      if (typeof window !== 'undefined') {\n        window.addEventListener('resize', run)\n\n        return () => {\n          window.removeEventListener('resize', run)\n          if (\n            rootRef.current?.parentElement &&\n            typeof previousValue === 'string'\n          ) {\n            rootRef.current.parentElement.style.paddingBottom = previousValue\n          }\n        }\n      }\n    }\n  }, [isResolvedOpen])\n\n  const { style: panelStyle = {}, ...otherPanelProps } = panelProps\n\n  const {\n    style: closeButtonStyle = {},\n    onClick: onCloseClick,\n    ...otherCloseButtonProps\n  } = closeButtonProps\n\n  const {\n    style: toggleButtonStyle = {},\n    onClick: onToggleClick,\n    ...otherToggleButtonProps\n  } = toggleButtonProps\n\n  // Do not render on the server\n  if (!isMounted()) return null\n\n  return (\n    <Container\n      ref={rootRef}\n      className=\"ReactQueryDevtools\"\n      aria-label=\"React Query Devtools\"\n    >\n      <ThemeProvider theme={theme}>\n        <ReactQueryDevtoolsPanel\n          ref={panelRef as any}\n          styleNonce={styleNonce}\n          {...otherPanelProps}\n          style={{\n            position: 'fixed',\n            bottom: '0',\n            right: '0',\n            zIndex: 99999,\n            width: '100%',\n            height: devtoolsHeight ?? 500,\n            maxHeight: '90%',\n            boxShadow: '0 0 20px rgba(0,0,0,.3)',\n            borderTop: `1px solid ${theme.gray}`,\n            transformOrigin: 'top',\n            // visibility will be toggled after transitions, but set initial state here\n            visibility: isOpen ? 'visible' : 'hidden',\n            ...panelStyle,\n            ...(isResizing\n              ? {\n                  transition: `none`,\n                }\n              : { transition: `all .2s ease` }),\n            ...(isResolvedOpen\n              ? {\n                  opacity: 1,\n                  pointerEvents: 'all',\n                  transform: `translateY(0) scale(1)`,\n                }\n              : {\n                  opacity: 0,\n                  pointerEvents: 'none',\n                  transform: `translateY(15px) scale(1.02)`,\n                }),\n          }}\n          isOpen={isResolvedOpen}\n          setIsOpen={setIsOpen}\n          handleDragStart={e => handleDragStart(panelRef.current, e)}\n        />\n        {isResolvedOpen ? (\n          <Button\n            type=\"button\"\n            aria-controls=\"ReactQueryDevtoolsPanel\"\n            aria-haspopup=\"true\"\n            aria-expanded=\"true\"\n            {...(otherCloseButtonProps as unknown)}\n            onClick={e => {\n              setIsOpen(false)\n              onCloseClick && onCloseClick(e)\n            }}\n            style={{\n              position: 'fixed',\n              zIndex: 99999,\n              margin: '.5em',\n              bottom: 0,\n              ...(position === 'top-right'\n                ? {\n                    right: '0',\n                  }\n                : position === 'top-left'\n                ? {\n                    left: '0',\n                  }\n                : position === 'bottom-right'\n                ? {\n                    right: '0',\n                  }\n                : {\n                    left: '0',\n                  }),\n              ...closeButtonStyle,\n            }}\n          >\n            Close\n          </Button>\n        ) : null}\n      </ThemeProvider>\n      {!isResolvedOpen ? (\n        <button\n          type=\"button\"\n          {...otherToggleButtonProps}\n          aria-label=\"Open React Query Devtools\"\n          aria-controls=\"ReactQueryDevtoolsPanel\"\n          aria-haspopup=\"true\"\n          aria-expanded=\"false\"\n          onClick={e => {\n            setIsOpen(true)\n            onToggleClick && onToggleClick(e)\n          }}\n          style={{\n            background: 'none',\n            border: 0,\n            padding: 0,\n            position: 'fixed',\n            zIndex: 99999,\n            display: 'inline-flex',\n            fontSize: '1.5em',\n            margin: '.5em',\n            cursor: 'pointer',\n            width: 'fit-content',\n            ...(position === 'top-right'\n              ? {\n                  top: '0',\n                  right: '0',\n                }\n              : position === 'top-left'\n              ? {\n                  top: '0',\n                  left: '0',\n                }\n              : position === 'bottom-right'\n              ? {\n                  bottom: '0',\n                  right: '0',\n                }\n              : {\n                  bottom: '0',\n                  left: '0',\n                }),\n            ...toggleButtonStyle,\n          }}\n        >\n          <Logo aria-hidden />\n        </button>\n      ) : null}\n    </Container>\n  )\n}\n\nconst getStatusRank = (q: Query) =>\n  q.state.isFetching ? 0 : !q.getObserversCount() ? 3 : q.isStale() ? 2 : 1\n\nconst sortFns: Record<string, (a: Query, b: Query) => number> = {\n  'Status > Last Updated': (a, b) =>\n    getStatusRank(a) === getStatusRank(b)\n      ? (sortFns['Last Updated']?.(a, b) as number)\n      : getStatusRank(a) > getStatusRank(b)\n      ? 1\n      : -1,\n  'Query Hash': (a, b) => (a.queryHash > b.queryHash ? 1 : -1),\n  'Last Updated': (a, b) =>\n    a.state.dataUpdatedAt < b.state.dataUpdatedAt ? 1 : -1,\n}\n\nexport const ReactQueryDevtoolsPanel = React.forwardRef<\n  HTMLDivElement,\n  DevtoolsPanelOptions\n>(function ReactQueryDevtoolsPanel(props, ref): React.ReactElement {\n  const {\n    isOpen = true,\n    styleNonce,\n    setIsOpen,\n    handleDragStart,\n    ...panelProps\n  } = props\n\n  const queryClient = useQueryClient()\n  const queryCache = queryClient.getQueryCache()\n\n  const [sort, setSort] = useLocalStorage(\n    'reactQueryDevtoolsSortFn',\n    Object.keys(sortFns)[0]\n  )\n\n  const [filter, setFilter] = useLocalStorage('reactQueryDevtoolsFilter', '')\n\n  const [sortDesc, setSortDesc] = useLocalStorage(\n    'reactQueryDevtoolsSortDesc',\n    false\n  )\n\n  const sortFn = React.useMemo(() => sortFns[sort as string], [sort])\n\n  React[isServer ? 'useEffect' : 'useLayoutEffect'](() => {\n    if (!sortFn) {\n      setSort(Object.keys(sortFns)[0] as string)\n    }\n  }, [setSort, sortFn])\n\n  const [unsortedQueries, setUnsortedQueries] = useSafeState(\n    Object.values(queryCache.findAll())\n  )\n\n  const [activeQueryHash, setActiveQueryHash] = useLocalStorage(\n    'reactQueryDevtoolsActiveQueryHash',\n    ''\n  )\n\n  const queries = React.useMemo(() => {\n    const sorted = [...unsortedQueries].sort(sortFn)\n\n    if (sortDesc) {\n      sorted.reverse()\n    }\n\n    if (!filter) {\n      return sorted\n    }\n\n    return matchSorter(sorted, filter, { keys: ['queryHash'] }).filter(\n      d => d.queryHash\n    )\n  }, [sortDesc, sortFn, unsortedQueries, filter])\n\n  const activeQuery = React.useMemo(() => {\n    return queries.find(query => query.queryHash === activeQueryHash)\n  }, [activeQueryHash, queries])\n\n  const hasFresh = queries.filter(q => getQueryStatusLabel(q) === 'fresh')\n    .length\n  const hasFetching = queries.filter(q => getQueryStatusLabel(q) === 'fetching')\n    .length\n  const hasStale = queries.filter(q => getQueryStatusLabel(q) === 'stale')\n    .length\n  const hasInactive = queries.filter(q => getQueryStatusLabel(q) === 'inactive')\n    .length\n\n  React.useEffect(() => {\n    if (isOpen) {\n      const unsubscribe = queryCache.subscribe(() => {\n        setUnsortedQueries(Object.values(queryCache.getAll()))\n      })\n      // re-subscribing after the panel is closed and re-opened won't trigger the callback,\n      // So we'll manually populate our state\n      setUnsortedQueries(Object.values(queryCache.getAll()))\n\n      return unsubscribe\n    }\n    return undefined\n  }, [isOpen, sort, sortFn, sortDesc, setUnsortedQueries, queryCache])\n\n  const handleRefetch = () => {\n    const promise = activeQuery?.fetch()\n    promise?.catch(noop)\n  }\n\n  return (\n    <ThemeProvider theme={theme}>\n      <Panel\n        ref={ref}\n        className=\"ReactQueryDevtoolsPanel\"\n        aria-label=\"React Query Devtools Panel\"\n        id=\"ReactQueryDevtoolsPanel\"\n        {...panelProps}\n      >\n        <style\n          nonce={styleNonce}\n          dangerouslySetInnerHTML={{\n            __html: `\n            .ReactQueryDevtoolsPanel * {\n              scrollbar-color: ${theme.backgroundAlt} ${theme.gray};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar, .ReactQueryDevtoolsPanel scrollbar {\n              width: 1em;\n              height: 1em;\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-track, .ReactQueryDevtoolsPanel scrollbar-track {\n              background: ${theme.backgroundAlt};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-thumb, .ReactQueryDevtoolsPanel scrollbar-thumb {\n              background: ${theme.gray};\n              border-radius: .5em;\n              border: 3px solid ${theme.backgroundAlt};\n            }\n          `,\n          }}\n        />\n        <div\n          style={{\n            position: 'absolute',\n            left: 0,\n            top: 0,\n            width: '100%',\n            height: '4px',\n            marginBottom: '-4px',\n            cursor: 'row-resize',\n            zIndex: 100000,\n          }}\n          onMouseDown={handleDragStart}\n        ></div>\n        <div\n          style={{\n            flex: '1 1 500px',\n            minHeight: '40%',\n            maxHeight: '100%',\n            overflow: 'auto',\n            borderRight: `1px solid ${theme.grayAlt}`,\n            display: isOpen ? 'flex' : 'none',\n            flexDirection: 'column',\n          }}\n        >\n          <div\n            style={{\n              padding: '.5em',\n              background: theme.backgroundAlt,\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n            }}\n          >\n            <button\n              type=\"button\"\n              aria-label=\"Close React Query Devtools\"\n              aria-controls=\"ReactQueryDevtoolsPanel\"\n              aria-haspopup=\"true\"\n              aria-expanded=\"true\"\n              onClick={() => setIsOpen(false)}\n              style={{\n                display: 'inline-flex',\n                background: 'none',\n                border: 0,\n                padding: 0,\n                marginRight: '.5em',\n                cursor: 'pointer',\n              }}\n            >\n              <Logo aria-hidden />\n            </button>\n            <div\n              style={{\n                display: 'flex',\n                flexDirection: 'column',\n              }}\n            >\n              <QueryKeys style={{ marginBottom: '.5em' }}>\n                <QueryKey\n                  style={{\n                    background: theme.success,\n                    opacity: hasFresh ? 1 : 0.3,\n                  }}\n                >\n                  fresh <Code>({hasFresh})</Code>\n                </QueryKey>{' '}\n                <QueryKey\n                  style={{\n                    background: theme.active,\n                    opacity: hasFetching ? 1 : 0.3,\n                  }}\n                >\n                  fetching <Code>({hasFetching})</Code>\n                </QueryKey>{' '}\n                <QueryKey\n                  style={{\n                    background: theme.warning,\n                    color: 'black',\n                    textShadow: '0',\n                    opacity: hasStale ? 1 : 0.3,\n                  }}\n                >\n                  stale <Code>({hasStale})</Code>\n                </QueryKey>{' '}\n                <QueryKey\n                  style={{\n                    background: theme.gray,\n                    opacity: hasInactive ? 1 : 0.3,\n                  }}\n                >\n                  inactive <Code>({hasInactive})</Code>\n                </QueryKey>\n              </QueryKeys>\n              <div\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                }}\n              >\n                <Input\n                  placeholder=\"Filter\"\n                  aria-label=\"Filter by queryhash\"\n                  value={filter ?? ''}\n                  onChange={e => setFilter(e.target.value)}\n                  onKeyDown={e => {\n                    if (e.key === 'Escape') setFilter('')\n                  }}\n                  style={{\n                    flex: '1',\n                    marginRight: '.5em',\n                    width: '100%',\n                  }}\n                />\n                {!filter ? (\n                  <>\n                    <Select\n                      aria-label=\"Sort queries\"\n                      value={sort}\n                      onChange={e => setSort(e.target.value)}\n                      style={{\n                        flex: '1',\n                        minWidth: 75,\n                        marginRight: '.5em',\n                      }}\n                    >\n                      {Object.keys(sortFns).map(key => (\n                        <option key={key} value={key}>\n                          Sort by {key}\n                        </option>\n                      ))}\n                    </Select>\n                    <Button\n                      type=\"button\"\n                      onClick={() => setSortDesc(old => !old)}\n                      style={{\n                        padding: '.3em .4em',\n                      }}\n                    >\n                      {sortDesc ? '⬇ Desc' : '⬆ Asc'}\n                    </Button>\n                  </>\n                ) : null}\n              </div>\n            </div>\n          </div>\n          <div\n            style={{\n              overflowY: 'auto',\n              flex: '1',\n            }}\n          >\n            {queries.map((query, i) => {\n              const isDisabled =\n                query.getObserversCount() > 0 && !query.isActive()\n              return (\n                <div\n                  key={query.queryHash || i}\n                  role=\"button\"\n                  aria-label={`Open query details for ${query.queryHash}`}\n                  onClick={() =>\n                    setActiveQueryHash(\n                      activeQueryHash === query.queryHash ? '' : query.queryHash\n                    )\n                  }\n                  style={{\n                    display: 'flex',\n                    borderBottom: `solid 1px ${theme.grayAlt}`,\n                    cursor: 'pointer',\n                    background:\n                      query === activeQuery\n                        ? 'rgba(255,255,255,.1)'\n                        : undefined,\n                  }}\n                >\n                  <div\n                    style={{\n                      flex: '0 0 auto',\n                      width: '2em',\n                      height: '2em',\n                      background: getQueryStatusColor(query, theme),\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontWeight: 'bold',\n                      textShadow:\n                        getQueryStatusLabel(query) === 'stale'\n                          ? '0'\n                          : '0 0 10px black',\n                      color:\n                        getQueryStatusLabel(query) === 'stale'\n                          ? 'black'\n                          : 'white',\n                    }}\n                  >\n                    {query.getObserversCount()}\n                  </div>\n                  {isDisabled ? (\n                    <div\n                      style={{\n                        flex: '0 0 auto',\n                        height: '2em',\n                        background: theme.gray,\n                        display: 'flex',\n                        alignItems: 'center',\n                        fontWeight: 'bold',\n                        padding: '0 0.5em',\n                      }}\n                    >\n                      disabled\n                    </div>\n                  ) : null}\n                  <Code\n                    style={{\n                      padding: '.5em',\n                    }}\n                  >\n                    {`${query.queryHash}`}\n                  </Code>\n                </div>\n              )\n            })}\n          </div>\n        </div>\n\n        {activeQuery ? (\n          <ActiveQueryPanel>\n            <div\n              style={{\n                padding: '.5em',\n                background: theme.backgroundAlt,\n                position: 'sticky',\n                top: 0,\n                zIndex: 1,\n              }}\n            >\n              Query Details\n            </div>\n            <div\n              style={{\n                padding: '.5em',\n              }}\n            >\n              <div\n                style={{\n                  marginBottom: '.5em',\n                  display: 'flex',\n                  alignItems: 'start',\n                  justifyContent: 'space-between',\n                }}\n              >\n                <Code\n                  style={{\n                    lineHeight: '1.8em',\n                  }}\n                >\n                  <pre\n                    style={{\n                      margin: 0,\n                      padding: 0,\n                      overflow: 'auto',\n                    }}\n                  >\n                    {JSON.stringify(activeQuery.queryKey, null, 2)}\n                  </pre>\n                </Code>\n                <span\n                  style={{\n                    padding: '0.3em .6em',\n                    borderRadius: '0.4em',\n                    fontWeight: 'bold',\n                    textShadow: '0 2px 10px black',\n                    background: getQueryStatusColor(activeQuery, theme),\n                    flexShrink: 0,\n                  }}\n                >\n                  {getQueryStatusLabel(activeQuery)}\n                </span>\n              </div>\n              <div\n                style={{\n                  marginBottom: '.5em',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                }}\n              >\n                Observers: <Code>{activeQuery.getObserversCount()}</Code>\n              </div>\n              <div\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'space-between',\n                }}\n              >\n                Last Updated:{' '}\n                <Code>\n                  {new Date(\n                    activeQuery.state.dataUpdatedAt\n                  ).toLocaleTimeString()}\n                </Code>\n              </div>\n            </div>\n            <div\n              style={{\n                background: theme.backgroundAlt,\n                padding: '.5em',\n                position: 'sticky',\n                top: 0,\n                zIndex: 1,\n              }}\n            >\n              Actions\n            </div>\n            <div\n              style={{\n                padding: '0.5em',\n              }}\n            >\n              <Button\n                type=\"button\"\n                onClick={handleRefetch}\n                disabled={activeQuery.state.isFetching}\n                style={{\n                  background: theme.active,\n                }}\n              >\n                Refetch\n              </Button>{' '}\n              <Button\n                type=\"button\"\n                onClick={() => queryClient.invalidateQueries(activeQuery)}\n                style={{\n                  background: theme.warning,\n                  color: theme.inputTextColor,\n                }}\n              >\n                Invalidate\n              </Button>{' '}\n              <Button\n                type=\"button\"\n                onClick={() => queryClient.resetQueries(activeQuery)}\n                style={{\n                  background: theme.gray,\n                }}\n              >\n                Reset\n              </Button>{' '}\n              <Button\n                type=\"button\"\n                onClick={() => queryClient.removeQueries(activeQuery)}\n                style={{\n                  background: theme.danger,\n                }}\n              >\n                Remove\n              </Button>\n            </div>\n            <div\n              style={{\n                background: theme.backgroundAlt,\n                padding: '.5em',\n                position: 'sticky',\n                top: 0,\n                zIndex: 1,\n              }}\n            >\n              Data Explorer\n            </div>\n            <div\n              style={{\n                padding: '.5em',\n              }}\n            >\n              <Explorer\n                label=\"Data\"\n                value={activeQuery?.state?.data}\n                defaultExpanded={{}}\n              />\n            </div>\n            <div\n              style={{\n                background: theme.backgroundAlt,\n                padding: '.5em',\n                position: 'sticky',\n                top: 0,\n                zIndex: 1,\n              }}\n            >\n              Query Explorer\n            </div>\n            <div\n              style={{\n                padding: '.5em',\n              }}\n            >\n              <Explorer\n                label=\"Query\"\n                value={activeQuery}\n                defaultExpanded={{\n                  queryKey: true,\n                }}\n              />\n            </div>\n          </ActiveQueryPanel>\n        ) : null}\n      </Panel>\n    </ThemeProvider>\n  )\n})\n"], "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "keys", "indexOf", "characterMap", "chars", "join", "allAccents", "RegExp", "firstAccent", "removeAccents", "string", "replace", "match", "hasAccents", "module", "rankings", "CASE_SENSITIVE_EQUAL", "EQUAL", "STARTS_WITH", "WORD_STARTS_WITH", "CONTAINS", "ACRONYM", "MATCHES", "NO_MATCH", "matchSorter", "defaultBaseSortFn", "a", "b", "String", "rankedValue", "localeCompare", "items", "value", "options", "_options", "_options$threshold", "threshold", "_options$baseSort", "baseSort", "matchedItems", "reduce", "reduceItemsToRanked", "sort", "sortRankedValues", "map", "_ref", "item", "matches", "index", "rankingInfo", "getHighestRanking", "rank", "_rankingInfo$keyThres", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "stringItem", "getMatchRanking", "keyIndex", "valuesToRank", "getAllValuesToRank", "_ref2", "_ref3", "itemValue", "attributes", "newRank", "newRankedValue", "minRanking", "maxRanking", "testString", "stringToRank", "prepareValueForComparison", "toLowerCase", "startsWith", "includes", "getAcronym", "getClosenessRanking", "acronym", "wordsInString", "split", "for<PERSON>ach", "wordInString", "splitByHyphenWords", "splitByHyphenWord", "substr", "matchingInOrderCharCount", "char<PERSON><PERSON>ber", "findMatchingCharacter", "matchChar", "j", "stringChar", "getRanking", "spread", "inOrderPercentage", "ranking", "firstIndex", "found", "<PERSON><PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "aRank", "aKeyIndex", "bRank", "bKeyIndex", "_ref4", "keepDiacritics", "getItemValues", "getNestedValue", "concat", "obj", "itemObj", "nested<PERSON><PERSON>", "allVals", "values", "getKeyAttributes", "defaultKeyAttributes", "Infinity", "getItem", "localStorage", "JSON", "parse", "undefined", "useLocalStorage", "defaultValue", "React", "useState", "setValue", "useEffect", "initialValue", "setter", "useCallback", "updater", "old", "newVal", "setItem", "stringify", "defaultTheme", "background", "backgroundAlt", "foreground", "gray", "grayAlt", "inputBackgroundColor", "inputTextColor", "success", "danger", "active", "warning", "ThemeContext", "createContext", "ThemeProvider", "theme", "rest", "useTheme", "useContext", "useMediaQuery", "query", "window", "matchMedia", "isMatch", "setIsMatch", "matcher", "onChange", "addListener", "removeListener", "isServer", "getQueryStatusColor", "state", "isFetching", "getObserversCount", "isStale", "getQueryStatusLabel", "styled", "type", "newStyles", "queries", "forwardRef", "ref", "style", "mediaStyles", "entries", "current", "createElement", "useIsMounted", "mountedRef", "useRef", "isMounted", "useSafeState", "initialState", "setState", "safeSetState", "scheduleMicrotask", "displayValue", "name", "getOwnPropertyNames", "newValue", "toString", "callback", "Promise", "resolve", "then", "catch", "error", "setTimeout", "Panel", "_props", "fontSize", "fontFamily", "display", "backgroundColor", "color", "flexDirection", "ActiveQueryPanel", "flex", "overflow", "height", "borderTop", "<PERSON><PERSON>", "props", "appearance", "fontWeight", "border", "borderRadius", "padding", "opacity", "disabled", "cursor", "Query<PERSON><PERSON>s", "Query<PERSON>ey", "alignItems", "textShadow", "Code", "Input", "lineHeight", "Select", "WebkitAppearance", "backgroundImage", "backgroundRepeat", "backgroundPosition", "backgroundSize", "Entry", "outline", "wordBreak", "Label", "LabelButton", "ExpandButton", "font", "Value", "SubEntries", "marginLeft", "paddingLeft", "borderLeft", "Info", "Expander", "expanded", "transition", "transform", "chunkArray", "array", "size", "result", "slice", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HandleEntry", "label", "subEntries", "subEntryPages", "toggleExpanded", "pageSize", "expandedPages", "setExpandedPages", "entry", "filter", "d", "isIterable", "x", "Symbol", "iterator", "Explorer", "defaultExpanded", "renderer", "Boolean", "setExpanded", "makeProperty", "sub", "subDefaultExpanded", "Array", "isArray", "from", "val", "Logo", "React.createElement", "noop", "ReactQueryDevtools", "initialIsOpen", "panelProps", "closeButtonProps", "toggleButtonProps", "position", "containerElement", "Container", "styleNonce", "rootRef", "panelRef", "isOpen", "setIsOpen", "devtoolsHeight", "setDevtoolsHeight", "isResolvedOpen", "setIsResolvedOpen", "isResizing", "setIsResizing", "handleDragStart", "panelElement", "startEvent", "button", "dragInfo", "originalHeight", "getBoundingClientRect", "pageY", "run", "moveEvent", "delta", "newHeight", "unsub", "document", "removeEventListener", "addEventListener", "handlePanelTransitionStart", "visibility", "handlePanelTransitionEnd", "previousValue", "parentElement", "paddingBottom", "containerHeight", "panelStyle", "otherPanelProps", "closeButtonStyle", "onCloseClick", "onClick", "otherCloseButtonProps", "toggleButtonStyle", "onToggleClick", "otherToggleButtonProps", "bottom", "right", "zIndex", "width", "maxHeight", "boxShadow", "transform<PERSON><PERSON>in", "pointerEvents", "e", "margin", "left", "top", "getStatusRank", "q", "sortFns", "queryHash", "dataUpdatedAt", "ReactQueryDevtoolsPanel", "queryClient", "useQueryClient", "queryCache", "get<PERSON><PERSON><PERSON><PERSON>ache", "setSort", "setFilter", "sortDesc", "setSortDesc", "sortFn", "useMemo", "findAll", "unsortedQueries", "setUnsortedQueries", "activeQueryHash", "setActiveQueryHash", "sorted", "reverse", "activeQuery", "find", "hasFresh", "hasFetching", "hasStale", "hasInactive", "unsubscribe", "subscribe", "getAll", "handleRefetch", "promise", "fetch", "__html", "marginBottom", "minHeight", "borderRight", "justifyContent", "marginRight", "min<PERSON><PERSON><PERSON>", "overflowY", "isDisabled", "isActive", "borderBottom", "query<PERSON><PERSON>", "flexShrink", "Date", "toLocaleTimeString", "invalidateQueries", "resetQueries", "removeQueries", "data"], "mappings": ";;;;;;;;EAAe,SAASA,QAAT,GAAoB;EACjCA,EAAAA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,MAAV,EAAkB;EAC5C,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,SAAS,CAACC,MAA9B,EAAsCF,CAAC,EAAvC,EAA2C;EACzC,UAAIG,MAAM,GAAGF,SAAS,CAACD,CAAD,CAAtB;;EAEA,WAAK,IAAII,GAAT,IAAgBD,MAAhB,EAAwB;EACtB,YAAIN,MAAM,CAACQ,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCJ,MAArC,EAA6CC,GAA7C,CAAJ,EAAuD;EACrDL,UAAAA,MAAM,CAACK,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAApB;EACD;EACF;EACF;;EAED,WAAOL,MAAP;EACD,GAZD;;EAcA,SAAOH,QAAQ,CAACY,KAAT,CAAe,IAAf,EAAqBP,SAArB,CAAP;EACD;;EChBc,SAASQ,6BAAT,CAAuCN,MAAvC,EAA+CO,QAA/C,EAAyD;EACtE,MAAIP,MAAM,IAAI,IAAd,EAAoB,OAAO,EAAP;EACpB,MAAIJ,MAAM,GAAG,EAAb;EACA,MAAIY,UAAU,GAAGd,MAAM,CAACe,IAAP,CAAYT,MAAZ,CAAjB;EACA,MAAIC,GAAJ,EAASJ,CAAT;;EAEA,OAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGW,UAAU,CAACT,MAA3B,EAAmCF,CAAC,EAApC,EAAwC;EACtCI,IAAAA,GAAG,GAAGO,UAAU,CAACX,CAAD,CAAhB;EACA,QAAIU,QAAQ,CAACG,OAAT,CAAiBT,GAAjB,KAAyB,CAA7B,EAAgC;EAChCL,IAAAA,MAAM,CAACK,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAApB;EACD;;EAED,SAAOL,MAAP;EACD;;ECbc,SAASH,UAAT,GAAoB;EACjCA,EAAAA,UAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,MAAV,EAAkB;EAC5C,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,SAAS,CAACC,MAA9B,EAAsCF,CAAC,EAAvC,EAA2C;EACzC,UAAIG,MAAM,GAAGF,SAAS,CAACD,CAAD,CAAtB;;EAEA,WAAK,IAAII,GAAT,IAAgBD,MAAhB,EAAwB;EACtB,YAAIN,MAAM,CAACQ,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCJ,MAArC,EAA6CC,GAA7C,CAAJ,EAAuD;EACrDL,UAAAA,MAAM,CAACK,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAApB;EACD;EACF;EACF;;EAED,WAAOL,MAAP;EACD,GAZD;;EAcA,SAAOH,UAAQ,CAACY,KAAT,CAAe,IAAf,EAAqBP,SAArB,CAAP;EACD;;EChBD,IAAIa,YAAY,GAAG;SACb,GADa;SAEb,GAFa;SAGb,GAHa;SAIb,GAJa;SAKb,GALa;SAMb,GANa;SAOb,GAPa;SAQb,GARa;SASb,GATa;SAUb,GAVa;SAWb,GAXa;SAYb,IAZa;SAab,GAba;SAcb,GAda;SAeb,GAfa;SAgBb,GAhBa;SAiBb,GAjBa;SAkBb,GAlBa;SAmBb,GAnBa;SAoBb,GApBa;SAqBb,GArBa;SAsBb,GAtBa;SAuBb,GAvBa;SAwBb,GAxBa;SAyBb,GAzBa;SA0Bb,GA1Ba;SA2Bb,GA3Ba;SA4Bb,GA5Ba;SA6Bb,GA7Ba;SA8Bb,GA9Ba;SA+Bb,GA/Ba;SAgCb,GAhCa;SAiCb,GAjCa;SAkCb,GAlCa;SAmCb,GAnCa;SAoCb,GApCa;SAqCb,GArCa;SAsCb,GAtCa;SAuCb,GAvCa;SAwCb,GAxCa;SAyCb,GAzCa;SA0Cb,GA1Ca;SA2Cb,GA3Ca;SA4Cb,GA5Ca;SA6Cb,GA7Ca;SA8Cb,GA9Ca;SA+Cb,GA/Ca;SAgDb,GAhDa;SAiDb,GAjDa;SAkDb,GAlDa;SAmDb,GAnDa;SAoDb,GApDa;SAqDb,GArDa;SAsDb,GAtDa;SAuDb,GAvDa;SAwDb,GAxDa;SAyDb,GAzDa;SA0Db,GA1Da;SA2Db,GA3Da;SA4Db,GA5Da;SA6Db,GA7Da;SA8Db,IA9Da;SA+Db,GA/Da;SAgEb,GAhEa;SAiEb,GAjEa;SAkEb,GAlEa;SAmEb,GAnEa;SAoEb,GApEa;SAqEb,GArEa;SAsEb,GAtEa;SAuEb,GAvEa;SAwEb,GAxEa;SAyEb,GAzEa;SA0Eb,GA1Ea;SA2Eb,GA3Ea;SA4Eb,GA5Ea;SA6Eb,GA7Ea;SA8Eb,GA9Ea;SA+Eb,GA/Ea;SAgFb,GAhFa;SAiFb,GAjFa;SAkFb,GAlFa;SAmFb,GAnFa;SAoFb,GApFa;SAqFb,GArFa;SAsFb,GAtFa;SAuFb,GAvFa;SAwFb,GAxFa;SAyFb,GAzFa;SA0Fb,GA1Fa;SA2Fb,GA3Fa;SA4Fb,GA5Fa;SA6Fb,GA7Fa;SA8Fb,GA9Fa;SA+Fb,GA/Fa;SAgGb,GAhGa;SAiGb,GAjGa;SAkGb,GAlGa;SAmGb,GAnGa;SAoGb,GApGa;SAqGb,GArGa;SAsGb,GAtGa;SAuGb,GAvGa;SAwGb,GAxGa;SAyGb,GAzGa;SA0Gb,GA1Ga;SA2Gb,GA3Ga;SA4Gb,GA5Ga;SA6Gb,GA7Ga;SA8Gb,GA9Ga;SA+Gb,GA/Ga;SAgHb,GAhHa;SAiHb,GAjHa;SAkHb,GAlHa;SAmHb,GAnHa;UAoHZ,GApHY;UAqHZ,GArHY;SAsHb,GAtHa;SAuHb,GAvHa;SAwHb,GAxHa;SAyHb,GAzHa;SA0Hb,GA1Ha;SA2Hb,GA3Ha;SA4Hb,GA5Ha;SA6Hb,GA7Ha;SA8Hb,GA9Ha;SA+Hb,GA/Ha;SAgIb,GAhIa;SAiIb,GAjIa;SAkIb,GAlIa;SAmIb,GAnIa;SAoIb,GApIa;SAqIb,GArIa;SAsIb,GAtIa;SAuIb,GAvIa;SAwIb,GAxIa;SAyIb,GAzIa;SA0Ib,GA1Ia;SA2Ib,GA3Ia;SA4Ib,GA5Ia;SA6Ib,GA7Ia;SA8Ib,GA9Ia;SA+Ib,GA/Ia;SAgJb,GAhJa;SAiJb,GAjJa;SAkJb,GAlJa;SAmJb,GAnJa;SAoJb,GApJa;SAqJb,GArJa;SAsJb,GAtJa;SAuJb,GAvJa;SAwJb,GAxJa;SAyJb,GAzJa;SA0Jb,GA1Ja;SA2Jb,GA3Ja;SA4Jb,GA5Ja;SA6Jb,GA7Ja;SA8Jb,IA9Ja;SA+Jb,IA/Ja;SAgKb,GAhKa;SAiKb,GAjKa;SAkKb,GAlKa;SAmKb,GAnKa;SAoKb,GApKa;SAqKb,GArKa;UAsKZ,GAtKY;UAuKZ,GAvKY;SAwKb,GAxKa;SAyKb,GAzKa;SA0Kb,GA1Ka;SA2Kb,GA3Ka;SA4Kb,GA5Ka;SA6Kb,GA7Ka;SA8Kb,GA9Ka;SA+Kb,GA/Ka;SAgLb,GAhLa;SAiLb,GAjLa;SAkLb,GAlLa;SAmLb,GAnLa;UAoLZ,GApLY;UAqLZ,GArLY;SAsLb,GAtLa;SAuLb,GAvLa;SAwLb,GAxLa;SAyLb,GAzLa;SA0Lb,GA1La;SA2Lb,GA3La;SA4Lb,GA5La;UA6LZ,GA7LY;UA8LZ,GA9LY;SA+Lb,GA/La;SAgMb,GAhMa;SAiMb,GAjMa;SAkMb,GAlMa;SAmMb,GAnMa;SAoMb,GApMa;SAqMb,IArMa;SAsMb,IAtMa;UAuMZ,GAvMY;UAwMZ,GAxMY;SAyMb,GAzMa;SA0Mb,GA1Ma;SA2Mb,GA3Ma;SA4Mb,GA5Ma;SA6Mb,GA7Ma;SA8Mb,GA9Ma;UA+MZ,GA/MY;UAgNZ,GAhNY;SAiNb,GAjNa;SAkNb,GAlNa;SAmNb,GAnNa;SAoNb,GApNa;SAqNb,GArNa;SAsNb,GAtNa;SAuNb,GAvNa;SAwNb,GAxNa;SAyNb,GAzNa;SA0Nb,GA1Na;SA2Nb,GA3Na;SA4Nb,GA5Na;SA6Nb,GA7Na;SA8Nb,GA9Na;SA+Nb,GA/Na;SAgOb,GAhOa;SAiOb,GAjOa;SAkOb,GAlOa;SAmOb,GAnOa;SAoOb,GApOa;UAqOZ,GArOY;UAsOZ,GAtOY;SAuOb,GAvOa;SAwOb,GAxOa;SAyOb,GAzOa;SA0Ob,GA1Oa;SA2Ob,GA3Oa;SA4Ob,GA5Oa;SA6Ob,GA7Oa;SA8Ob,GA9Oa;SA+Ob,GA/Oa;SAgPb,GAhPa;SAiPb,GAjPa;SAkPb,GAlPa;SAmPb,GAnPa;SAoPb,GApPa;UAqPZ,GArPY;UAsPZ,GAtPY;SAuPb,GAvPa;SAwPb,GAxPa;SAyPb,GAzPa;SA0Pb,GA1Pa;UA2PZ,GA3PY;UA4PZ,GA5PY;SA6Pb,GA7Pa;SA8Pb,GA9Pa;SA+Pb,GA/Pa;UAgQZ,GAhQY;UAiQZ,GAjQY;SAkQb,GAlQa;SAmQb,GAnQa;SAoQb,GApQa;SAqQb,GArQa;SAsQb,GAtQa;SAuQb,GAvQa;SAwQb,GAxQa;SAyQb,GAzQa;SA0Qb,GA1Qa;SA2Qb,GA3Qa;SA4Qb,GA5Qa;SA6Qb,GA7Qa;SA8Qb,GA9Qa;SA+Qb,GA/Qa;SAgRb,GAhRa;SAiRb,GAjRa;SAkRb,GAlRa;SAmRb,GAnRa;SAoRb,GApRa;SAqRb,GArRa;SAsRb,GAtRa;SAuRb,GAvRa;SAwRb,GAxRa;SAyRb,GAzRa;SA0Rb,GA1Ra;SA2Rb,GA3Ra;SA4Rb,GA5Ra;SA6Rb,GA7Ra;SA8Rb,GA9Ra;SA+Rb,GA/Ra;SAgSb,GAhSa;SAiSb,GAjSa;SAkSb,GAlSa;SAmSb,GAnSa;SAoSb,IApSa;SAqSb,IArSa;SAsSb,GAtSa;SAuSb,GAvSa;SAwSb,IAxSa;SAySb,IAzSa;SA0Sb,GA1Sa;SA2Sb,GA3Sa;SA4Sb,GA5Sa;SA6Sb,GA7Sa;UA8SZ,GA9SY;UA+SZ,GA/SY;SAgTb,GAhTa;SAiTb,GAjTa;SAkTb,GAlTa;SAmTb,GAnTa;UAoTZ,GApTY;UAqTZ,GArTY;UAsTZ,GAtTY;UAuTZ,GAvTY;UAwTZ,GAxTY;UAyTZ,GAzTY;SA0Tb,GA1Ta;SA2Tb,GA3Ta;SA4Tb,GA5Ta;SA6Tb,GA7Ta;SA8Tb,GA9Ta;SA+Tb,GA/Ta;SAgUb,GAhUa;SAiUb,GAjUa;SAkUb,GAlUa;SAmUb,GAnUa;SAoUb,GApUa;SAqUb,GArUa;SAsUb,GAtUa;SAuUb,GAvUa;SAwUb,GAxUa;SAyUb,GAzUa;SA0Ub,GA1Ua;SA2Ub,GA3Ua;SA4Ub,GA5Ua;SA6Ub,GA7Ua;SA8Ub,GA9Ua;SA+Ub,GA/Ua;SAgVb,GAhVa;SAiVb,GAjVa;UAkVZ,GAlVY;UAmVZ,GAnVY;UAoVZ,GApVY;UAqVZ,GArVY;UAsVZ,GAtVY;UAuVZ,GAvVY;UAwVZ,GAxVY;UAyVZ,GAzVY;SA0Vb,GA1Va;SA2Vb,GA3Va;SA4Vb,GA5Va;SA6Vb,GA7Va;UA8VZ,GA9VY;SA+Vb,GA/Va;SAgWb,GAhWa;SAiWb,GAjWa;UAkWZ,GAlWY;UAmWZ,GAnWY;UAoWZ,GApWY;UAqWZ,GArWY;UAsWZ,GAtWY;UAuWZ,GAvWY;UAwWZ,GAxWY;UAyWZ,GAzWY;SA0Wb,GA1Wa;SA2Wb,GA3Wa;UA4WZ,GA5WY;UA6WZ,GA7WY;UA8WZ,GA9WY;UA+WZ,GA/WY;UAgXZ,GAhXY;UAiXZ,GAjXY;UAkXZ,GAlXY;UAmXZ,GAnXY;UAoXZ,GApXY;UAqXZ,GArXY;UAsXZ,GAtXY;UAuXZ,GAvXY;SAwXb,GAxXa;SAyXb,GAzXa;SA0Xb,GA1Xa;SA2Xb,GA3Xa;UA4XZ,GA5XY;UA6XZ,GA7XY;SA8Xb,GA9Xa;SA+Xb,GA/Xa;UAgYZ,GAhYY;UAiYZ,GAjYY;UAkYZ,GAlYY;UAmYZ,GAnYY;UAoYZ,GApYY;UAqYZ,GArYY;UAsYZ,GAtYY;UAuYZ,GAvYY;UAwYZ,GAxYY;UAyYZ,GAzYY;UA0YZ,GA1YY;UA2YZ,GA3YY;UA4YZ,GA5YY;UA6YZ,GA7YY;UA8YZ,GA9YY;UA+YZ;GA/YP;EAkZA,IAAIC,KAAK,GAAGlB,MAAM,CAACe,IAAP,CAAYE,YAAZ,EAA0BE,IAA1B,CAA+B,GAA/B,CAAZ;EACA,IAAIC,UAAU,GAAG,IAAIC,MAAJ,CAAWH,KAAX,EAAkB,GAAlB,CAAjB;EACA,IAAII,WAAW,GAAG,IAAID,MAAJ,CAAWH,KAAX,EAAkB,EAAlB,CAAlB;;EAEA,IAAIK,aAAa,GAAG,UAASC,MAAT,EAAiB;WAC7BA,MAAM,CAACC,OAAP,CAAeL,UAAf,EAA2B,UAASM,KAAT,EAAgB;aAC1CT,YAAY,CAACS,KAAD,CAAnB;KADM,CAAP;GADD;;EAMA,IAAIC,UAAU,GAAG,UAASH,MAAT,EAAiB;WAC1B,CAAC,CAACA,MAAM,CAACE,KAAP,CAAaJ,WAAb,CAAT;GADD;;EAIAM,mBAAA,GAAiBL,aAAjB;EACAK,OAAA,GAAqBD,UAArB;EACAC,UAAA,GAAwBL,aAAxB;;;;EC/ZA,IAAIM,QAAQ,GAAG;EACbC,EAAAA,oBAAoB,EAAE,CADT;EAEbC,EAAAA,KAAK,EAAE,CAFM;EAGbC,EAAAA,WAAW,EAAE,CAHA;EAIbC,EAAAA,gBAAgB,EAAE,CAJL;EAKbC,EAAAA,QAAQ,EAAE,CALG;EAMbC,EAAAA,OAAO,EAAE,CANI;EAObC,EAAAA,OAAO,EAAE,CAPI;EAQbC,EAAAA,QAAQ,EAAE;EARG,CAAf;EAUAC,WAAW,CAACT,QAAZ,GAAuBA,QAAvB;;EAEA,IAAIU,iBAAiB,GAAG,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EACtC,SAAOC,MAAM,CAACF,CAAC,CAACG,WAAH,CAAN,CAAsBC,aAAtB,CAAoCF,MAAM,CAACD,CAAC,CAACE,WAAH,CAA1C,CAAP;EACD,CAFD;EAGA;;;;;;;;;EASA,SAASL,WAAT,CAAqBO,KAArB,EAA4BC,KAA5B,EAAmCC,OAAnC,EAA4C;EAC1C,MAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;EACtBA,IAAAA,OAAO,GAAG,EAAV;EACD;;EAED,MAAIC,QAAQ,GAAGD,OAAf;EAAA,MACIhC,IAAI,GAAGiC,QAAQ,CAACjC,IADpB;EAAA,MAEIkC,kBAAkB,GAAGD,QAAQ,CAACE,SAFlC;EAAA,MAGIA,SAAS,GAAGD,kBAAkB,KAAK,KAAK,CAA5B,GAAgCpB,QAAQ,CAACO,OAAzC,GAAmDa,kBAHnE;EAAA,MAIIE,iBAAiB,GAAGH,QAAQ,CAACI,QAJjC;EAAA,MAKIA,QAAQ,GAAGD,iBAAiB,KAAK,KAAK,CAA3B,GAA+BZ,iBAA/B,GAAmDY,iBALlE;EAMA,MAAIE,YAAY,GAAGR,KAAK,CAACS,MAAN,CAAaC,mBAAb,EAAkC,EAAlC,CAAnB;EACA,SAAOF,YAAY,CAACG,IAAb,CAAkB,UAAUhB,CAAV,EAAaC,CAAb,EAAgB;EACvC,WAAOgB,gBAAgB,CAACjB,CAAD,EAAIC,CAAJ,EAAOW,QAAP,CAAvB;EACD,GAFM,EAEJM,GAFI,CAEA,UAAUC,IAAV,EAAgB;EACrB,QAAIC,IAAI,GAAGD,IAAI,CAACC,IAAhB;EACA,WAAOA,IAAP;EACD,GALM,CAAP;;EAOA,WAASL,mBAAT,CAA6BM,OAA7B,EAAsCD,IAAtC,EAA4CE,KAA5C,EAAmD;EACjD,QAAIC,WAAW,GAAGC,iBAAiB,CAACJ,IAAD,EAAO7C,IAAP,EAAa+B,KAAb,EAAoBC,OAApB,CAAnC;EACA,QAAIkB,IAAI,GAAGF,WAAW,CAACE,IAAvB;EAAA,QACIC,qBAAqB,GAAGH,WAAW,CAACI,YADxC;EAAA,QAEIA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAA/B,GAAmChB,SAAnC,GAA+CgB,qBAFlE;;EAIA,QAAID,IAAI,IAAIE,YAAZ,EAA0B;EACxBN,MAAAA,OAAO,CAACO,IAAR,CAAarE,UAAQ,CAAC,EAAD,EAAKgE,WAAL,EAAkB;EACrCH,QAAAA,IAAI,EAAEA,IAD+B;EAErCE,QAAAA,KAAK,EAAEA;EAF8B,OAAlB,CAArB;EAID;;EAED,WAAOD,OAAP;EACD;EACF;EACD;;;;;;;;;;EAUA,SAASG,iBAAT,CAA2BJ,IAA3B,EAAiC7C,IAAjC,EAAuC+B,KAAvC,EAA8CC,OAA9C,EAAuD;EACrD,MAAI,CAAChC,IAAL,EAAW;EACT;EACA,QAAIsD,UAAU,GAAGT,IAAjB;EACA,WAAO;EACL;EACAjB,MAAAA,WAAW,EAAE0B,UAFR;EAGLJ,MAAAA,IAAI,EAAEK,eAAe,CAACD,UAAD,EAAavB,KAAb,EAAoBC,OAApB,CAHhB;EAILwB,MAAAA,QAAQ,EAAE,CAAC,CAJN;EAKLJ,MAAAA,YAAY,EAAEpB,OAAO,CAACG;EALjB,KAAP;EAOD;;EAED,MAAIsB,YAAY,GAAGC,kBAAkB,CAACb,IAAD,EAAO7C,IAAP,CAArC;EACA,SAAOyD,YAAY,CAAClB,MAAb,CAAoB,UAAUoB,KAAV,EAAiBC,KAAjB,EAAwBxE,CAAxB,EAA2B;EACpD,QAAI8D,IAAI,GAAGS,KAAK,CAACT,IAAjB;EAAA,QACItB,WAAW,GAAG+B,KAAK,CAAC/B,WADxB;EAAA,QAEI4B,QAAQ,GAAGG,KAAK,CAACH,QAFrB;EAAA,QAGIJ,YAAY,GAAGO,KAAK,CAACP,YAHzB;EAIA,QAAIS,SAAS,GAAGD,KAAK,CAACC,SAAtB;EAAA,QACIC,UAAU,GAAGF,KAAK,CAACE,UADvB;EAEA,QAAIC,OAAO,GAAGR,eAAe,CAACM,SAAD,EAAY9B,KAAZ,EAAmBC,OAAnB,CAA7B;EACA,QAAIgC,cAAc,GAAGpC,WAArB;EACA,QAAIqC,UAAU,GAAGH,UAAU,CAACG,UAA5B;EAAA,QACIC,UAAU,GAAGJ,UAAU,CAACI,UAD5B;EAAA,QAEI/B,SAAS,GAAG2B,UAAU,CAAC3B,SAF3B;;EAIA,QAAI4B,OAAO,GAAGE,UAAV,IAAwBF,OAAO,IAAIjD,QAAQ,CAACO,OAAhD,EAAyD;EACvD0C,MAAAA,OAAO,GAAGE,UAAV;EACD,KAFD,MAEO,IAAIF,OAAO,GAAGG,UAAd,EAA0B;EAC/BH,MAAAA,OAAO,GAAGG,UAAV;EACD;;EAED,QAAIH,OAAO,GAAGb,IAAd,EAAoB;EAClBA,MAAAA,IAAI,GAAGa,OAAP;EACAP,MAAAA,QAAQ,GAAGpE,CAAX;EACAgE,MAAAA,YAAY,GAAGjB,SAAf;EACA6B,MAAAA,cAAc,GAAGH,SAAjB;EACD;;EAED,WAAO;EACLjC,MAAAA,WAAW,EAAEoC,cADR;EAELd,MAAAA,IAAI,EAAEA,IAFD;EAGLM,MAAAA,QAAQ,EAAEA,QAHL;EAILJ,MAAAA,YAAY,EAAEA;EAJT,KAAP;EAMD,GAhCM,EAgCJ;EACDxB,IAAAA,WAAW,EAAEiB,IADZ;EAEDK,IAAAA,IAAI,EAAEpC,QAAQ,CAACQ,QAFd;EAGDkC,IAAAA,QAAQ,EAAE,CAAC,CAHV;EAIDJ,IAAAA,YAAY,EAAEpB,OAAO,CAACG;EAJrB,GAhCI,CAAP;EAsCD;EACD;;;;;;;;;EASA,SAASoB,eAAT,CAAyBY,UAAzB,EAAqCC,YAArC,EAAmDpC,OAAnD,EAA4D;EAC1DmC,EAAAA,UAAU,GAAGE,yBAAyB,CAACF,UAAD,EAAanC,OAAb,CAAtC;EACAoC,EAAAA,YAAY,GAAGC,yBAAyB,CAACD,YAAD,EAAepC,OAAf,CAAxC,CAF0D;;EAI1D,MAAIoC,YAAY,CAAC9E,MAAb,GAAsB6E,UAAU,CAAC7E,MAArC,EAA6C;EAC3C,WAAOwB,QAAQ,CAACQ,QAAhB;EACD,GANyD;;;EAS1D,MAAI6C,UAAU,KAAKC,YAAnB,EAAiC;EAC/B,WAAOtD,QAAQ,CAACC,oBAAhB;EACD,GAXyD;;;EAc1DoD,EAAAA,UAAU,GAAGA,UAAU,CAACG,WAAX,EAAb;EACAF,EAAAA,YAAY,GAAGA,YAAY,CAACE,WAAb,EAAf,CAf0D;;EAiB1D,MAAIH,UAAU,KAAKC,YAAnB,EAAiC;EAC/B,WAAOtD,QAAQ,CAACE,KAAhB;EACD,GAnByD;;;EAsB1D,MAAImD,UAAU,CAACI,UAAX,CAAsBH,YAAtB,CAAJ,EAAyC;EACvC,WAAOtD,QAAQ,CAACG,WAAhB;EACD,GAxByD;;;EA2B1D,MAAIkD,UAAU,CAACK,QAAX,CAAoB,MAAMJ,YAA1B,CAAJ,EAA6C;EAC3C,WAAOtD,QAAQ,CAACI,gBAAhB;EACD,GA7ByD;;;EAgC1D,MAAIiD,UAAU,CAACK,QAAX,CAAoBJ,YAApB,CAAJ,EAAuC;EACrC,WAAOtD,QAAQ,CAACK,QAAhB;EACD,GAFD,MAEO,IAAIiD,YAAY,CAAC9E,MAAb,KAAwB,CAA5B,EAA+B;EACpC;EACA;EACA;EACA,WAAOwB,QAAQ,CAACQ,QAAhB;EACD,GAvCyD;;;EA0C1D,MAAImD,UAAU,CAACN,UAAD,CAAV,CAAuBK,QAAvB,CAAgCJ,YAAhC,CAAJ,EAAmD;EACjD,WAAOtD,QAAQ,CAACM,OAAhB;EACD,GA5CyD;EA6C1D;;;EAGA,SAAOsD,mBAAmB,CAACP,UAAD,EAAaC,YAAb,CAA1B;EACD;EACD;;;;;;;;EAQA,SAASK,UAAT,CAAoBhE,MAApB,EAA4B;EAC1B,MAAIkE,OAAO,GAAG,EAAd;EACA,MAAIC,aAAa,GAAGnE,MAAM,CAACoE,KAAP,CAAa,GAAb,CAApB;EACAD,EAAAA,aAAa,CAACE,OAAd,CAAsB,UAAUC,YAAV,EAAwB;EAC5C,QAAIC,kBAAkB,GAAGD,YAAY,CAACF,KAAb,CAAmB,GAAnB,CAAzB;EACAG,IAAAA,kBAAkB,CAACF,OAAnB,CAA2B,UAAUG,iBAAV,EAA6B;EACtDN,MAAAA,OAAO,IAAIM,iBAAiB,CAACC,MAAlB,CAAyB,CAAzB,EAA4B,CAA5B,CAAX;EACD,KAFD;EAGD,GALD;EAMA,SAAOP,OAAP;EACD;EACD;;;;;;;;;;;;EAYA,SAASD,mBAAT,CAA6BP,UAA7B,EAAyCC,YAAzC,EAAuD;EACrD,MAAIe,wBAAwB,GAAG,CAA/B;EACA,MAAIC,UAAU,GAAG,CAAjB;;EAEA,WAASC,qBAAT,CAA+BC,SAA/B,EAA0C7E,MAA1C,EAAkDsC,KAAlD,EAAyD;EACvD,SAAK,IAAIwC,CAAC,GAAGxC,KAAb,EAAoBwC,CAAC,GAAG9E,MAAM,CAACnB,MAA/B,EAAuCiG,CAAC,EAAxC,EAA4C;EAC1C,UAAIC,UAAU,GAAG/E,MAAM,CAAC8E,CAAD,CAAvB;;EAEA,UAAIC,UAAU,KAAKF,SAAnB,EAA8B;EAC5BH,QAAAA,wBAAwB,IAAI,CAA5B;EACA,eAAOI,CAAC,GAAG,CAAX;EACD;EACF;;EAED,WAAO,CAAC,CAAR;EACD;;EAED,WAASE,UAAT,CAAoBC,MAApB,EAA4B;EAC1B,QAAIC,iBAAiB,GAAGR,wBAAwB,GAAGf,YAAY,CAAC9E,MAAhE;EACA,QAAIsG,OAAO,GAAG9E,QAAQ,CAACO,OAAT,GAAmBsE,iBAAiB,IAAI,IAAID,MAAR,CAAlD;EACA,WAAOE,OAAP;EACD;;EAED,MAAIC,UAAU,GAAGR,qBAAqB,CAACjB,YAAY,CAAC,CAAD,CAAb,EAAkBD,UAAlB,EAA8B,CAA9B,CAAtC;;EAEA,MAAI0B,UAAU,GAAG,CAAjB,EAAoB;EAClB,WAAO/E,QAAQ,CAACQ,QAAhB;EACD;;EAED8D,EAAAA,UAAU,GAAGS,UAAb;;EAEA,OAAK,IAAIzG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgF,YAAY,CAAC9E,MAAjC,EAAyCF,CAAC,EAA1C,EAA8C;EAC5C,QAAIkG,SAAS,GAAGlB,YAAY,CAAChF,CAAD,CAA5B;EACAgG,IAAAA,UAAU,GAAGC,qBAAqB,CAACC,SAAD,EAAYnB,UAAZ,EAAwBiB,UAAxB,CAAlC;EACA,QAAIU,KAAK,GAAGV,UAAU,GAAG,CAAC,CAA1B;;EAEA,QAAI,CAACU,KAAL,EAAY;EACV,aAAOhF,QAAQ,CAACQ,QAAhB;EACD;EACF;;EAED,MAAIoE,MAAM,GAAGN,UAAU,GAAGS,UAA1B;EACA,SAAOJ,UAAU,CAACC,MAAD,CAAjB;EACD;EACD;;;;;;;;EAQA,SAAShD,gBAAT,CAA0BjB,CAA1B,EAA6BC,CAA7B,EAAgCW,QAAhC,EAA0C;EACxC,MAAI0D,MAAM,GAAG,CAAC,CAAd;EACA,MAAIC,MAAM,GAAG,CAAb;EACA,MAAIC,KAAK,GAAGxE,CAAC,CAACyB,IAAd;EAAA,MACIgD,SAAS,GAAGzE,CAAC,CAAC+B,QADlB;EAEA,MAAI2C,KAAK,GAAGzE,CAAC,CAACwB,IAAd;EAAA,MACIkD,SAAS,GAAG1E,CAAC,CAAC8B,QADlB;;EAGA,MAAIyC,KAAK,KAAKE,KAAd,EAAqB;EACnB,QAAID,SAAS,KAAKE,SAAlB,EAA6B;EAC3B;EACA,aAAO/D,QAAQ,CAACZ,CAAD,EAAIC,CAAJ,CAAf;EACD,KAHD,MAGO;EACL,aAAOwE,SAAS,GAAGE,SAAZ,GAAwBL,MAAxB,GAAiCC,MAAxC;EACD;EACF,GAPD,MAOO;EACL,WAAOC,KAAK,GAAGE,KAAR,GAAgBJ,MAAhB,GAAyBC,MAAhC;EACD;EACF;EACD;;;;;;;;EAQA,SAAS3B,yBAAT,CAAmCtC,KAAnC,EAA0CsE,KAA1C,EAAiD;EAC/C,MAAIC,cAAc,GAAGD,KAAK,CAACC,cAA3B,CAD+C;EAG/C;;EACAvE,EAAAA,KAAK,GAAG,KAAKA,KAAb,CAJ+C;;EAM/C,MAAI,CAACuE,cAAL,EAAqB;EACnBvE,IAAAA,KAAK,GAAGvB,eAAa,CAACuB,KAAD,CAArB;EACD;;EAED,SAAOA,KAAP;EACD;EACD;;;;;;;;EAQA,SAASwE,aAAT,CAAuB1D,IAAvB,EAA6BrD,GAA7B,EAAkC;EAChC,MAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;EAC3BA,IAAAA,GAAG,GAAGA,GAAG,CAACA,GAAV;EACD;;EAED,MAAIuC,KAAJ;;EAEA,MAAI,OAAOvC,GAAP,KAAe,UAAnB,EAA+B;EAC7BuC,IAAAA,KAAK,GAAGvC,GAAG,CAACqD,IAAD,CAAX,CAD6B;EAE9B,GAFD,MAEO;EACLd,IAAAA,KAAK,GAAGyE,cAAc,CAAChH,GAAD,EAAMqD,IAAN,CAAtB;EACD,GAX+B;EAchC;;;EACA,SAAOd,KAAK,IAAI,IAAT,GAAgB,GAAG0E,MAAH,CAAU1E,KAAV,CAAhB,GAAmC,IAA1C;EACD;EACD;;;;;;;;;EASA,SAASyE,cAAT,CAAwBhH,GAAxB,EAA6BkH,GAA7B,EAAkC;EAChC;EACA,SAAOlH,GAAG,CAACqF,KAAJ,CAAU,GAAV,EAAetC,MAAf,CAAsB,UAAUoE,OAAV,EAAmBC,SAAnB,EAA8B;EACzD;EACA,WAAOD,OAAO,GAAGA,OAAO,CAACC,SAAD,CAAV,GAAwB,IAAtC;EACD,GAHM,EAGJF,GAHI,CAAP;EAID;EACD;;;;;;;;EAQA,SAAShD,kBAAT,CAA4Bb,IAA5B,EAAkC7C,IAAlC,EAAwC;EACtC,SAAOA,IAAI,CAACuC,MAAL,CAAY,UAAUsE,OAAV,EAAmBrH,GAAnB,EAAwB;EACzC,QAAIsH,MAAM,GAAGP,aAAa,CAAC1D,IAAD,EAAOrD,GAAP,CAA1B;;EAEA,QAAIsH,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAAChC,OAAP,CAAe,UAAUjB,SAAV,EAAqB;EAClCgD,QAAAA,OAAO,CAACxD,IAAR,CAAa;EACXQ,UAAAA,SAAS,EAAEA,SADA;EAEXC,UAAAA,UAAU,EAAEiD,gBAAgB,CAACvH,GAAD;EAFjB,SAAb;EAID,OALD;EAMD;;EAED,WAAOqH,OAAP;EACD,GAbM,EAaJ,EAbI,CAAP;EAcD;;EAED,IAAIG,oBAAoB,GAAG;EACzB9C,EAAAA,UAAU,EAAE+C,QADa;EAEzBhD,EAAAA,UAAU,EAAE,CAACgD;EAFY,CAA3B;EAIA;;;;;;EAMA,SAASF,gBAAT,CAA0BvH,GAA1B,EAA+B;EAC7B,MAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;EAC3B,WAAOwH,oBAAP;EACD;;EAED,SAAOhI,UAAQ,CAAC,EAAD,EAAKgI,oBAAL,EAA2BxH,GAA3B,CAAf;EACD;;EClYD,IAAM0H,OAAO,GAAG,SAAVA,OAAU,CAAC1H,GAAD,EAA0B;EACxC,MAAI;EACF,QAAMqE,SAAS,GAAGsD,YAAY,CAACD,OAAb,CAAqB1H,GAArB,CAAlB;;EACA,QAAI,OAAOqE,SAAP,KAAqB,QAAzB,EAAmC;EACjC,aAAOuD,IAAI,CAACC,KAAL,CAAWxD,SAAX,CAAP;EACD;;EACD,WAAOyD,SAAP;EACD,GAND,CAME,gBAAM;EACN,WAAOA,SAAP;EACD;EACF,CAVD;;EAYe,SAASC,eAAT,CACb/H,GADa,EAEbgI,YAFa,EAG+C;EAAA,wBAClCC,cAAK,CAACC,QAAN,EADkC;EAAA,MACrD3F,KADqD;EAAA,MAC9C4F,QAD8C;;EAG5DF,EAAAA,cAAK,CAACG,SAAN,CAAgB,YAAM;EACpB,QAAMC,YAAY,GAAGX,OAAO,CAAC1H,GAAD,CAA5B;;EAEA,QAAI,OAAOqI,YAAP,KAAwB,WAAxB,IAAuCA,YAAY,KAAK,IAA5D,EAAkE;EAChEF,MAAAA,QAAQ,CACN,OAAOH,YAAP,KAAwB,UAAxB,GAAqCA,YAAY,EAAjD,GAAsDA,YADhD,CAAR;EAGD,KAJD,MAIO;EACLG,MAAAA,QAAQ,CAACE,YAAD,CAAR;EACD;EACF,GAVD,EAUG,CAACL,YAAD,EAAehI,GAAf,CAVH;EAYA,MAAMsI,MAAM,GAAGL,cAAK,CAACM,WAAN,CACb,UAAAC,OAAO,EAAI;EACTL,IAAAA,QAAQ,CAAC,UAAAM,GAAG,EAAI;EACd,UAAIC,MAAM,GAAGF,OAAb;;EAEA,UAAI,OAAOA,OAAP,IAAkB,UAAtB,EAAkC;EAChCE,QAAAA,MAAM,GAAGF,OAAO,CAACC,GAAD,CAAhB;EACD;;EACD,UAAI;EACFd,QAAAA,YAAY,CAACgB,OAAb,CAAqB3I,GAArB,EAA0B4H,IAAI,CAACgB,SAAL,CAAeF,MAAf,CAA1B;EACD,OAFD,CAEE,iBAAM;;EAER,aAAOA,MAAP;EACD,KAXO,CAAR;EAYD,GAdY,EAeb,CAAC1I,GAAD,CAfa,CAAf;EAkBA,SAAO,CAACuC,KAAD,EAAQ+F,MAAR,CAAP;EACD;;ECjDM,IAAMO,YAAY,GAAG;EAC1BC,EAAAA,UAAU,EAAE,SADc;EAE1BC,EAAAA,aAAa,EAAE,SAFW;EAG1BC,EAAAA,UAAU,EAAE,OAHc;EAI1BC,EAAAA,IAAI,EAAE,SAJoB;EAK1BC,EAAAA,OAAO,EAAE,SALiB;EAM1BC,EAAAA,oBAAoB,EAAE,MANI;EAO1BC,EAAAA,cAAc,EAAE,MAPU;EAQ1BC,EAAAA,OAAO,EAAE,SARiB;EAS1BC,EAAAA,MAAM,EAAE,SATkB;EAU1BC,EAAAA,MAAM,EAAE,SAVkB;EAW1BC,EAAAA,OAAO,EAAE;EAXiB,CAArB;EAoBP,IAAMC,YAAY,gBAAGxB,cAAK,CAACyB,aAAN,CAAoBb,YAApB,CAArB;EAEO,SAASc,aAAT,OAA0D;EAAA,MAAjCC,KAAiC,QAAjCA,KAAiC;EAAA,MAAvBC,IAAuB;;EAC/D,sBAAO5B,6BAAC,YAAD,CAAc,QAAd;EAAuB,IAAA,KAAK,EAAE2B;EAA9B,KAAyCC,IAAzC,EAAP;EACD;EAEM,SAASC,QAAT,GAAoB;EACzB,SAAO7B,cAAK,CAAC8B,UAAN,CAAiBN,YAAjB,CAAP;EACD;;EC5Bc,SAASO,aAAT,CAAuBC,KAAvB,EAA2D;EACxE;EADwE,wBAE1ChC,cAAK,CAACC,QAAN,CAAe,YAAM;EACjD,QAAI,OAAOgC,MAAP,KAAkB,WAAtB,EAAmC;EACjC,aAAOA,MAAM,CAACC,UAAP,IAAqBD,MAAM,CAACC,UAAP,CAAkBF,KAAlB,EAAyB3G,OAArD;EACD;EACF,GAJ6B,CAF0C;EAAA,MAEjE8G,OAFiE;EAAA,MAExDC,UAFwD;;;EASxEpC,EAAAA,cAAK,CAACG,SAAN,CAAgB,YAAM;EACpB,QAAI,OAAO8B,MAAP,KAAkB,WAAtB,EAAmC;EACjC,UAAI,CAACA,MAAM,CAACC,UAAZ,EAAwB;EACtB;EACD,OAHgC;;;EAMjC,UAAMG,OAAO,GAAGJ,MAAM,CAACC,UAAP,CAAkBF,KAAlB,CAAhB,CANiC;;EASjC,UAAMM,QAAQ,GAAG,SAAXA,QAAW;EAAA,YAAGjH,OAAH,QAAGA,OAAH;EAAA,eACf+G,UAAU,CAAC/G,OAAD,CADK;EAAA,OAAjB,CATiC;;;EAajCgH,MAAAA,OAAO,CAACE,WAAR,CAAoBD,QAApB;EAEA,aAAO,YAAM;EACX;EACAD,QAAAA,OAAO,CAACG,cAAR,CAAuBF,QAAvB;EACD,OAHD;EAID;EACF,GArBD,EAqBG,CAACH,OAAD,EAAUH,KAAV,EAAiBI,UAAjB,CArBH;EAuBA,SAAOD,OAAP;EACD;;EC7BM,IAAMM,QAAQ,GAAG,OAAOR,MAAP,KAAkB,WAAnC;EAqBA,SAASS,mBAAT,CAA6BV,KAA7B,EAA2CL,KAA3C,EAAyD;EAC9D,SAAOK,KAAK,CAACW,KAAN,CAAYC,UAAZ,GACHjB,KAAK,CAACL,MADH,GAEH,CAACU,KAAK,CAACa,iBAAN,EAAD,GACAlB,KAAK,CAACX,IADN,GAEAgB,KAAK,CAACc,OAAN,KACAnB,KAAK,CAACJ,OADN,GAEAI,KAAK,CAACP,OANV;EAOD;EAEM,SAAS2B,mBAAT,CAA6Bf,KAA7B,EAA2C;EAChD,SAAOA,KAAK,CAACW,KAAN,CAAYC,UAAZ,GACH,UADG,GAEH,CAACZ,KAAK,CAACa,iBAAN,EAAD,GACA,UADA,GAEAb,KAAK,CAACc,OAAN,KACA,OADA,GAEA,OANJ;EAOD;EAMM,SAASE,MAAT,CACLC,IADK,EAELC,SAFK,EAGLC,OAHK,EAIL;EAAA,MADAA,OACA;EADAA,IAAAA,OACA,GADkC,EAClC;EAAA;;EACA,sBAAOnD,cAAK,CAACoD,UAAN,CACL,gBAAqBC,GAArB,EAA6B;EAAA,QAA1BC,KAA0B,QAA1BA,KAA0B;EAAA,QAAhB1B,IAAgB;;EAC3B,QAAMD,KAAK,GAAGE,QAAQ,EAAtB;EAEA,QAAM0B,WAAW,GAAG/L,MAAM,CAACgM,OAAP,CAAeL,OAAf,EAAwBrI,MAAxB,CAClB,UAAC2I,OAAD,SAA2B;EAAA,UAAhB1L,GAAgB;EAAA,UAAXuC,KAAW;EACzB;EACA,aAAOyH,aAAa,CAAChK,GAAD,CAAb,gBAEE0L,OAFF,EAGG,OAAOnJ,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAACsH,IAAD,EAAOD,KAAP,CAAnC,GAAmDrH,KAHtD,IAKHmJ,OALJ;EAMD,KATiB,EAUlB,EAVkB,CAApB;EAaA,wBAAOzD,cAAK,CAAC0D,aAAN,CAAoBT,IAApB,eACFrB,IADE;EAEL0B,MAAAA,KAAK,eACC,OAAOJ,SAAP,KAAqB,UAArB,GACAA,SAAS,CAACtB,IAAD,EAAOD,KAAP,CADT,GAEAuB,SAHD,EAIAI,KAJA,EAKAC,WALA,CAFA;EASLF,MAAAA,GAAG,EAAHA;EATK,OAAP;EAWD,GA5BI,CAAP;EA8BD;EAEM,SAASM,YAAT,GAAwB;EAC7B,MAAMC,UAAU,GAAG5D,cAAK,CAAC6D,MAAN,CAAa,KAAb,CAAnB;EACA,MAAMC,SAAS,GAAG9D,cAAK,CAACM,WAAN,CAAkB;EAAA,WAAMsD,UAAU,CAACH,OAAjB;EAAA,GAAlB,EAA4C,EAA5C,CAAlB;EAEAzD,EAAAA,cAAK,CAACyC,QAAQ,GAAG,WAAH,GAAiB,iBAA1B,CAAL,CAAkD,YAAM;EACtDmB,IAAAA,UAAU,CAACH,OAAX,GAAqB,IAArB;EACA,WAAO,YAAM;EACXG,MAAAA,UAAU,CAACH,OAAX,GAAqB,KAArB;EACD,KAFD;EAGD,GALD,EAKG,EALH;EAOA,SAAOK,SAAP;EACD;EAED;;;;;;EAKO,SAASC,YAAT,CAAyBC,YAAzB,EAAmE;EACxE,MAAMF,SAAS,GAAGH,YAAY,EAA9B;;EADwE,wBAE9C3D,cAAK,CAACC,QAAN,CAAe+D,YAAf,CAF8C;EAAA,MAEjErB,KAFiE;EAAA,MAE1DsB,QAF0D;;EAIxE,MAAMC,YAAY,GAAGlE,cAAK,CAACM,WAAN,CACnB,UAAChG,KAAD,EAAc;EACZ6J,IAAAA,iBAAiB,CAAC,YAAM;EACtB,UAAIL,SAAS,EAAb,EAAiB;EACfG,QAAAA,QAAQ,CAAC3J,KAAD,CAAR;EACD;EACF,KAJgB,CAAjB;EAKD,GAPkB,EAQnB,CAACwJ,SAAD,CARmB,CAArB;EAWA,SAAO,CAACnB,KAAD,EAAQuB,YAAR,CAAP;EACD;EAED;;;;;EAIO,IAAME,YAAY,GAAG,SAAfA,YAAe,CAAC9J,KAAD,EAAoB;EAC9C,MAAM+J,IAAI,GAAG7M,MAAM,CAAC8M,mBAAP,CAA2B9M,MAAM,CAAC8C,KAAD,CAAjC,CAAb;EACA,MAAMiK,QAAQ,GAAG,OAAOjK,KAAP,KAAiB,QAAjB,GAA+BA,KAAK,CAACkK,QAAN,EAA/B,SAAqDlK,KAAtE;EAEA,SAAOqF,IAAI,CAACgB,SAAL,CAAe4D,QAAf,EAAyBF,IAAzB,CAAP;EACD,CALM;EAOP;;;;;EAIA,SAASF,iBAAT,CAA2BM,QAA3B,EAAiD;EAC/CC,EAAAA,OAAO,CAACC,OAAR,GACGC,IADH,CACQH,QADR,EAEGI,KAFH,CAES,UAAAC,KAAK;EAAA,WACVC,UAAU,CAAC,YAAM;EACf,YAAMD,KAAN;EACD,KAFS,CADA;EAAA,GAFd;EAOD;;EClJM,IAAME,KAAK,GAAGhC,MAAM,CACzB,KADyB,EAEzB,UAACiC,MAAD,EAAStD,KAAT;EAAA,SAAoB;EAClBuD,IAAAA,QAAQ,EAAE,0BADQ;EAElBC,IAAAA,UAAU,cAFQ;EAGlBC,IAAAA,OAAO,EAAE,MAHS;EAIlBC,IAAAA,eAAe,EAAE1D,KAAK,CAACd,UAJL;EAKlByE,IAAAA,KAAK,EAAE3D,KAAK,CAACZ;EALK,GAApB;EAAA,CAFyB,EASzB;EACE,wBAAsB;EACpBwE,IAAAA,aAAa,EAAE;EADK,GADxB;EAIE,wBAAsB;EACpBL,IAAAA,QAAQ,EAAE,MADU;;EAAA;EAJxB,CATyB,CAApB;EAoBA,IAAMM,gBAAgB,GAAGxC,MAAM,CACpC,KADoC,EAEpC;EAAA,SAAO;EACLyC,IAAAA,IAAI,EAAE,WADD;EAELL,IAAAA,OAAO,EAAE,MAFJ;EAGLG,IAAAA,aAAa,EAAE,QAHV;EAILG,IAAAA,QAAQ,EAAE,MAJL;EAKLC,IAAAA,MAAM,EAAE;EALH,GAAP;EAAA,CAFoC,EASpC;EACE,wBAAsB,uBAACV,MAAD,EAAStD,KAAT;EAAA,WAAoB;EACxCiE,MAAAA,SAAS,iBAAejE,KAAK,CAACX;EADU,KAApB;EAAA;EADxB,CAToC,CAA/B;EAgBA,IAAM6E,MAAM,GAAG7C,MAAM,CAAC,QAAD,EAAW,UAAC8C,KAAD,EAAQnE,KAAR;EAAA,SAAmB;EACxDoE,IAAAA,UAAU,EAAE,MAD4C;EAExDb,IAAAA,QAAQ,EAAE,MAF8C;EAGxDc,IAAAA,UAAU,EAAE,MAH4C;EAIxDnF,IAAAA,UAAU,EAAEc,KAAK,CAACX,IAJsC;EAKxDiF,IAAAA,MAAM,EAAE,GALgD;EAMxDC,IAAAA,YAAY,EAAE,MAN0C;EAOxDZ,IAAAA,KAAK,EAAE,OAPiD;EAQxDa,IAAAA,OAAO,EAAE,MAR+C;EASxDC,IAAAA,OAAO,EAAEN,KAAK,CAACO,QAAN,GAAiB,IAAjB,GAAwBxG,SATuB;EAUxDyG,IAAAA,MAAM,EAAE;EAVgD,GAAnB;EAAA,CAAX,CAArB;EAaA,IAAMC,SAAS,GAAGvD,MAAM,CAAC,MAAD,EAAS;EACtCoC,EAAAA,OAAO,EAAE,cAD6B;EAEtCF,EAAAA,QAAQ,EAAE;EAF4B,CAAT,CAAxB;EAKA,IAAMsB,QAAQ,GAAGxD,MAAM,CAAC,MAAD,EAAS;EACrCoC,EAAAA,OAAO,EAAE,aAD4B;EAErCqB,EAAAA,UAAU,EAAE,QAFyB;EAGrCN,EAAAA,OAAO,EAAE,WAH4B;EAIrCH,EAAAA,UAAU,EAAE,MAJyB;EAKrCU,EAAAA,UAAU,EAAE,gBALyB;EAMrCR,EAAAA,YAAY,EAAE;EANuB,CAAT,CAAvB;EASA,IAAMS,IAAI,GAAG3D,MAAM,CAAC,MAAD,EAAS;EACjCkC,EAAAA,QAAQ,EAAE,MADuB;EAEjCI,EAAAA,KAAK,EAAE,SAF0B;EAGjCzE,EAAAA,UAAU,EAAE;EAHqB,CAAT,CAAnB;EAMA,IAAM+F,KAAK,GAAG5D,MAAM,CAAC,OAAD,EAAU,UAACiC,MAAD,EAAStD,KAAT;EAAA,SAAoB;EACvD0D,IAAAA,eAAe,EAAE1D,KAAK,CAACT,oBADgC;EAEvD+E,IAAAA,MAAM,EAAE,CAF+C;EAGvDC,IAAAA,YAAY,EAAE,MAHyC;EAIvDZ,IAAAA,KAAK,EAAE3D,KAAK,CAACR,cAJ0C;EAKvD+D,IAAAA,QAAQ,EAAE,MAL6C;EAMvD2B,IAAAA,UAAU,OAN6C;EAOvDV,IAAAA,OAAO,EAAE;EAP8C,GAApB;EAAA,CAAV,CAApB;EAUA,IAAMW,MAAM,GAAG9D,MAAM,CAC1B,QAD0B,EAE1B,UAACiC,MAAD,EAAStD,KAAT;EAAA,SAAoB;EAClByD,IAAAA,OAAO,gBADW;EAElBF,IAAAA,QAAQ,QAFU;EAGlBC,IAAAA,UAAU,cAHQ;EAIlBa,IAAAA,UAAU,EAAE,QAJM;EAKlBa,IAAAA,UAAU,OALQ;EAMlBV,IAAAA,OAAO,wBANW;EAOlBR,IAAAA,MAAM,EAAE,MAPU;EAQlBM,IAAAA,MAAM,EAAE,CARU;EASlBC,IAAAA,YAAY,QATM;EAUlBH,IAAAA,UAAU,QAVQ;EAWlBgB,IAAAA,gBAAgB,EAAE,MAXA;EAYlB1B,IAAAA,eAAe,EAAE1D,KAAK,CAACT,oBAZL;EAalB8F,IAAAA,eAAe,kKAbG;EAclBC,IAAAA,gBAAgB,aAdE;EAelBC,IAAAA,kBAAkB,sBAfA;EAgBlBC,IAAAA,cAAc,oBAhBI;EAiBlB7B,IAAAA,KAAK,EAAE3D,KAAK,CAACR;EAjBK,GAApB;EAAA,CAF0B,EAqB1B;EACE,wBAAsB;EACpBiE,IAAAA,OAAO,EAAE;EADW;EADxB,CArB0B,CAArB;;EC7EA,IAAMgC,KAAK,GAAGpE,MAAM,CAAC,KAAD,EAAQ;EACjCmC,EAAAA,UAAU,EAAE,kBADqB;EAEjCD,EAAAA,QAAQ,EAAE,KAFuB;EAGjC2B,EAAAA,UAAU,EAAE,KAHqB;EAIjCQ,EAAAA,OAAO,EAAE,MAJwB;EAKjCC,EAAAA,SAAS,EAAE;EALsB,CAAR,CAApB;EAQA,IAAMC,KAAK,GAAGvE,MAAM,CAAC,MAAD,EAAS;EAClCsC,EAAAA,KAAK,EAAE;EAD2B,CAAT,CAApB;EAIA,IAAMkC,WAAW,GAAGxE,MAAM,CAAC,QAAD,EAAW;EAC1CsD,EAAAA,MAAM,EAAE,SADkC;EAE1ChB,EAAAA,KAAK,EAAE;EAFmC,CAAX,CAA1B;EAKA,IAAMmC,YAAY,GAAGzE,MAAM,CAAC,QAAD,EAAW;EAC3CsD,EAAAA,MAAM,EAAE,SADmC;EAE3ChB,EAAAA,KAAK,EAAE,SAFoC;EAG3CoC,EAAAA,IAAI,EAAE,SAHqC;EAI3CL,EAAAA,OAAO,EAAE,SAJkC;EAK3CxG,EAAAA,UAAU,EAAE,aAL+B;EAM3CoF,EAAAA,MAAM,EAAE,MANmC;EAO3CE,EAAAA,OAAO,EAAE;EAPkC,CAAX,CAA3B;EAUA,IAAMwB,KAAK,GAAG3E,MAAM,CAAC,MAAD,EAAS,UAACiC,MAAD,EAAStD,KAAT;EAAA,SAAoB;EACtD2D,IAAAA,KAAK,EAAE3D,KAAK,CAACN;EADyC,GAApB;EAAA,CAAT,CAApB;EAIA,IAAMuG,UAAU,GAAG5E,MAAM,CAAC,KAAD,EAAQ;EACtC6E,EAAAA,UAAU,EAAE,MAD0B;EAEtCC,EAAAA,WAAW,EAAE,KAFyB;EAGtCC,EAAAA,UAAU,EAAE;EAH0B,CAAR,CAAzB;EAMA,IAAMC,IAAI,GAAGhF,MAAM,CAAC,MAAD,EAAS;EACjCsC,EAAAA,KAAK,EAAE,MAD0B;EAEjCJ,EAAAA,QAAQ,EAAE;EAFuB,CAAT,CAAnB;EAUA,IAAM+C,QAAQ,GAAG,SAAXA,QAAW;EAAA,MAAGC,QAAH,QAAGA,QAAH;EAAA,wBAAa5E,KAAb;EAAA,MAAaA,KAAb,2BAAqB,EAArB;EAAA,sBACtBtD;EACE,IAAA,KAAK;EACHoF,MAAAA,OAAO,EAAE,cADN;EAEH+C,MAAAA,UAAU,EAAE,cAFT;EAGHC,MAAAA,SAAS,eAAYF,QAAQ,GAAG,EAAH,GAAQ,CAA5B,eAAqC5E,KAAK,CAAC8E,SAAN,IAAmB,EAAxD;EAHN,OAIA9E,KAJA;EADP,cADsB;EAAA,CAAjB;;EA6BP;;;;;;;;;EASO,SAAS+E,UAAT,CAAuBC,KAAvB,EAAmCC,IAAnC,EAAwD;EAC7D,MAAIA,IAAI,GAAG,CAAX,EAAc,OAAO,EAAP;EACd,MAAI5Q,CAAC,GAAG,CAAR;EACA,MAAM6Q,MAAa,GAAG,EAAtB;;EACA,SAAO7Q,CAAC,GAAG2Q,KAAK,CAACzQ,MAAjB,EAAyB;EACvB2Q,IAAAA,MAAM,CAAC5M,IAAP,CAAY0M,KAAK,CAACG,KAAN,CAAY9Q,CAAZ,EAAeA,CAAC,GAAG4Q,IAAnB,CAAZ;EACA5Q,IAAAA,CAAC,GAAGA,CAAC,GAAG4Q,IAAR;EACD;;EACD,SAAOC,MAAP;EACD;EAIM,IAAME,eAAyB,GAAG,SAA5BA,eAA4B,QAUnC;EAAA,MATJC,WASI,SATJA,WASI;EAAA,MARJC,KAQI,SARJA,KAQI;EAAA,MAPJtO,KAOI,SAPJA,KAOI;EAAA,+BANJuO,UAMI;EAAA,MANJA,UAMI,iCANS,EAMT;EAAA,kCALJC,aAKI;EAAA,MALJA,aAKI,oCALY,EAKZ;EAAA,MAJJ7F,IAII,SAJJA,IAII;EAAA,6BAHJiF,QAGI;EAAA,MAHJA,QAGI,+BAHO,KAGP;EAAA,MAFJa,cAEI,SAFJA,cAEI;EAAA,MADJC,QACI,SADJA,QACI;;EAAA,wBACsChJ,cAAK,CAACC,QAAN,CAAyB,EAAzB,CADtC;EAAA,MACGgJ,aADH;EAAA,MACkBC,gBADlB;;EAGJ,sBACElJ,6BAAC,KAAD;EAAO,IAAA,GAAG,EAAE4I;EAAZ,KACG,CAAAE,aAAa,QAAb,YAAAA,aAAa,CAAEjR,MAAf,iBACCmI,yEACEA,6BAAC,YAAD;EAAc,IAAA,OAAO,EAAE;EAAA,aAAM+I,cAAc,EAApB;EAAA;EAAvB,kBACE/I,6BAAC,QAAD;EAAU,IAAA,QAAQ,EAAEkI;EAApB,IADF,OACoCU,KADpC,EAC2C,GAD3C,eAEE5I,6BAAC,IAAD,QACG9F,MAAM,CAAC+I,IAAD,CAAN,CAAapG,WAAb,OAA+B,UAA/B,GAA4C,aAA5C,GAA4D,EAD/D,EAEGgM,UAAU,CAAChR,MAFd,OAEuBgR,UAAU,CAAChR,MAAX,GAAoB,CAApB,mBAFvB,CAFF,CADF,EAQGqQ,QAAQ,GACPY,aAAa,CAACjR,MAAd,KAAyB,CAAzB,gBACEmI,6BAAC,UAAD,QACG6I,UAAU,CAAC3N,GAAX,CAAe,UAAAiO,KAAK;EAAA,wBACnBnJ,6BAAC,WAAD;EAAa,MAAA,GAAG,EAAEmJ,KAAK,CAACP,KAAxB;EAA+B,MAAA,KAAK,EAAEO;EAAtC,MADmB;EAAA,GAApB,CADH,CADF,gBAOEnJ,6BAAC,UAAD,QACG8I,aAAa,CAAC5N,GAAd,CAAkB,UAACsI,OAAD,EAAUlI,KAAV;EAAA,wBACjB0E;EAAK,MAAA,GAAG,EAAE1E;EAAV,oBACE0E,6BAAC,KAAD,qBACEA,6BAAC,WAAD;EACE,MAAA,OAAO,EAAE;EAAA,eACPkJ,gBAAgB,CAAC,UAAA1I,GAAG;EAAA,iBAClBA,GAAG,CAACzD,QAAJ,CAAazB,KAAb,IACIkF,GAAG,CAAC4I,MAAJ,CAAW,UAAAC,CAAC;EAAA,mBAAIA,CAAC,KAAK/N,KAAV;EAAA,WAAZ,CADJ,aAEQkF,GAFR,GAEalF,KAFb,EADkB;EAAA,SAAJ,CADT;EAAA;EADX,oBASE0E,6BAAC,QAAD;EAAU,MAAA,QAAQ,EAAEkI;EAApB,MATF,QASqC5M,KAAK,GAAG0N,QAT7C,UAS2D,GAT3D,EAUG1N,KAAK,GAAG0N,QAAR,GAAmBA,QAAnB,GAA8B,CAVjC,MADF,EAaGC,aAAa,CAAClM,QAAd,CAAuBzB,KAAvB,iBACC0E,6BAAC,UAAD,QACGwD,OAAO,CAACtI,GAAR,CAAY,UAAAiO,KAAK;EAAA,0BAChBnJ,6BAAC,WAAD;EAAa,QAAA,GAAG,EAAEmJ,KAAK,CAACP,KAAxB;EAA+B,QAAA,KAAK,EAAEO;EAAtC,QADgB;EAAA,KAAjB,CADH,CADD,GAMG,IAnBN,CADF,CADiB;EAAA,GAAlB,CADH,CARK,GAoCL,IA5CN,CADD,gBAgDCnJ,yEACEA,6BAAC,KAAD,QAAQ4I,KAAR,MADF,oBAC0B5I,6BAAC,KAAD,QAAQoE,YAAY,CAAC9J,KAAD,CAApB,CAD1B,CAjDJ,CADF;EAwDD,CArEM;;EAoFP,SAASgP,UAAT,CAAoBC,CAApB,EAAoD;EAClD,SAAOC,MAAM,CAACC,QAAP,IAAmBF,CAA1B;EACD;;EAEc,SAASG,QAAT,QAMG;EAAA,MALhBpP,KAKgB,SALhBA,KAKgB;EAAA,MAJhBqP,eAIgB,SAJhBA,eAIgB;EAAA,6BAHhBC,QAGgB;EAAA,MAHhBA,QAGgB,+BAHLlB,eAGK;EAAA,6BAFhBM,QAEgB;EAAA,MAFhBA,QAEgB,+BAFL,GAEK;EAAA,MADbpH,IACa;;EAAA,yBACgB5B,cAAK,CAACC,QAAN,CAAe4J,OAAO,CAACF,eAAD,CAAtB,CADhB;EAAA,MACTzB,QADS;EAAA,MACC4B,WADD;;EAEhB,MAAMf,cAAc,GAAG/I,cAAK,CAACM,WAAN,CAAkB;EAAA,WAAMwJ,WAAW,CAAC,UAAAtJ,GAAG;EAAA,aAAI,CAACA,GAAL;EAAA,KAAJ,CAAjB;EAAA,GAAlB,EAAkD,EAAlD,CAAvB;EAEA,MAAIyC,IAAY,GAAG,OAAO3I,KAA1B;EACA,MAAIuO,UAAsB,GAAG,EAA7B;;EAEA,MAAMkB,YAAY,GAAG,SAAfA,YAAe,CAACC,GAAD,EAAsD;EAAA;;EACzE,QAAMC,kBAAkB,GACtBN,eAAe,KAAK,IAApB,sBACOK,GAAG,CAACpB,KADX,IACmB,IADnB,WAEIe,eAFJ,oBAEIA,eAAe,CAAGK,GAAG,CAACpB,KAAP,CAHrB;EAIA,wBACKoB,GADL;EAEEL,MAAAA,eAAe,EAAEM;EAFnB;EAID,GATD;;EAWA,MAAIC,KAAK,CAACC,OAAN,CAAc7P,KAAd,CAAJ,EAA0B;EACxB2I,IAAAA,IAAI,GAAG,OAAP;EACA4F,IAAAA,UAAU,GAAGvO,KAAK,CAACY,GAAN,CAAU,UAACmO,CAAD,EAAI1R,CAAJ;EAAA,aACrBoS,YAAY,CAAC;EACXnB,QAAAA,KAAK,EAAEjR,CAAC,CAAC6M,QAAF,EADI;EAEXlK,QAAAA,KAAK,EAAE+O;EAFI,OAAD,CADS;EAAA,KAAV,CAAb;EAMD,GARD,MAQO,IACL/O,KAAK,KAAK,IAAV,IACA,OAAOA,KAAP,KAAiB,QADjB,IAEAgP,UAAU,CAAChP,KAAD,CAFV,IAGA,OAAOA,KAAK,CAACkP,MAAM,CAACC,QAAR,CAAZ,KAAkC,UAJ7B,EAKL;EACAxG,IAAAA,IAAI,GAAG,UAAP;EACA4F,IAAAA,UAAU,GAAGqB,KAAK,CAACE,IAAN,CAAW9P,KAAX,EAAkB,UAAC+P,GAAD,EAAM1S,CAAN;EAAA,aAC7BoS,YAAY,CAAC;EACXnB,QAAAA,KAAK,EAAEjR,CAAC,CAAC6M,QAAF,EADI;EAEXlK,QAAAA,KAAK,EAAE+P;EAFI,OAAD,CADiB;EAAA,KAAlB,CAAb;EAMD,GAbM,MAaA,IAAI,OAAO/P,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,KAAK,IAA3C,EAAiD;EACtD2I,IAAAA,IAAI,GAAG,QAAP;EACA4F,IAAAA,UAAU,GAAGrR,MAAM,CAACgM,OAAP,CAAelJ,KAAf,EAAsBY,GAAtB,CAA0B;EAAA,UAAEnD,GAAF;EAAA,UAAOsS,GAAP;EAAA,aACrCN,YAAY,CAAC;EACXnB,QAAAA,KAAK,EAAE7Q,GADI;EAEXuC,QAAAA,KAAK,EAAE+P;EAFI,OAAD,CADyB;EAAA,KAA1B,CAAb;EAMD;;EAED,MAAMvB,aAAa,GAAGT,UAAU,CAACQ,UAAD,EAAaG,QAAb,CAAhC;EAEA,SAAOY,QAAQ;EACbjB,IAAAA,WAAW,EAAE;EAAA,UAAGQ,KAAH,SAAGA,KAAH;EAAA,0BACXnJ,6BAAC,QAAD;EAAU,QAAA,KAAK,EAAE1F,KAAjB;EAAwB,QAAA,QAAQ,EAAEsP;EAAlC,SAAgDhI,IAAhD,EAA0DuH,KAA1D,EADW;EAAA,KADA;EAIblG,IAAAA,IAAI,EAAJA,IAJa;EAKb4F,IAAAA,UAAU,EAAVA,UALa;EAMbC,IAAAA,aAAa,EAAbA,aANa;EAObxO,IAAAA,KAAK,EAALA,KAPa;EAQb4N,IAAAA,QAAQ,EAARA,QARa;EASba,IAAAA,cAAc,EAAdA,cATa;EAUbC,IAAAA,QAAQ,EAARA;EAVa,KAWVpH,IAXU,EAAf;EAaD;;EClQc,SAAS0I,IAAT,CAAcxE,KAAd,EAA0B;EACvC,sBACEyE;EACE,IAAA,KAAK,EAAC,MADR;EAEE,IAAA,MAAM,EAAC,MAFT;EAGE,IAAA,OAAO,EAAC,aAHV;EAIE,IAAA,OAAO,EAAC;EAJV,KAKMzE,KALN,gBAOEyE;EAAG,IAAA,MAAM,EAAC,MAAV;EAAiB,IAAA,WAAW,EAAC,GAA7B;EAAiC,IAAA,IAAI,EAAC,MAAtC;EAA6C,IAAA,QAAQ,EAAC;EAAtD,kBACEA;EAAG,IAAA,SAAS,EAAC;EAAb,kBACEA;EACE,IAAA,CAAC,EAAC,02EADJ;EAEE,IAAA,IAAI,EAAC,SAFP;EAGE,IAAA,QAAQ,EAAC,SAHX;EAIE,IAAA,SAAS,EAAC;EAJZ,IADF,eAOEA;EACE,IAAA,CAAC,EAAC,qwFADJ;EAEE,IAAA,IAAI,EAAC;EAFP,IAPF,eAWEA;EACE,IAAA,CAAC,EAAC,m0GADJ;EAEE,IAAA,IAAI,EAAC;EAFP,IAXF,CADF,CAPF,CADF;EA4BD;;EC6CM,SAASC,IAAT,GAA2B;EAChC,SAAO3K,SAAP;EACD;;ECeD,IAAM4C,UAAQ,GAAG,OAAOR,MAAP,KAAkB,WAAnC;EAEO,SAASwI,kBAAT,OAQwC;EAAA,MAP7CC,aAO6C,QAP7CA,aAO6C;EAAA,6BAN7CC,UAM6C;EAAA,MAN7CA,UAM6C,gCANhC,EAMgC;EAAA,mCAL7CC,gBAK6C;EAAA,MAL7CA,gBAK6C,sCAL1B,EAK0B;EAAA,mCAJ7CC,iBAI6C;EAAA,MAJ7CA,iBAI6C,sCAJzB,EAIyB;EAAA,2BAH7CC,QAG6C;EAAA,MAH7CA,QAG6C,8BAHlC,aAGkC;EAAA,mCAF7CC,gBAE6C;EAAA,MAF3BC,SAE2B,sCAFf,OAEe;EAAA,MAD7CC,UAC6C,QAD7CA,UAC6C;EAC7C,MAAMC,OAAO,GAAGlL,cAAK,CAAC6D,MAAN,CAA6B,IAA7B,CAAhB;EACA,MAAMsH,QAAQ,GAAGnL,cAAK,CAAC6D,MAAN,CAA6B,IAA7B,CAAjB;;EAF6C,yBAGjB/D,eAAe,CACzC,wBADyC,EAEzC4K,aAFyC,CAHE;EAAA,MAGtCU,MAHsC;EAAA,MAG9BC,SAH8B;;EAAA,0BAODvL,eAAe,CACzD,0BADyD,EAEzD,IAFyD,CAPd;EAAA,MAOtCwL,cAPsC;EAAA,MAOtBC,iBAPsB;;EAAA,sBAWDxH,YAAY,CAAC,KAAD,CAXX;EAAA,MAWtCyH,cAXsC;EAAA,MAWtBC,iBAXsB;;EAAA,uBAYT1H,YAAY,CAAC,KAAD,CAZH;EAAA,MAYtC2H,UAZsC;EAAA,MAY1BC,aAZ0B;;EAa7C,MAAM7H,SAAS,GAAGH,YAAY,EAA9B;;EAEA,MAAMiI,gBAAe,GAAG,SAAlBA,eAAkB,CACtBC,YADsB,EAEtBC,UAFsB,EAGnB;EAAA;;EACH,QAAIA,UAAU,CAACC,MAAX,KAAsB,CAA1B,EAA6B,OAD1B;;EAGHJ,IAAAA,aAAa,CAAC,IAAD,CAAb;EAEA,QAAMK,QAAQ,GAAG;EACfC,MAAAA,cAAc,2BAAEJ,YAAF,oBAAEA,YAAY,CAAEK,qBAAd,GAAsCvG,MAAxC,oCAAkD,CADjD;EAEfwG,MAAAA,KAAK,EAAEL,UAAU,CAACK;EAFH,KAAjB;;EAKA,QAAMC,GAAG,GAAG,SAANA,GAAM,CAACC,SAAD,EAA2B;EACrC,UAAMC,KAAK,GAAGN,QAAQ,CAACG,KAAT,GAAiBE,SAAS,CAACF,KAAzC;EACA,UAAMI,SAAS,GAAG,CAAAP,QAAQ,QAAR,YAAAA,QAAQ,CAAEC,cAAV,IAA2BK,KAA7C;EAEAf,MAAAA,iBAAiB,CAACgB,SAAD,CAAjB;;EAEA,UAAIA,SAAS,GAAG,EAAhB,EAAoB;EAClBlB,QAAAA,SAAS,CAAC,KAAD,CAAT;EACD,OAFD,MAEO;EACLA,QAAAA,SAAS,CAAC,IAAD,CAAT;EACD;EACF,KAXD;;EAaA,QAAMmB,KAAK,GAAG,SAARA,KAAQ,GAAM;EAClBb,MAAAA,aAAa,CAAC,KAAD,CAAb;EACAc,MAAAA,QAAQ,CAACC,mBAAT,CAA6B,WAA7B,EAA0CN,GAA1C;EACAK,MAAAA,QAAQ,CAACC,mBAAT,CAA6B,SAA7B,EAAwCF,KAAxC;EACD,KAJD;;EAMAC,IAAAA,QAAQ,CAACE,gBAAT,CAA0B,WAA1B,EAAuCP,GAAvC;EACAK,IAAAA,QAAQ,CAACE,gBAAT,CAA0B,SAA1B,EAAqCH,KAArC;EACD,GAlCD;;EAoCAxM,EAAAA,cAAK,CAACG,SAAN,CAAgB,YAAM;EACpBsL,IAAAA,iBAAiB,CAACL,MAAD,WAACA,MAAD,GAAW,KAAX,CAAjB;EACD,GAFD,EAEG,CAACA,MAAD,EAASI,cAAT,EAAyBC,iBAAzB,CAFH,EAnD6C;EAwD7C;;EACAzL,EAAAA,cAAK,CAACG,SAAN,CAAgB,YAAM;EACpB,QAAMkD,GAAG,GAAG8H,QAAQ,CAAC1H,OAArB;;EACA,QAAIJ,GAAJ,EAAS;EACP,UAAMuJ,0BAA0B,GAAG,SAA7BA,0BAA6B,GAAM;EACvC,YAAIvJ,GAAG,IAAImI,cAAX,EAA2B;EACzBnI,UAAAA,GAAG,CAACC,KAAJ,CAAUuJ,UAAV,GAAuB,SAAvB;EACD;EACF,OAJD;;EAMA,UAAMC,wBAAwB,GAAG,SAA3BA,wBAA2B,GAAM;EACrC,YAAIzJ,GAAG,IAAI,CAACmI,cAAZ,EAA4B;EAC1BnI,UAAAA,GAAG,CAACC,KAAJ,CAAUuJ,UAAV,GAAuB,QAAvB;EACD;EACF,OAJD;;EAMAxJ,MAAAA,GAAG,CAACsJ,gBAAJ,CAAqB,iBAArB,EAAwCC,0BAAxC;EACAvJ,MAAAA,GAAG,CAACsJ,gBAAJ,CAAqB,eAArB,EAAsCG,wBAAtC;EAEA,aAAO,YAAM;EACXzJ,QAAAA,GAAG,CAACqJ,mBAAJ,CAAwB,iBAAxB,EAA2CE,0BAA3C;EACAvJ,QAAAA,GAAG,CAACqJ,mBAAJ,CAAwB,eAAxB,EAAyCI,wBAAzC;EACD,OAHD;EAID;EACF,GAvBD,EAuBG,CAACtB,cAAD,CAvBH;EAyBAxL,EAAAA,cAAK,CAACyC,UAAQ,GAAG,WAAH,GAAiB,iBAA1B,CAAL,CAAkD,YAAM;EACtD,QAAI+I,cAAJ,EAAoB;EAAA;;EAClB,UAAMuB,aAAa,uBAAG7B,OAAO,CAACzH,OAAX,8CAAG,iBAAiBuJ,aAApB,qBAAG,sBAAgC1J,KAAhC,CAAsC2J,aAA5D;;EAEA,UAAMb,GAAG,GAAG,SAANA,GAAM,GAAM;EAAA;;EAChB,YAAMc,eAAe,wBAAG/B,QAAQ,CAAC1H,OAAZ,qBAAG,kBAAkByI,qBAAlB,GAA0CvG,MAAlE;;EACA,iCAAIuF,OAAO,CAACzH,OAAZ,qBAAI,kBAAiBuJ,aAArB,EAAoC;EAClC9B,UAAAA,OAAO,CAACzH,OAAR,CAAgBuJ,aAAhB,CAA8B1J,KAA9B,CAAoC2J,aAApC,GAAuDC,eAAvD;EACD;EACF,OALD;;EAOAd,MAAAA,GAAG;;EAEH,UAAI,OAAOnK,MAAP,KAAkB,WAAtB,EAAmC;EACjCA,QAAAA,MAAM,CAAC0K,gBAAP,CAAwB,QAAxB,EAAkCP,GAAlC;EAEA,eAAO,YAAM;EAAA;;EACXnK,UAAAA,MAAM,CAACyK,mBAAP,CAA2B,QAA3B,EAAqCN,GAArC;;EACA,cACE,sBAAAlB,OAAO,CAACzH,OAAR,uCAAiBuJ,aAAjB,KACA,OAAOD,aAAP,KAAyB,QAF3B,EAGE;EACA7B,YAAAA,OAAO,CAACzH,OAAR,CAAgBuJ,aAAhB,CAA8B1J,KAA9B,CAAoC2J,aAApC,GAAoDF,aAApD;EACD;EACF,SARD;EASD;EACF;EACF,GA3BD,EA2BG,CAACvB,cAAD,CA3BH;;EAlF6C,0BA+GUb,UA/GV,CA+GrCrH,KA/GqC;EAAA,MA+G9B6J,UA/G8B,kCA+GjB,EA/GiB;EAAA,MA+GVC,eA/GU,iCA+GUzC,UA/GV;;EAAA,8BAqHzCC,gBArHyC,CAkH3CtH,KAlH2C;EAAA,MAkHpC+J,gBAlHoC,sCAkHjB,EAlHiB;EAAA,MAmHlCC,YAnHkC,GAqHzC1C,gBArHyC,CAmH3C2C,OAnH2C;EAAA,MAoHxCC,qBApHwC,iCAqHzC5C,gBArHyC;;EAAA,8BA2HzCC,iBA3HyC,CAwH3CvH,KAxH2C;EAAA,MAwHpCmK,iBAxHoC,sCAwHhB,EAxHgB;EAAA,MAyHlCC,aAzHkC,GA2HzC7C,iBA3HyC,CAyH3C0C,OAzH2C;EAAA,MA0HxCI,sBA1HwC,iCA2HzC9C,iBA3HyC;;;EA8H7C,MAAI,CAAC/G,SAAS,EAAd,EAAkB,OAAO,IAAP;EAElB,sBACE9D,6BAAC,SAAD;EACE,IAAA,GAAG,EAAEkL,OADP;EAEE,IAAA,SAAS,EAAC,oBAFZ;EAGE,kBAAW;EAHb,kBAKElL,6BAAC,aAAD;EAAe,IAAA,KAAK,EAAE2B;EAAtB,kBACE3B,6BAAC,uBAAD;EACE,IAAA,GAAG,EAAEmL,QADP;EAEE,IAAA,UAAU,EAAEF;EAFd,KAGMmC,eAHN;EAIE,IAAA,KAAK;EACHtC,MAAAA,QAAQ,EAAE,OADP;EAEH8C,MAAAA,MAAM,EAAE,GAFL;EAGHC,MAAAA,KAAK,EAAE,GAHJ;EAIHC,MAAAA,MAAM,EAAE,KAJL;EAKHC,MAAAA,KAAK,EAAE,MALJ;EAMHpI,MAAAA,MAAM,EAAE2F,cAAF,WAAEA,cAAF,GAAoB,GANvB;EAOH0C,MAAAA,SAAS,EAAE,KAPR;EAQHC,MAAAA,SAAS,EAAE,yBARR;EASHrI,MAAAA,SAAS,iBAAejE,YAAK,CAACX,IAT3B;EAUHkN,MAAAA,eAAe,EAAE,KAVd;EAWH;EACArB,MAAAA,UAAU,EAAEzB,MAAM,GAAG,SAAH,GAAe;EAZ9B,OAaA+B,UAbA,EAcCzB,UAAU,GACV;EACEvD,MAAAA,UAAU;EADZ,KADU,GAIV;EAAEA,MAAAA,UAAU;EAAZ,KAlBD,EAmBCqD,cAAc,GACd;EACEpF,MAAAA,OAAO,EAAE,CADX;EAEE+H,MAAAA,aAAa,EAAE,KAFjB;EAGE/F,MAAAA,SAAS;EAHX,KADc,GAMd;EACEhC,MAAAA,OAAO,EAAE,CADX;EAEE+H,MAAAA,aAAa,EAAE,MAFjB;EAGE/F,MAAAA,SAAS;EAHX,KAzBD,CAJP;EAmCE,IAAA,MAAM,EAAEoD,cAnCV;EAoCE,IAAA,SAAS,EAAEH,SApCb;EAqCE,IAAA,eAAe,EAAE,yBAAA+C,CAAC;EAAA,aAAIxC,gBAAe,CAACT,QAAQ,CAAC1H,OAAV,EAAmB2K,CAAnB,CAAnB;EAAA;EArCpB,KADF,EAwCG5C,cAAc,gBACbxL,6BAAC,MAAD;EACE,IAAA,IAAI,EAAC,QADP;EAEE,qBAAc,yBAFhB;EAGE,qBAAc,MAHhB;EAIE,qBAAc;EAJhB,KAKOwN,qBALP;EAME,IAAA,OAAO,EAAE,iBAAAY,CAAC,EAAI;EACZ/C,MAAAA,SAAS,CAAC,KAAD,CAAT;EACAiC,MAAAA,YAAY,IAAIA,YAAY,CAACc,CAAD,CAA5B;EACD,KATH;EAUE,IAAA,KAAK;EACHtD,MAAAA,QAAQ,EAAE,OADP;EAEHgD,MAAAA,MAAM,EAAE,KAFL;EAGHO,MAAAA,MAAM,EAAE,MAHL;EAIHT,MAAAA,MAAM,EAAE;EAJL,OAKC9C,QAAQ,KAAK,WAAb,GACA;EACE+C,MAAAA,KAAK,EAAE;EADT,KADA,GAIA/C,QAAQ,KAAK,UAAb,GACA;EACEwD,MAAAA,IAAI,EAAE;EADR,KADA,GAIAxD,QAAQ,KAAK,cAAb,GACA;EACE+C,MAAAA,KAAK,EAAE;EADT,KADA,GAIA;EACES,MAAAA,IAAI,EAAE;EADR,KAjBD,EAoBAjB,gBApBA;EAVP,cADa,GAoCX,IA5EN,CALF,EAmFG,CAAC7B,cAAD,gBACCxL;EACE,IAAA,IAAI,EAAC;EADP,KAEM2N,sBAFN;EAGE,kBAAW,2BAHb;EAIE,qBAAc,yBAJhB;EAKE,qBAAc,MALhB;EAME,qBAAc,OANhB;EAOE,IAAA,OAAO,EAAE,iBAAAS,CAAC,EAAI;EACZ/C,MAAAA,SAAS,CAAC,IAAD,CAAT;EACAqC,MAAAA,aAAa,IAAIA,aAAa,CAACU,CAAD,CAA9B;EACD,KAVH;EAWE,IAAA,KAAK;EACHvN,MAAAA,UAAU,EAAE,MADT;EAEHoF,MAAAA,MAAM,EAAE,CAFL;EAGHE,MAAAA,OAAO,EAAE,CAHN;EAIH2E,MAAAA,QAAQ,EAAE,OAJP;EAKHgD,MAAAA,MAAM,EAAE,KALL;EAMH1I,MAAAA,OAAO,EAAE,aANN;EAOHF,MAAAA,QAAQ,EAAE,OAPP;EAQHmJ,MAAAA,MAAM,EAAE,MARL;EASH/H,MAAAA,MAAM,EAAE,SATL;EAUHyH,MAAAA,KAAK,EAAE;EAVJ,OAWCjD,QAAQ,KAAK,WAAb,GACA;EACEyD,MAAAA,GAAG,EAAE,GADP;EAEEV,MAAAA,KAAK,EAAE;EAFT,KADA,GAKA/C,QAAQ,KAAK,UAAb,GACA;EACEyD,MAAAA,GAAG,EAAE,GADP;EAEED,MAAAA,IAAI,EAAE;EAFR,KADA,GAKAxD,QAAQ,KAAK,cAAb,GACA;EACE8C,MAAAA,MAAM,EAAE,GADV;EAEEC,MAAAA,KAAK,EAAE;EAFT,KADA,GAKA;EACED,MAAAA,MAAM,EAAE,GADV;EAEEU,MAAAA,IAAI,EAAE;EAFR,KA1BD,EA8BAb,iBA9BA;EAXP,mBA4CEzN,6BAAC,IAAD;EAAM;EAAN,IA5CF,CADD,GA+CG,IAlIN,CADF;EAsID;;EAED,IAAMwO,aAAa,GAAG,SAAhBA,aAAgB,CAACC,CAAD;EAAA,SACpBA,CAAC,CAAC9L,KAAF,CAAQC,UAAR,GAAqB,CAArB,GAAyB,CAAC6L,CAAC,CAAC5L,iBAAF,EAAD,GAAyB,CAAzB,GAA6B4L,CAAC,CAAC3L,OAAF,KAAc,CAAd,GAAkB,CADpD;EAAA,CAAtB;;EAGA,IAAM4L,OAAuD,GAAG;EAC9D,2BAAyB,2BAAC1U,CAAD,EAAIC,CAAJ;EAAA;;EAAA,WACvBuU,aAAa,CAACxU,CAAD,CAAb,KAAqBwU,aAAa,CAACvU,CAAD,CAAlC,2BACKyU,OAAO,CAAC,cAAD,CADZ,qBACK,0BAAAA,OAAO,EAAmB1U,CAAnB,EAAsBC,CAAtB,CADZ,GAEIuU,aAAa,CAACxU,CAAD,CAAb,GAAmBwU,aAAa,CAACvU,CAAD,CAAhC,GACA,CADA,GAEA,CAAC,CALkB;EAAA,GADqC;EAO9D,gBAAc,mBAACD,CAAD,EAAIC,CAAJ;EAAA,WAAWD,CAAC,CAAC2U,SAAF,GAAc1U,CAAC,CAAC0U,SAAhB,GAA4B,CAA5B,GAAgC,CAAC,CAA5C;EAAA,GAPgD;EAQ9D,kBAAgB,qBAAC3U,CAAD,EAAIC,CAAJ;EAAA,WACdD,CAAC,CAAC2I,KAAF,CAAQiM,aAAR,GAAwB3U,CAAC,CAAC0I,KAAF,CAAQiM,aAAhC,GAAgD,CAAhD,GAAoD,CAAC,CADvC;EAAA;EAR8C,CAAhE;MAYaC,uBAAuB,gBAAG7O,cAAK,CAACoD,UAAN,CAGrC,SAASyL,uBAAT,CAAiC/I,KAAjC,EAAwCzC,GAAxC,EAAiE;EAAA;;EAAA,sBAO7DyC,KAP6D,CAE/DsF,MAF+D;EAAA,MAE/DA,MAF+D,8BAEtD,IAFsD;EAAA,MAG/DH,UAH+D,GAO7DnF,KAP6D,CAG/DmF,UAH+D;EAAA,MAI/DI,SAJ+D,GAO7DvF,KAP6D,CAI/DuF,SAJ+D;EAAA,MAK/DO,eAL+D,GAO7D9F,KAP6D,CAK/D8F,eAL+D;EAAA,MAM5DjB,UAN4D,iCAO7D7E,KAP6D;;EASjE,MAAMgJ,WAAW,GAAGC,yBAAc,EAAlC;EACA,MAAMC,UAAU,GAAGF,WAAW,CAACG,aAAZ,EAAnB;;EAViE,0BAYzCnP,eAAe,CACrC,0BADqC,EAErCtI,MAAM,CAACe,IAAP,CAAYmW,OAAZ,EAAqB,CAArB,CAFqC,CAZ0B;EAAA,MAY1D1T,IAZ0D;EAAA,MAYpDkU,OAZoD;;EAAA,0BAiBrCpP,eAAe,CAAC,0BAAD,EAA6B,EAA7B,CAjBsB;EAAA,MAiB1DsJ,MAjB0D;EAAA,MAiBlD+F,SAjBkD;;EAAA,0BAmBjCrP,eAAe,CAC7C,4BAD6C,EAE7C,KAF6C,CAnBkB;EAAA,MAmB1DsP,QAnB0D;EAAA,MAmBhDC,WAnBgD;;EAwBjE,MAAMC,MAAM,GAAGtP,cAAK,CAACuP,OAAN,CAAc;EAAA,WAAMb,OAAO,CAAC1T,IAAD,CAAb;EAAA,GAAd,EAA6C,CAACA,IAAD,CAA7C,CAAf;EAEAgF,EAAAA,cAAK,CAACyC,UAAQ,GAAG,WAAH,GAAiB,iBAA1B,CAAL,CAAkD,YAAM;EACtD,QAAI,CAAC6M,MAAL,EAAa;EACXJ,MAAAA,OAAO,CAAC1X,MAAM,CAACe,IAAP,CAAYmW,OAAZ,EAAqB,CAArB,CAAD,CAAP;EACD;EACF,GAJD,EAIG,CAACQ,OAAD,EAAUI,MAAV,CAJH;;EA1BiE,uBAgCnBvL,YAAY,CACxDvM,MAAM,CAAC6H,MAAP,CAAc2P,UAAU,CAACQ,OAAX,EAAd,CADwD,CAhCO;EAAA,MAgC1DC,eAhC0D;EAAA,MAgCzCC,kBAhCyC;;EAAA,0BAoCnB5P,eAAe,CAC3D,mCAD2D,EAE3D,EAF2D,CApCI;EAAA,MAoC1D6P,eApC0D;EAAA,MAoCzCC,kBApCyC;;EAyCjE,MAAMzM,OAAO,GAAGnD,cAAK,CAACuP,OAAN,CAAc,YAAM;EAClC,QAAMM,MAAM,GAAG,UAAIJ,eAAJ,EAAqBzU,IAArB,CAA0BsU,MAA1B,CAAf;;EAEA,QAAIF,QAAJ,EAAc;EACZS,MAAAA,MAAM,CAACC,OAAP;EACD;;EAED,QAAI,CAAC1G,MAAL,EAAa;EACX,aAAOyG,MAAP;EACD;;EAED,WAAO/V,WAAW,CAAC+V,MAAD,EAASzG,MAAT,EAAiB;EAAE7Q,MAAAA,IAAI,EAAE,CAAC,WAAD;EAAR,KAAjB,CAAX,CAAqD6Q,MAArD,CACL,UAAAC,CAAC;EAAA,aAAIA,CAAC,CAACsF,SAAN;EAAA,KADI,CAAP;EAGD,GAde,EAcb,CAACS,QAAD,EAAWE,MAAX,EAAmBG,eAAnB,EAAoCrG,MAApC,CAda,CAAhB;EAgBA,MAAM2G,WAAW,GAAG/P,cAAK,CAACuP,OAAN,CAAc,YAAM;EACtC,WAAOpM,OAAO,CAAC6M,IAAR,CAAa,UAAAhO,KAAK;EAAA,aAAIA,KAAK,CAAC2M,SAAN,KAAoBgB,eAAxB;EAAA,KAAlB,CAAP;EACD,GAFmB,EAEjB,CAACA,eAAD,EAAkBxM,OAAlB,CAFiB,CAApB;EAIA,MAAM8M,QAAQ,GAAG9M,OAAO,CAACiG,MAAR,CAAe,UAAAqF,CAAC;EAAA,WAAI1L,mBAAmB,CAAC0L,CAAD,CAAnB,KAA2B,OAA/B;EAAA,GAAhB,EACd5W,MADH;EAEA,MAAMqY,WAAW,GAAG/M,OAAO,CAACiG,MAAR,CAAe,UAAAqF,CAAC;EAAA,WAAI1L,mBAAmB,CAAC0L,CAAD,CAAnB,KAA2B,UAA/B;EAAA,GAAhB,EACjB5W,MADH;EAEA,MAAMsY,QAAQ,GAAGhN,OAAO,CAACiG,MAAR,CAAe,UAAAqF,CAAC;EAAA,WAAI1L,mBAAmB,CAAC0L,CAAD,CAAnB,KAA2B,OAA/B;EAAA,GAAhB,EACd5W,MADH;EAEA,MAAMuY,WAAW,GAAGjN,OAAO,CAACiG,MAAR,CAAe,UAAAqF,CAAC;EAAA,WAAI1L,mBAAmB,CAAC0L,CAAD,CAAnB,KAA2B,UAA/B;EAAA,GAAhB,EACjB5W,MADH;EAGAmI,EAAAA,cAAK,CAACG,SAAN,CAAgB,YAAM;EACpB,QAAIiL,MAAJ,EAAY;EACV,UAAMiF,WAAW,GAAGrB,UAAU,CAACsB,SAAX,CAAqB,YAAM;EAC7CZ,QAAAA,kBAAkB,CAAClY,MAAM,CAAC6H,MAAP,CAAc2P,UAAU,CAACuB,MAAX,EAAd,CAAD,CAAlB;EACD,OAFmB,CAApB,CADU;EAKV;;EACAb,MAAAA,kBAAkB,CAAClY,MAAM,CAAC6H,MAAP,CAAc2P,UAAU,CAACuB,MAAX,EAAd,CAAD,CAAlB;EAEA,aAAOF,WAAP;EACD;;EACD,WAAOxQ,SAAP;EACD,GAZD,EAYG,CAACuL,MAAD,EAASpQ,IAAT,EAAesU,MAAf,EAAuBF,QAAvB,EAAiCM,kBAAjC,EAAqDV,UAArD,CAZH;;EAcA,MAAMwB,aAAa,GAAG,SAAhBA,aAAgB,GAAM;EAC1B,QAAMC,OAAO,GAAGV,WAAH,oBAAGA,WAAW,CAAEW,KAAb,EAAhB;EACAD,IAAAA,OAAO,QAAP,YAAAA,OAAO,CAAE5L,KAAT,CAAe2F,IAAf;EACD,GAHD;;EAKA,sBACExK,6BAAC,aAAD;EAAe,IAAA,KAAK,EAAE2B;EAAtB,kBACE3B,6BAAC,KAAD;EACE,IAAA,GAAG,EAAEqD,GADP;EAEE,IAAA,SAAS,EAAC,yBAFZ;EAGE,kBAAW,4BAHb;EAIE,IAAA,EAAE,EAAC;EAJL,KAKMsH,UALN,gBAOE3K;EACE,IAAA,KAAK,EAAEiL,UADT;EAEE,IAAA,uBAAuB,EAAE;EACvB0F,MAAAA,MAAM,kFAEehP,YAAK,CAACb,aAFrB,SAEsCa,YAAK,CAACX,IAF5C,4UAWUW,YAAK,CAACb,aAXhB,mKAeUa,YAAK,CAACX,IAfhB,+EAiBgBW,YAAK,CAACb,aAjBtB;EADiB;EAF3B,IAPF,eAgCEd;EACE,IAAA,KAAK,EAAE;EACL8K,MAAAA,QAAQ,EAAE,UADL;EAELwD,MAAAA,IAAI,EAAE,CAFD;EAGLC,MAAAA,GAAG,EAAE,CAHA;EAILR,MAAAA,KAAK,EAAE,MAJF;EAKLpI,MAAAA,MAAM,EAAE,KALH;EAMLiL,MAAAA,YAAY,EAAE,MANT;EAOLtK,MAAAA,MAAM,EAAE,YAPH;EAQLwH,MAAAA,MAAM,EAAE;EARH,KADT;EAWE,IAAA,WAAW,EAAElC;EAXf,IAhCF,eA6CE5L;EACE,IAAA,KAAK,EAAE;EACLyF,MAAAA,IAAI,EAAE,WADD;EAELoL,MAAAA,SAAS,EAAE,KAFN;EAGL7C,MAAAA,SAAS,EAAE,MAHN;EAILtI,MAAAA,QAAQ,EAAE,MAJL;EAKLoL,MAAAA,WAAW,iBAAenP,YAAK,CAACV,OAL3B;EAMLmE,MAAAA,OAAO,EAAEgG,MAAM,GAAG,MAAH,GAAY,MANtB;EAOL7F,MAAAA,aAAa,EAAE;EAPV;EADT,kBAWEvF;EACE,IAAA,KAAK,EAAE;EACLmG,MAAAA,OAAO,EAAE,MADJ;EAELtF,MAAAA,UAAU,EAAEc,YAAK,CAACb,aAFb;EAGLsE,MAAAA,OAAO,EAAE,MAHJ;EAIL2L,MAAAA,cAAc,EAAE,eAJX;EAKLtK,MAAAA,UAAU,EAAE;EALP;EADT,kBASEzG;EACE,IAAA,IAAI,EAAC,QADP;EAEE,kBAAW,4BAFb;EAGE,qBAAc,yBAHhB;EAIE,qBAAc,MAJhB;EAKE,qBAAc,MALhB;EAME,IAAA,OAAO,EAAE;EAAA,aAAMqL,SAAS,CAAC,KAAD,CAAf;EAAA,KANX;EAOE,IAAA,KAAK,EAAE;EACLjG,MAAAA,OAAO,EAAE,aADJ;EAELvE,MAAAA,UAAU,EAAE,MAFP;EAGLoF,MAAAA,MAAM,EAAE,CAHH;EAILE,MAAAA,OAAO,EAAE,CAJJ;EAKL6K,MAAAA,WAAW,EAAE,MALR;EAML1K,MAAAA,MAAM,EAAE;EANH;EAPT,kBAgBEtG,6BAAC,IAAD;EAAM;EAAN,IAhBF,CATF,eA2BEA;EACE,IAAA,KAAK,EAAE;EACLoF,MAAAA,OAAO,EAAE,MADJ;EAELG,MAAAA,aAAa,EAAE;EAFV;EADT,kBAMEvF,6BAAC,SAAD;EAAW,IAAA,KAAK,EAAE;EAAE4Q,MAAAA,YAAY,EAAE;EAAhB;EAAlB,kBACE5Q,6BAAC,QAAD;EACE,IAAA,KAAK,EAAE;EACLa,MAAAA,UAAU,EAAEc,YAAK,CAACP,OADb;EAELgF,MAAAA,OAAO,EAAE6J,QAAQ,GAAG,CAAH,GAAO;EAFnB;EADT,4BAMQjQ,6BAAC,IAAD,aAAQiQ,QAAR,MANR,CADF,EAQc,GARd,eASEjQ,6BAAC,QAAD;EACE,IAAA,KAAK,EAAE;EACLa,MAAAA,UAAU,EAAEc,YAAK,CAACL,MADb;EAEL8E,MAAAA,OAAO,EAAE8J,WAAW,GAAG,CAAH,GAAO;EAFtB;EADT,+BAMWlQ,6BAAC,IAAD,aAAQkQ,WAAR,MANX,CATF,EAgBc,GAhBd,eAiBElQ,6BAAC,QAAD;EACE,IAAA,KAAK,EAAE;EACLa,MAAAA,UAAU,EAAEc,YAAK,CAACJ,OADb;EAEL+D,MAAAA,KAAK,EAAE,OAFF;EAGLoB,MAAAA,UAAU,EAAE,GAHP;EAILN,MAAAA,OAAO,EAAE+J,QAAQ,GAAG,CAAH,GAAO;EAJnB;EADT,4BAQQnQ,6BAAC,IAAD,aAAQmQ,QAAR,MARR,CAjBF,EA0Bc,GA1Bd,eA2BEnQ,6BAAC,QAAD;EACE,IAAA,KAAK,EAAE;EACLa,MAAAA,UAAU,EAAEc,YAAK,CAACX,IADb;EAELoF,MAAAA,OAAO,EAAEgK,WAAW,GAAG,CAAH,GAAO;EAFtB;EADT,+BAMWpQ,6BAAC,IAAD,aAAQoQ,WAAR,MANX,CA3BF,CANF,eA0CEpQ;EACE,IAAA,KAAK,EAAE;EACLoF,MAAAA,OAAO,EAAE,MADJ;EAELqB,MAAAA,UAAU,EAAE;EAFP;EADT,kBAMEzG,6BAAC,KAAD;EACE,IAAA,WAAW,EAAC,QADd;EAEE,kBAAW,qBAFb;EAGE,IAAA,KAAK,EAAEoJ,MAAF,WAAEA,MAAF,GAAY,EAHnB;EAIE,IAAA,QAAQ,EAAE,kBAAAgF,CAAC;EAAA,aAAIe,SAAS,CAACf,CAAC,CAAC1W,MAAF,CAAS4C,KAAV,CAAb;EAAA,KAJb;EAKE,IAAA,SAAS,EAAE,mBAAA8T,CAAC,EAAI;EACd,UAAIA,CAAC,CAACrW,GAAF,KAAU,QAAd,EAAwBoX,SAAS,CAAC,EAAD,CAAT;EACzB,KAPH;EAQE,IAAA,KAAK,EAAE;EACL1J,MAAAA,IAAI,EAAE,GADD;EAELuL,MAAAA,WAAW,EAAE,MAFR;EAGLjD,MAAAA,KAAK,EAAE;EAHF;EART,IANF,EAoBG,CAAC3E,MAAD,gBACCpJ,yEACEA,6BAAC,MAAD;EACE,kBAAW,cADb;EAEE,IAAA,KAAK,EAAEhF,IAFT;EAGE,IAAA,QAAQ,EAAE,kBAAAoT,CAAC;EAAA,aAAIc,OAAO,CAACd,CAAC,CAAC1W,MAAF,CAAS4C,KAAV,CAAX;EAAA,KAHb;EAIE,IAAA,KAAK,EAAE;EACLmL,MAAAA,IAAI,EAAE,GADD;EAELwL,MAAAA,QAAQ,EAAE,EAFL;EAGLD,MAAAA,WAAW,EAAE;EAHR;EAJT,KAUGxZ,MAAM,CAACe,IAAP,CAAYmW,OAAZ,EAAqBxT,GAArB,CAAyB,UAAAnD,GAAG;EAAA,wBAC3BiI;EAAQ,MAAA,GAAG,EAAEjI,GAAb;EAAkB,MAAA,KAAK,EAAEA;EAAzB,mBACWA,GADX,CAD2B;EAAA,GAA5B,CAVH,CADF,eAiBEiI,6BAAC,MAAD;EACE,IAAA,IAAI,EAAC,QADP;EAEE,IAAA,OAAO,EAAE;EAAA,aAAMqP,WAAW,CAAC,UAAA7O,GAAG;EAAA,eAAI,CAACA,GAAL;EAAA,OAAJ,CAAjB;EAAA,KAFX;EAGE,IAAA,KAAK,EAAE;EACL2F,MAAAA,OAAO,EAAE;EADJ;EAHT,KAOGiJ,QAAQ,GAAG,QAAH,GAAc,OAPzB,CAjBF,CADD,GA4BG,IAhDN,CA1CF,CA3BF,CAXF,eAoIEpP;EACE,IAAA,KAAK,EAAE;EACLkR,MAAAA,SAAS,EAAE,MADN;EAELzL,MAAAA,IAAI,EAAE;EAFD;EADT,KAMGtC,OAAO,CAACjI,GAAR,CAAY,UAAC8G,KAAD,EAAQrK,CAAR,EAAc;EACzB,QAAMwZ,UAAU,GACdnP,KAAK,CAACa,iBAAN,KAA4B,CAA5B,IAAiC,CAACb,KAAK,CAACoP,QAAN,EADpC;EAEA,wBACEpR;EACE,MAAA,GAAG,EAAEgC,KAAK,CAAC2M,SAAN,IAAmBhX,CAD1B;EAEE,MAAA,IAAI,EAAC,QAFP;EAGE,gDAAsCqK,KAAK,CAAC2M,SAH9C;EAIE,MAAA,OAAO,EAAE;EAAA,eACPiB,kBAAkB,CAChBD,eAAe,KAAK3N,KAAK,CAAC2M,SAA1B,GAAsC,EAAtC,GAA2C3M,KAAK,CAAC2M,SADjC,CADX;EAAA,OAJX;EASE,MAAA,KAAK,EAAE;EACLvJ,QAAAA,OAAO,EAAE,MADJ;EAELiM,QAAAA,YAAY,iBAAe1P,YAAK,CAACV,OAF5B;EAGLqF,QAAAA,MAAM,EAAE,SAHH;EAILzF,QAAAA,UAAU,EACRmB,KAAK,KAAK+N,WAAV,GACI,sBADJ,GAEIlQ;EAPD;EATT,oBAmBEG;EACE,MAAA,KAAK,EAAE;EACLyF,QAAAA,IAAI,EAAE,UADD;EAELsI,QAAAA,KAAK,EAAE,KAFF;EAGLpI,QAAAA,MAAM,EAAE,KAHH;EAIL9E,QAAAA,UAAU,EAAE6B,mBAAmB,CAACV,KAAD,EAAQL,YAAR,CAJ1B;EAKLyD,QAAAA,OAAO,EAAE,MALJ;EAMLqB,QAAAA,UAAU,EAAE,QANP;EAOLsK,QAAAA,cAAc,EAAE,QAPX;EAQL/K,QAAAA,UAAU,EAAE,MARP;EASLU,QAAAA,UAAU,EACR3D,mBAAmB,CAACf,KAAD,CAAnB,KAA+B,OAA/B,GACI,GADJ,GAEI,gBAZD;EAaLsD,QAAAA,KAAK,EACHvC,mBAAmB,CAACf,KAAD,CAAnB,KAA+B,OAA/B,GACI,OADJ,GAEI;EAhBD;EADT,OAoBGA,KAAK,CAACa,iBAAN,EApBH,CAnBF,EAyCGsO,UAAU,gBACTnR;EACE,MAAA,KAAK,EAAE;EACLyF,QAAAA,IAAI,EAAE,UADD;EAELE,QAAAA,MAAM,EAAE,KAFH;EAGL9E,QAAAA,UAAU,EAAEc,YAAK,CAACX,IAHb;EAILoE,QAAAA,OAAO,EAAE,MAJJ;EAKLqB,QAAAA,UAAU,EAAE,QALP;EAMLT,QAAAA,UAAU,EAAE,MANP;EAOLG,QAAAA,OAAO,EAAE;EAPJ;EADT,kBADS,GAcP,IAvDN,eAwDEnG,6BAAC,IAAD;EACE,MAAA,KAAK,EAAE;EACLmG,QAAAA,OAAO,EAAE;EADJ;EADT,YAKMnE,KAAK,CAAC2M,SALZ,CAxDF,CADF;EAkED,GArEA,CANH,CApIF,CA7CF,EAgQGoB,WAAW,gBACV/P,6BAAC,gBAAD,qBACEA;EACE,IAAA,KAAK,EAAE;EACLmG,MAAAA,OAAO,EAAE,MADJ;EAELtF,MAAAA,UAAU,EAAEc,YAAK,CAACb,aAFb;EAGLgK,MAAAA,QAAQ,EAAE,QAHL;EAILyD,MAAAA,GAAG,EAAE,CAJA;EAKLT,MAAAA,MAAM,EAAE;EALH;EADT,qBADF,eAYE9N;EACE,IAAA,KAAK,EAAE;EACLmG,MAAAA,OAAO,EAAE;EADJ;EADT,kBAKEnG;EACE,IAAA,KAAK,EAAE;EACL4Q,MAAAA,YAAY,EAAE,MADT;EAELxL,MAAAA,OAAO,EAAE,MAFJ;EAGLqB,MAAAA,UAAU,EAAE,OAHP;EAILsK,MAAAA,cAAc,EAAE;EAJX;EADT,kBAQE/Q,6BAAC,IAAD;EACE,IAAA,KAAK,EAAE;EACL6G,MAAAA,UAAU,EAAE;EADP;EADT,kBAKE7G;EACE,IAAA,KAAK,EAAE;EACLqO,MAAAA,MAAM,EAAE,CADH;EAELlI,MAAAA,OAAO,EAAE,CAFJ;EAGLT,MAAAA,QAAQ,EAAE;EAHL;EADT,KAOG/F,IAAI,CAACgB,SAAL,CAAeoP,WAAW,CAACuB,QAA3B,EAAqC,IAArC,EAA2C,CAA3C,CAPH,CALF,CARF,eAuBEtR;EACE,IAAA,KAAK,EAAE;EACLmG,MAAAA,OAAO,EAAE,YADJ;EAELD,MAAAA,YAAY,EAAE,OAFT;EAGLF,MAAAA,UAAU,EAAE,MAHP;EAILU,MAAAA,UAAU,EAAE,kBAJP;EAKL7F,MAAAA,UAAU,EAAE6B,mBAAmB,CAACqN,WAAD,EAAcpO,YAAd,CAL1B;EAML4P,MAAAA,UAAU,EAAE;EANP;EADT,KAUGxO,mBAAmB,CAACgN,WAAD,CAVtB,CAvBF,CALF,eAyCE/P;EACE,IAAA,KAAK,EAAE;EACL4Q,MAAAA,YAAY,EAAE,MADT;EAELxL,MAAAA,OAAO,EAAE,MAFJ;EAGLqB,MAAAA,UAAU,EAAE,QAHP;EAILsK,MAAAA,cAAc,EAAE;EAJX;EADT,iCAQa/Q,6BAAC,IAAD,QAAO+P,WAAW,CAAClN,iBAAZ,EAAP,CARb,CAzCF,eAmDE7C;EACE,IAAA,KAAK,EAAE;EACLoF,MAAAA,OAAO,EAAE,MADJ;EAELqB,MAAAA,UAAU,EAAE,QAFP;EAGLsK,MAAAA,cAAc,EAAE;EAHX;EADT,sBAOgB,GAPhB,eAQE/Q,6BAAC,IAAD,QACG,IAAIwR,IAAJ,CACCzB,WAAW,CAACpN,KAAZ,CAAkBiM,aADnB,EAEC6C,kBAFD,EADH,CARF,CAnDF,CAZF,eA8EEzR;EACE,IAAA,KAAK,EAAE;EACLa,MAAAA,UAAU,EAAEc,YAAK,CAACb,aADb;EAELqF,MAAAA,OAAO,EAAE,MAFJ;EAGL2E,MAAAA,QAAQ,EAAE,QAHL;EAILyD,MAAAA,GAAG,EAAE,CAJA;EAKLT,MAAAA,MAAM,EAAE;EALH;EADT,eA9EF,eAyFE9N;EACE,IAAA,KAAK,EAAE;EACLmG,MAAAA,OAAO,EAAE;EADJ;EADT,kBAKEnG,6BAAC,MAAD;EACE,IAAA,IAAI,EAAC,QADP;EAEE,IAAA,OAAO,EAAEwQ,aAFX;EAGE,IAAA,QAAQ,EAAET,WAAW,CAACpN,KAAZ,CAAkBC,UAH9B;EAIE,IAAA,KAAK,EAAE;EACL/B,MAAAA,UAAU,EAAEc,YAAK,CAACL;EADb;EAJT,eALF,EAcY,GAdZ,eAeEtB,6BAAC,MAAD;EACE,IAAA,IAAI,EAAC,QADP;EAEE,IAAA,OAAO,EAAE;EAAA,aAAM8O,WAAW,CAAC4C,iBAAZ,CAA8B3B,WAA9B,CAAN;EAAA,KAFX;EAGE,IAAA,KAAK,EAAE;EACLlP,MAAAA,UAAU,EAAEc,YAAK,CAACJ,OADb;EAEL+D,MAAAA,KAAK,EAAE3D,YAAK,CAACR;EAFR;EAHT,kBAfF,EAwBY,GAxBZ,eAyBEnB,6BAAC,MAAD;EACE,IAAA,IAAI,EAAC,QADP;EAEE,IAAA,OAAO,EAAE;EAAA,aAAM8O,WAAW,CAAC6C,YAAZ,CAAyB5B,WAAzB,CAAN;EAAA,KAFX;EAGE,IAAA,KAAK,EAAE;EACLlP,MAAAA,UAAU,EAAEc,YAAK,CAACX;EADb;EAHT,aAzBF,EAiCY,GAjCZ,eAkCEhB,6BAAC,MAAD;EACE,IAAA,IAAI,EAAC,QADP;EAEE,IAAA,OAAO,EAAE;EAAA,aAAM8O,WAAW,CAAC8C,aAAZ,CAA0B7B,WAA1B,CAAN;EAAA,KAFX;EAGE,IAAA,KAAK,EAAE;EACLlP,MAAAA,UAAU,EAAEc,YAAK,CAACN;EADb;EAHT,cAlCF,CAzFF,eAqIErB;EACE,IAAA,KAAK,EAAE;EACLa,MAAAA,UAAU,EAAEc,YAAK,CAACb,aADb;EAELqF,MAAAA,OAAO,EAAE,MAFJ;EAGL2E,MAAAA,QAAQ,EAAE,QAHL;EAILyD,MAAAA,GAAG,EAAE,CAJA;EAKLT,MAAAA,MAAM,EAAE;EALH;EADT,qBArIF,eAgJE9N;EACE,IAAA,KAAK,EAAE;EACLmG,MAAAA,OAAO,EAAE;EADJ;EADT,kBAKEnG,6BAAC,QAAD;EACE,IAAA,KAAK,EAAC,MADR;EAEE,IAAA,KAAK,EAAE+P,WAAF,0CAAEA,WAAW,CAAEpN,KAAf,qBAAE,mBAAoBkP,IAF7B;EAGE,IAAA,eAAe,EAAE;EAHnB,IALF,CAhJF,eA2JE7R;EACE,IAAA,KAAK,EAAE;EACLa,MAAAA,UAAU,EAAEc,YAAK,CAACb,aADb;EAELqF,MAAAA,OAAO,EAAE,MAFJ;EAGL2E,MAAAA,QAAQ,EAAE,QAHL;EAILyD,MAAAA,GAAG,EAAE,CAJA;EAKLT,MAAAA,MAAM,EAAE;EALH;EADT,sBA3JF,eAsKE9N;EACE,IAAA,KAAK,EAAE;EACLmG,MAAAA,OAAO,EAAE;EADJ;EADT,kBAKEnG,6BAAC,QAAD;EACE,IAAA,KAAK,EAAC,OADR;EAEE,IAAA,KAAK,EAAE+P,WAFT;EAGE,IAAA,eAAe,EAAE;EACfuB,MAAAA,QAAQ,EAAE;EADK;EAHnB,IALF,CAtKF,CADU,GAqLR,IArbN,CADF,CADF;EA2bD,CAvhBsC;;;;;;;;;;;;;"}