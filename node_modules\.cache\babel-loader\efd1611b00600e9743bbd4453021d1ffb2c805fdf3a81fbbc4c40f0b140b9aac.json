{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u0633\\u0637\\u062D \\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\\\zencod\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, AppBar, Toolbar, Typography, Drawer, List, ListItem, ListItemIcon, ListItemText, CssBaseline, Container, Grid, Card, CardContent, IconButton, Menu, MenuItem } from '@mui/material';\nimport { Dashboard as DashboardIcon, AccountBalance, People, Business, Inventory, ShoppingCart, Receipt, Payment, Assessment, Settings, Menu as MenuIcon, AccountCircle } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst drawerWidth = 240;\n\n// Simple Dashboard Component\nconst Dashboard = () => {\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          gutterBottom: true,\n          children: \"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              children: \"\\u20AA 125,000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"\\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              children: \"342\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              children: \"1,234\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"\\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0639\\u0644\\u0642\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              children: \"23\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"\\u0627\\u0644\\u0646\\u0634\\u0627\\u0637 \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"\\u2022 \\u062A\\u0645 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0639\\u0645\\u064A\\u0644 \\u062C\\u062F\\u064A\\u062F: \\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"\\u2022 \\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629 #1234\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"\\u2022 \\u062A\\u0645 \\u0627\\u0633\\u062A\\u0644\\u0627\\u0645 \\u062F\\u0641\\u0639\\u0629 \\u0628\\u0642\\u064A\\u0645\\u0629 \\u20AA 5,000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n\n// Simple Page Component\n_c = Dashboard;\nconst SimplePage = ({\n  title,\n  description\n}) => {\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 4,\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          gutterBottom: true,\n          color: \"primary\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 2\n          },\n          children: \"\\u0647\\u0630\\u0627 \\u0627\\u0644\\u0642\\u0633\\u0645 \\u0642\\u064A\\u062F \\u0627\\u0644\\u062A\\u0637\\u0648\\u064A\\u0631 \\u0648\\u0633\\u064A\\u062A\\u0645 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u0644\\u0645\\u0632\\u064A\\u062F \\u0645\\u0646 \\u0627\\u0644\\u0645\\u064A\\u0632\\u0627\\u062A \\u0642\\u0631\\u064A\\u0628\\u0627\\u064B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_c2 = SimplePage;\nconst App = () => {\n  _s();\n  const [currentPage, setCurrentPage] = useState('dashboard');\n  const [anchorEl, setAnchorEl] = useState(null);\n  const menuItems = [{\n    id: 'dashboard',\n    label: 'لوحة التحكم',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 52\n    }, this)\n  }, {\n    id: 'accounts',\n    label: 'الحسابات',\n    icon: /*#__PURE__*/_jsxDEV(AccountBalance, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 48\n    }, this)\n  }, {\n    id: 'customers',\n    label: 'العملاء',\n    icon: /*#__PURE__*/_jsxDEV(People, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 48\n    }, this)\n  }, {\n    id: 'suppliers',\n    label: 'الموردين',\n    icon: /*#__PURE__*/_jsxDEV(Business, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 49\n    }, this)\n  }, {\n    id: 'products',\n    label: 'المنتجات',\n    icon: /*#__PURE__*/_jsxDEV(Inventory, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 48\n    }, this)\n  }, {\n    id: 'inventory',\n    label: 'المخزون',\n    icon: /*#__PURE__*/_jsxDEV(Inventory, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 48\n    }, this)\n  }, {\n    id: 'sales',\n    label: 'المبيعات',\n    icon: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 45\n    }, this)\n  }, {\n    id: 'purchases',\n    label: 'المشتريات',\n    icon: /*#__PURE__*/_jsxDEV(ShoppingCart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 50\n    }, this)\n  }, {\n    id: 'invoices',\n    label: 'الفواتير',\n    icon: /*#__PURE__*/_jsxDEV(Receipt, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 48\n    }, this)\n  }, {\n    id: 'payments',\n    label: 'المدفوعات',\n    icon: /*#__PURE__*/_jsxDEV(Payment, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 49\n    }, this)\n  }, {\n    id: 'reports',\n    label: 'التقارير',\n    icon: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 47\n    }, this)\n  }, {\n    id: 'settings',\n    label: 'الإعدادات',\n    icon: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 49\n    }, this)\n  }];\n  const handleMenuClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'dashboard':\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 16\n        }, this);\n      case 'accounts':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628\\u0627\\u062A\",\n          description: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u062D\\u0633\\u0627\\u0628\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629 \\u0648\\u0627\\u0644\\u0623\\u0631\\u0635\\u062F\\u0629 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 16\n        }, this);\n      case 'customers':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\",\n          description: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621 \\u0648\\u0627\\u0644\\u062A\\u0648\\u0627\\u0635\\u0644 \\u0645\\u0639\\u0647\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 16\n        }, this);\n      case 'suppliers':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\",\n          description: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0648\\u0627\\u0644\\u062A\\u0639\\u0627\\u0645\\u0644\\u0627\\u062A \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 16\n        }, this);\n      case 'products':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\",\n          description: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0643\\u062A\\u0627\\u0644\\u0648\\u062C \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0648\\u0627\\u0644\\u0623\\u0633\\u0639\\u0627\\u0631\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 16\n        }, this);\n      case 'inventory':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\",\n          description: \"\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0648\\u0627\\u0644\\u0643\\u0645\\u064A\\u0627\\u062A \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 16\n        }, this);\n      case 'sales':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\",\n          description: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0639\\u0645\\u0644\\u064A\\u0627\\u062A \\u0627\\u0644\\u0628\\u064A\\u0639 \\u0648\\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 16\n        }, this);\n      case 'purchases':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\",\n          description: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0639\\u0645\\u0644\\u064A\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0627\\u0621 \\u0645\\u0646 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 16\n        }, this);\n      case 'invoices':\n        return /*#__PURE__*/_jsxDEV(InvoicesPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 16\n        }, this);\n      case 'payments':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A\",\n          description: \"\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629 \\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A \\u0648\\u0627\\u0644\\u0645\\u0633\\u062A\\u062D\\u0642\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 16\n        }, this);\n      case 'reports':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631\",\n          description: \"\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0645\\u0627\\u0644\\u064A\\u0629 \\u0648\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0645\\u0641\\u0635\\u0644\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 16\n        }, this);\n      case 'settings':\n        return /*#__PURE__*/_jsxDEV(SimplePage, {\n          title: \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\",\n          description: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0648\\u0627\\u0644\\u062A\\u062E\\u0635\\u064A\\u0635\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      sx: {\n        zIndex: theme => theme.zIndex.drawer + 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"inherit\",\n          \"aria-label\": \"open drawer\",\n          edge: \"start\",\n          sx: {\n            mr: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          noWrap: true,\n          component: \"div\",\n          sx: {\n            flexGrow: 1\n          },\n          children: \"\\u0646\\u0638\\u0627\\u0645 \\u0632\\u064A\\u0646 \\u0643\\u0648\\u062F \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"large\",\n          \"aria-label\": \"account of current user\",\n          \"aria-controls\": \"menu-appbar\",\n          \"aria-haspopup\": \"true\",\n          onClick: handleMenuClick,\n          color: \"inherit\",\n          children: /*#__PURE__*/_jsxDEV(AccountCircle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          id: \"menu-appbar\",\n          anchorEl: anchorEl,\n          anchorOrigin: {\n            vertical: 'top',\n            horizontal: 'right'\n          },\n          keepMounted: true,\n          transformOrigin: {\n            vertical: 'top',\n            horizontal: 'right'\n          },\n          open: Boolean(anchorEl),\n          onClose: handleMenuClose,\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: handleMenuClose,\n            children: \"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: handleMenuClose,\n            children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"permanent\",\n      sx: {\n        width: drawerWidth,\n        flexShrink: 0,\n        [`& .MuiDrawer-paper`]: {\n          width: drawerWidth,\n          boxSizing: 'border-box'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Toolbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: menuItems.map(item => /*#__PURE__*/_jsxDEV(ListItem, {\n            button: true,\n            selected: currentPage === item.id,\n            onClick: () => setCurrentPage(item.id),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Toolbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), renderPage()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 227,\n    columnNumber: 5\n  }, this);\n};\n_s(App, \"AKWfrgXNDDt1v+0n+T3Rxyf6VKw=\");\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Dashboard\");\n$RefreshReg$(_c2, \"SimplePage\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useState", "Box", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "Drawer", "List", "ListItem", "ListItemIcon", "ListItemText", "CssBaseline", "Container", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "MenuItem", "Dashboard", "DashboardIcon", "AccountBalance", "People", "Business", "Inventory", "ShoppingCart", "Receipt", "Payment", "Assessment", "Settings", "MenuIcon", "AccountCircle", "jsxDEV", "_jsxDEV", "drawerWidth", "max<PERSON><PERSON><PERSON>", "sx", "mt", "mb", "children", "container", "spacing", "item", "xs", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sm", "md", "color", "component", "_c", "SimplePage", "title", "description", "p", "textAlign", "_c2", "App", "_s", "currentPage", "setCurrentPage", "anchorEl", "setAnchorEl", "menuItems", "id", "label", "icon", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "renderPage", "InvoicesPage", "display", "position", "zIndex", "theme", "drawer", "edge", "mr", "noWrap", "flexGrow", "size", "onClick", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "keepMounted", "transform<PERSON><PERSON>in", "open", "Boolean", "onClose", "width", "flexShrink", "boxSizing", "overflow", "map", "button", "selected", "primary", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  AppBar,\n  <PERSON><PERSON>bar,\n  Typography,\n  Drawer,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  CssBaseline,\n  Container,\n  Grid,\n  Card,\n  CardContent,\n  IconButton,\n  Menu,\n  MenuItem,\n  Button,\n  TextField,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  Autocomplete,\n  Divider,\n  Chip,\n  Alert\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  AccountBalance,\n  People,\n  Business,\n  Inventory,\n  ShoppingCart,\n  Receipt,\n  Payment,\n  Assessment,\n  Settings,\n\n  Menu as MenuIcon,\n  AccountCircle\n} from '@mui/icons-material';\n\nconst drawerWidth = 240;\n\n// Simple Dashboard Component\nconst Dashboard: React.FC = () => {\n  return (\n    <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n      <Grid container spacing={3}>\n        <Grid item xs={12}>\n          <Typography variant=\"h4\" gutterBottom>\n            لوحة التحكم الرئيسية\n          </Typography>\n        </Grid>\n\n        {/* Stats Cards */}\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                إجمالي المبيعات\n              </Typography>\n              <Typography variant=\"h5\" component=\"div\">\n                ₪ 125,000\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                العملاء\n              </Typography>\n              <Typography variant=\"h5\" component=\"div\">\n                342\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                المنتجات\n              </Typography>\n              <Typography variant=\"h5\" component=\"div\">\n                1,234\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"textSecondary\" gutterBottom>\n                الطلبات المعلقة\n              </Typography>\n              <Typography variant=\"h5\" component=\"div\">\n                23\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Recent Activity */}\n        <Grid item xs={12}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                النشاط الأخير\n              </Typography>\n              <Typography variant=\"body2\">\n                • تم إضافة عميل جديد: أحمد محمد\n              </Typography>\n              <Typography variant=\"body2\">\n                • تم إنشاء فاتورة جديدة #1234\n              </Typography>\n              <Typography variant=\"body2\">\n                • تم استلام دفعة بقيمة ₪ 5,000\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Container>\n  );\n};\n\n// Simple Page Component\nconst SimplePage: React.FC<{ title: string; description: string }> = ({ title, description }) => {\n  return (\n    <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n      <Card>\n        <CardContent sx={{ p: 4, textAlign: 'center' }}>\n          <Typography variant=\"h4\" gutterBottom color=\"primary\">\n            {title}\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            {description}\n          </Typography>\n          <Typography variant=\"body2\" sx={{ mt: 2 }}>\n            هذا القسم قيد التطوير وسيتم إضافة المزيد من الميزات قريباً\n          </Typography>\n        </CardContent>\n      </Card>\n    </Container>\n  );\n};\n\nconst App: React.FC = () => {\n  const [currentPage, setCurrentPage] = useState('dashboard');\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n\n  const menuItems = [\n    { id: 'dashboard', label: 'لوحة التحكم', icon: <DashboardIcon /> },\n    { id: 'accounts', label: 'الحسابات', icon: <AccountBalance /> },\n    { id: 'customers', label: 'العملاء', icon: <People /> },\n    { id: 'suppliers', label: 'الموردين', icon: <Business /> },\n    { id: 'products', label: 'المنتجات', icon: <Inventory /> },\n    { id: 'inventory', label: 'المخزون', icon: <Inventory /> },\n    { id: 'sales', label: 'المبيعات', icon: <ShoppingCart /> },\n    { id: 'purchases', label: 'المشتريات', icon: <ShoppingCart /> },\n    { id: 'invoices', label: 'الفواتير', icon: <Receipt /> },\n    { id: 'payments', label: 'المدفوعات', icon: <Payment /> },\n    { id: 'reports', label: 'التقارير', icon: <Assessment /> },\n    { id: 'settings', label: 'الإعدادات', icon: <Settings /> },\n  ];\n\n  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'dashboard':\n        return <Dashboard />;\n      case 'accounts':\n        return <SimplePage title=\"إدارة الحسابات\" description=\"إدارة حسابات الشركة والأرصدة المالية\" />;\n      case 'customers':\n        return <SimplePage title=\"إدارة العملاء\" description=\"إدارة بيانات العملاء والتواصل معهم\" />;\n      case 'suppliers':\n        return <SimplePage title=\"إدارة الموردين\" description=\"إدارة بيانات الموردين والتعاملات التجارية\" />;\n      case 'products':\n        return <SimplePage title=\"إدارة المنتجات\" description=\"إدارة كتالوج المنتجات والأسعار\" />;\n      case 'inventory':\n        return <SimplePage title=\"إدارة المخزون\" description=\"متابعة المخزون والكميات المتاحة\" />;\n      case 'sales':\n        return <SimplePage title=\"إدارة المبيعات\" description=\"إدارة عمليات البيع والطلبات\" />;\n      case 'purchases':\n        return <SimplePage title=\"إدارة المشتريات\" description=\"إدارة عمليات الشراء من الموردين\" />;\n      case 'invoices':\n        return <InvoicesPage />;\n      case 'payments':\n        return <SimplePage title=\"إدارة المدفوعات\" description=\"متابعة المدفوعات والمستحقات\" />;\n      case 'reports':\n        return <SimplePage title=\"التقارير\" description=\"تقارير مالية وإحصائيات مفصلة\" />;\n      case 'settings':\n        return <SimplePage title=\"الإعدادات\" description=\"إعدادات النظام والتخصيص\" />;\n      default:\n        return <Dashboard />;\n    }\n  };\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      <CssBaseline />\n\n      {/* App Bar */}\n      <AppBar position=\"fixed\" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge=\"start\"\n            sx={{ mr: 2 }}\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1 }}>\n            نظام زين كود المحاسبي\n          </Typography>\n          <IconButton\n            size=\"large\"\n            aria-label=\"account of current user\"\n            aria-controls=\"menu-appbar\"\n            aria-haspopup=\"true\"\n            onClick={handleMenuClick}\n            color=\"inherit\"\n          >\n            <AccountCircle />\n          </IconButton>\n          <Menu\n            id=\"menu-appbar\"\n            anchorEl={anchorEl}\n            anchorOrigin={{\n              vertical: 'top',\n              horizontal: 'right',\n            }}\n            keepMounted\n            transformOrigin={{\n              vertical: 'top',\n              horizontal: 'right',\n            }}\n            open={Boolean(anchorEl)}\n            onClose={handleMenuClose}\n          >\n            <MenuItem onClick={handleMenuClose}>الملف الشخصي</MenuItem>\n            <MenuItem onClick={handleMenuClose}>تسجيل الخروج</MenuItem>\n          </Menu>\n        </Toolbar>\n      </AppBar>\n\n      {/* Sidebar */}\n      <Drawer\n        variant=\"permanent\"\n        sx={{\n          width: drawerWidth,\n          flexShrink: 0,\n          [`& .MuiDrawer-paper`]: { width: drawerWidth, boxSizing: 'border-box' },\n        }}\n      >\n        <Toolbar />\n        <Box sx={{ overflow: 'auto' }}>\n          <List>\n            {menuItems.map((item) => (\n              <ListItem\n                button\n                key={item.id}\n                selected={currentPage === item.id}\n                onClick={() => setCurrentPage(item.id)}\n              >\n                <ListItemIcon>{item.icon}</ListItemIcon>\n                <ListItemText primary={item.label} />\n              </ListItem>\n            ))}\n          </List>\n        </Box>\n      </Drawer>\n\n      {/* Main Content */}\n      <Box component=\"main\" sx={{ flexGrow: 1, p: 3 }}>\n        <Toolbar />\n        {renderPage()}\n      </Box>\n    </Box>\n  );\n};\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,QAAQ,QAqBH,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,cAAc,EACdC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVC,QAAQ,EAERZ,IAAI,IAAIa,QAAQ,EAChBC,aAAa,QACR,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,WAAW,GAAG,GAAG;;AAEvB;AACA,MAAMf,SAAmB,GAAGA,CAAA,KAAM;EAChC,oBACEc,OAAA,CAACrB,SAAS;IAACuB,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eAC5CN,OAAA,CAACpB,IAAI;MAAC2B,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAF,QAAA,gBACzBN,OAAA,CAACpB,IAAI;QAAC6B,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAJ,QAAA,eAChBN,OAAA,CAAC5B,UAAU;UAACuC,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAN,QAAA,EAAC;QAEtC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGPhB,OAAA,CAACpB,IAAI;QAAC6B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACO,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BN,OAAA,CAACnB,IAAI;UAAAyB,QAAA,eACHN,OAAA,CAAClB,WAAW;YAAAwB,QAAA,gBACVN,OAAA,CAAC5B,UAAU;cAAC+C,KAAK,EAAC,eAAe;cAACP,YAAY;cAAAN,QAAA,EAAC;YAE/C;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAAC5B,UAAU;cAACuC,OAAO,EAAC,IAAI;cAACS,SAAS,EAAC,KAAK;cAAAd,QAAA,EAAC;YAEzC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPhB,OAAA,CAACpB,IAAI;QAAC6B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACO,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BN,OAAA,CAACnB,IAAI;UAAAyB,QAAA,eACHN,OAAA,CAAClB,WAAW;YAAAwB,QAAA,gBACVN,OAAA,CAAC5B,UAAU;cAAC+C,KAAK,EAAC,eAAe;cAACP,YAAY;cAAAN,QAAA,EAAC;YAE/C;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAAC5B,UAAU;cAACuC,OAAO,EAAC,IAAI;cAACS,SAAS,EAAC,KAAK;cAAAd,QAAA,EAAC;YAEzC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPhB,OAAA,CAACpB,IAAI;QAAC6B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACO,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BN,OAAA,CAACnB,IAAI;UAAAyB,QAAA,eACHN,OAAA,CAAClB,WAAW;YAAAwB,QAAA,gBACVN,OAAA,CAAC5B,UAAU;cAAC+C,KAAK,EAAC,eAAe;cAACP,YAAY;cAAAN,QAAA,EAAC;YAE/C;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAAC5B,UAAU;cAACuC,OAAO,EAAC,IAAI;cAACS,SAAS,EAAC,KAAK;cAAAd,QAAA,EAAC;YAEzC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPhB,OAAA,CAACpB,IAAI;QAAC6B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACO,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BN,OAAA,CAACnB,IAAI;UAAAyB,QAAA,eACHN,OAAA,CAAClB,WAAW;YAAAwB,QAAA,gBACVN,OAAA,CAAC5B,UAAU;cAAC+C,KAAK,EAAC,eAAe;cAACP,YAAY;cAAAN,QAAA,EAAC;YAE/C;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAAC5B,UAAU;cAACuC,OAAO,EAAC,IAAI;cAACS,SAAS,EAAC,KAAK;cAAAd,QAAA,EAAC;YAEzC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPhB,OAAA,CAACpB,IAAI;QAAC6B,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAJ,QAAA,eAChBN,OAAA,CAACnB,IAAI;UAAAyB,QAAA,eACHN,OAAA,CAAClB,WAAW;YAAAwB,QAAA,gBACVN,OAAA,CAAC5B,UAAU;cAACuC,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAN,QAAA,EAAC;YAEtC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAAC5B,UAAU;cAACuC,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAE5B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAAC5B,UAAU;cAACuC,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAE5B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhB,OAAA,CAAC5B,UAAU;cAACuC,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAE5B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;;AAED;AAAAK,EAAA,GAvFMnC,SAAmB;AAwFzB,MAAMoC,UAA4D,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAY,CAAC,KAAK;EAC/F,oBACExB,OAAA,CAACrB,SAAS;IAACuB,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eAC5CN,OAAA,CAACnB,IAAI;MAAAyB,QAAA,eACHN,OAAA,CAAClB,WAAW;QAACqB,EAAE,EAAE;UAAEsB,CAAC,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAApB,QAAA,gBAC7CN,OAAA,CAAC5B,UAAU;UAACuC,OAAO,EAAC,IAAI;UAACC,YAAY;UAACO,KAAK,EAAC,SAAS;UAAAb,QAAA,EAClDiB;QAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACbhB,OAAA,CAAC5B,UAAU;UAACuC,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,gBAAgB;UAAAb,QAAA,EAC/CkB;QAAW;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACbhB,OAAA,CAAC5B,UAAU;UAACuC,OAAO,EAAC,OAAO;UAACR,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,EAAC;QAE3C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACW,GAAA,GAlBIL,UAA4D;AAoBlE,MAAMM,GAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,WAAW,CAAC;EAC3D,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAqB,IAAI,CAAC;EAElE,MAAMkE,SAAS,GAAG,CAChB;IAAEC,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,eAAErC,OAAA,CAACb,aAAa;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAClE;IAAEmB,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,eAAErC,OAAA,CAACZ,cAAc;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC/D;IAAEmB,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,eAAErC,OAAA,CAACX,MAAM;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACvD;IAAEmB,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,eAAErC,OAAA,CAACV,QAAQ;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC1D;IAAEmB,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,eAAErC,OAAA,CAACT,SAAS;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC1D;IAAEmB,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,eAAErC,OAAA,CAACT,SAAS;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC1D;IAAEmB,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,eAAErC,OAAA,CAACR,YAAY;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC1D;IAAEmB,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,eAAErC,OAAA,CAACR,YAAY;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC/D;IAAEmB,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,eAAErC,OAAA,CAACP,OAAO;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACxD;IAAEmB,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,eAAErC,OAAA,CAACN,OAAO;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACzD;IAAEmB,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,eAAErC,OAAA,CAACL,UAAU;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC1D;IAAEmB,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,eAAErC,OAAA,CAACJ,QAAQ;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CAC3D;EAED,MAAMsB,eAAe,GAAIC,KAAoC,IAAK;IAChEN,WAAW,CAACM,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BR,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMS,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQZ,WAAW;MACjB,KAAK,WAAW;QACd,oBAAO9B,OAAA,CAACd,SAAS;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,UAAU;QACb,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,iFAAgB;UAACC,WAAW,EAAC;QAAsC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjG,KAAK,WAAW;QACd,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,2EAAe;UAACC,WAAW,EAAC;QAAoC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9F,KAAK,WAAW;QACd,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,iFAAgB;UAACC,WAAW,EAAC;QAA2C;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtG,KAAK,UAAU;QACb,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,iFAAgB;UAACC,WAAW,EAAC;QAAgC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3F,KAAK,WAAW;QACd,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,2EAAe;UAACC,WAAW,EAAC;QAAiC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3F,KAAK,OAAO;QACV,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,iFAAgB;UAACC,WAAW,EAAC;QAA6B;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxF,KAAK,WAAW;QACd,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,uFAAiB;UAACC,WAAW,EAAC;QAAiC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7F,KAAK,UAAU;QACb,oBAAOhB,OAAA,CAAC2C,YAAY;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,UAAU;QACb,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,uFAAiB;UAACC,WAAW,EAAC;QAA6B;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzF,KAAK,SAAS;QACZ,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,kDAAU;UAACC,WAAW,EAAC;QAA8B;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnF,KAAK,UAAU;QACb,oBAAOhB,OAAA,CAACsB,UAAU;UAACC,KAAK,EAAC,wDAAW;UAACC,WAAW,EAAC;QAAyB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/E;QACE,oBAAOhB,OAAA,CAACd,SAAS;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxB;EACF,CAAC;EAED,oBACEhB,OAAA,CAAC/B,GAAG;IAACkC,EAAE,EAAE;MAAEyC,OAAO,EAAE;IAAO,CAAE;IAAAtC,QAAA,gBAC3BN,OAAA,CAACtB,WAAW;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGfhB,OAAA,CAAC9B,MAAM;MAAC2E,QAAQ,EAAC,OAAO;MAAC1C,EAAE,EAAE;QAAE2C,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAACE,MAAM,GAAG;MAAE,CAAE;MAAA1C,QAAA,eAC1EN,OAAA,CAAC7B,OAAO;QAAAmC,QAAA,gBACNN,OAAA,CAACjB,UAAU;UACToC,KAAK,EAAC,SAAS;UACf,cAAW,aAAa;UACxB8B,IAAI,EAAC,OAAO;UACZ9C,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE,CAAE;UAAA5C,QAAA,eAEdN,OAAA,CAACH,QAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACbhB,OAAA,CAAC5B,UAAU;UAACuC,OAAO,EAAC,IAAI;UAACwC,MAAM;UAAC/B,SAAS,EAAC,KAAK;UAACjB,EAAE,EAAE;YAAEiD,QAAQ,EAAE;UAAE,CAAE;UAAA9C,QAAA,EAAC;QAErE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhB,OAAA,CAACjB,UAAU;UACTsE,IAAI,EAAC,OAAO;UACZ,cAAW,yBAAyB;UACpC,iBAAc,aAAa;UAC3B,iBAAc,MAAM;UACpBC,OAAO,EAAEhB,eAAgB;UACzBnB,KAAK,EAAC,SAAS;UAAAb,QAAA,eAEfN,OAAA,CAACF,aAAa;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbhB,OAAA,CAAChB,IAAI;UACHmD,EAAE,EAAC,aAAa;UAChBH,QAAQ,EAAEA,QAAS;UACnBuB,YAAY,EAAE;YACZC,QAAQ,EAAE,KAAK;YACfC,UAAU,EAAE;UACd,CAAE;UACFC,WAAW;UACXC,eAAe,EAAE;YACfH,QAAQ,EAAE,KAAK;YACfC,UAAU,EAAE;UACd,CAAE;UACFG,IAAI,EAAEC,OAAO,CAAC7B,QAAQ,CAAE;UACxB8B,OAAO,EAAErB,eAAgB;UAAAnC,QAAA,gBAEzBN,OAAA,CAACf,QAAQ;YAACqE,OAAO,EAAEb,eAAgB;YAAAnC,QAAA,EAAC;UAAY;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC3DhB,OAAA,CAACf,QAAQ;YAACqE,OAAO,EAAEb,eAAgB;YAAAnC,QAAA,EAAC;UAAY;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGThB,OAAA,CAAC3B,MAAM;MACLsC,OAAO,EAAC,WAAW;MACnBR,EAAE,EAAE;QACF4D,KAAK,EAAE9D,WAAW;QAClB+D,UAAU,EAAE,CAAC;QACb,CAAC,oBAAoB,GAAG;UAAED,KAAK,EAAE9D,WAAW;UAAEgE,SAAS,EAAE;QAAa;MACxE,CAAE;MAAA3D,QAAA,gBAEFN,OAAA,CAAC7B,OAAO;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXhB,OAAA,CAAC/B,GAAG;QAACkC,EAAE,EAAE;UAAE+D,QAAQ,EAAE;QAAO,CAAE;QAAA5D,QAAA,eAC5BN,OAAA,CAAC1B,IAAI;UAAAgC,QAAA,EACF4B,SAAS,CAACiC,GAAG,CAAE1D,IAAI,iBAClBT,OAAA,CAACzB,QAAQ;YACP6F,MAAM;YAENC,QAAQ,EAAEvC,WAAW,KAAKrB,IAAI,CAAC0B,EAAG;YAClCmB,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAACtB,IAAI,CAAC0B,EAAE,CAAE;YAAA7B,QAAA,gBAEvCN,OAAA,CAACxB,YAAY;cAAA8B,QAAA,EAAEG,IAAI,CAAC4B;YAAI;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eACxChB,OAAA,CAACvB,YAAY;cAAC6F,OAAO,EAAE7D,IAAI,CAAC2B;YAAM;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GALhCP,IAAI,CAAC0B,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMJ,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGThB,OAAA,CAAC/B,GAAG;MAACmD,SAAS,EAAC,MAAM;MAACjB,EAAE,EAAE;QAAEiD,QAAQ,EAAE,CAAC;QAAE3B,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBAC9CN,OAAA,CAAC7B,OAAO;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACV0B,UAAU,CAAC,CAAC;IAAA;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,CA7IID,GAAa;AAAA2C,GAAA,GAAb3C,GAAa;AA+InB,eAAeA,GAAG;AAAC,IAAAP,EAAA,EAAAM,GAAA,EAAA4C,GAAA;AAAAC,YAAA,CAAAnD,EAAA;AAAAmD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}