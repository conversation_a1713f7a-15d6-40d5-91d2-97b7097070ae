import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  Snackbar,
} from '@mui/material';
import { Add, Download, Upload } from '@mui/icons-material';

import PaymentStats from '../../components/Payments/PaymentStats';
import PaymentFilters from '../../components/Payments/PaymentFilters';
import PaymentTable from '../../components/Payments/PaymentTable';
import PaymentForm from '../../components/Payments/PaymentForm';
import {
  Payment,
  PaymentStats as PaymentStatsType,
  PaymentFilters as PaymentFiltersType,
  CreatePaymentRequest,
} from '../../types/payments';

const PaymentsPage: React.FC = () => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [stats, setStats] = useState<PaymentStatsType>({
    totalIncoming: 0,
    totalOutgoing: 0,
    pendingPayments: 0,
    completedPayments: 0,
    todayPayments: 0,
    monthlyIncoming: 0,
    monthlyOutgoing: 0,
    overduePayments: 0,
  });
  const [filters, setFilters] = useState<PaymentFiltersType>({});
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | undefined>();
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({ open: false, message: '', severity: 'success' });
  const [loading, setLoading] = useState(false);

  // Mock data for demonstration
  const mockPayments: Payment[] = [
    {
      id: '1',
      invoiceId: 'INV-001',
      invoiceNumber: 'INV-2024-001',
      customerId: 'CUST-001',
      customerName: 'أحمد محمد',
      amount: 15000,
      paidAmount: 15000,
      remainingAmount: 0,
      paymentMethod: 'cash',
      paymentDate: '2024-01-15',
      status: 'completed',
      type: 'incoming',
      reference: 'CASH-001',
      notes: 'دفع نقدي كامل',
      createdBy: 'USER-001',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z',
    },
    {
      id: '2',
      invoiceId: 'INV-002',
      invoiceNumber: 'INV-2024-002',
      customerId: 'CUST-002',
      customerName: 'فاطمة علي',
      amount: 8500,
      paidAmount: 5000,
      remainingAmount: 3500,
      paymentMethod: 'card',
      paymentDate: '2024-01-16',
      status: 'pending',
      type: 'incoming',
      reference: 'CARD-002',
      notes: 'دفع جزئي بالبطاقة',
      createdBy: 'USER-001',
      createdAt: '2024-01-16T11:00:00Z',
      updatedAt: '2024-01-16T11:00:00Z',
    },
    {
      id: '3',
      supplierId: 'SUPP-001',
      supplierName: 'شركة التوريد المحدودة',
      amount: 12000,
      paidAmount: 12000,
      remainingAmount: 0,
      paymentMethod: 'bank_transfer',
      paymentDate: '2024-01-17',
      status: 'completed',
      type: 'outgoing',
      reference: 'BANK-003',
      notes: 'دفع للمورد عبر التحويل البنكي',
      createdBy: 'USER-001',
      createdAt: '2024-01-17T09:00:00Z',
      updatedAt: '2024-01-17T09:00:00Z',
    },
    {
      id: '4',
      invoiceId: 'INV-003',
      invoiceNumber: 'INV-2024-003',
      customerId: 'CUST-003',
      customerName: 'محمد سالم',
      amount: 22000,
      paidAmount: 0,
      remainingAmount: 22000,
      paymentMethod: 'installment',
      paymentDate: '2024-01-18',
      dueDate: '2024-02-18',
      status: 'pending',
      type: 'incoming',
      notes: 'دفع بالأقساط - القسط الأول مستحق',
      createdBy: 'USER-001',
      createdAt: '2024-01-18T14:00:00Z',
      updatedAt: '2024-01-18T14:00:00Z',
    },
  ];

  const mockStats: PaymentStatsType = {
    totalIncoming: 45500,
    totalOutgoing: 12000,
    pendingPayments: 2,
    completedPayments: 2,
    todayPayments: 1,
    monthlyIncoming: 45500,
    monthlyOutgoing: 12000,
    overduePayments: 1,
  };

useEffect(() => {
    // Simulate API call
    setLoading(true);
    setTimeout(() => {
      setPayments(mockPayments);
      setStats(mockStats);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    // Apply filters
    let filtered = payments;

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        (payment) =>
          payment.customerName?.toLowerCase().includes(searchLower) ||
          payment.supplierName?.toLowerCase().includes(searchLower) ||
          payment.invoiceNumber?.toLowerCase().includes(searchLower) ||
          payment.reference?.toLowerCase().includes(searchLower)
      );
    }

    if (filters.status) {
      filtered = filtered.filter((payment) => payment.status === filters.status);
    }

    if (filters.paymentMethod) {
      filtered = filtered.filter((payment) => payment.paymentMethod === filters.paymentMethod);
    }

    if (filters.type) {
      filtered = filtered.filter((payment) => payment.type === filters.type);
    }

    if (filters.dateFrom) {
      filtered = filtered.filter((payment) => payment.paymentDate >= filters.dateFrom!);
    }

    if (filters.dateTo) {
      filtered = filtered.filter((payment) => payment.paymentDate <= filters.dateTo!);
    }

    setFilteredPayments(filtered);
  }, [payments, filters]);

  const handleCreatePayment = (paymentData: CreatePaymentRequest) => {
    // Simulate API call
    const newPayment: Payment = {
      id: `PAY-${Date.now()}`,
      ...paymentData,
      paidAmount: paymentData.amount,
      remainingAmount: 0,
      status: 'completed',
      createdBy: 'USER-001',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setPayments((prev) => [newPayment, ...prev]);
    setSnackbar({
      open: true,
      message: 'تم إنشاء الدفعة بنجاح',
      severity: 'success',
    });
  };

  const handleEditPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsFormOpen(true);
  };

  const handleDeletePayment = (paymentId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
      setPayments((prev) => prev.filter((p) => p.id !== paymentId));
      setSnackbar({
        open: true,
        message: 'تم حذف الدفعة بنجاح',
        severity: 'success',
      });
    }
  };

  const handleViewPayment = (payment: Payment) => {
    // Implement view payment details
    console.log('View payment:', payment);
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const handleCloseSnackbar = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  const handleExportData = () => {
    // Implement export functionality
    setSnackbar({
      open: true,
      message: 'جاري تصدير البيانات...',
      severity: 'info',
    });
  };

  const handleImportData = () => {
    // Implement import functionality
    setSnackbar({
      open: true,
      message: 'جاري استيراد البيانات...',
      severity: 'info',
    });
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Page Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" fontWeight={700} gutterBottom>
            إدارة المدفوعات
          </Typography>
          <Typography variant="body1" color="text.secondary">
            إدارة وتتبع جميع المدفوعات الواردة والصادرة
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<Upload />}
            onClick={handleImportData}
            size="small"
          >
            استيراد
          </Button>
          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={handleExportData}
            size="small"
          >
            تصدير
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => {
              setSelectedPayment(undefined);
              setIsFormOpen(true);
            }}
          >
            إضافة دفعة
          </Button>
        </Box>
      </Box>

      {/* Payment Statistics */}
      <PaymentStats stats={stats} />

      {/* Filters */}
      <PaymentFilters
        filters={filters}
        onFiltersChange={setFilters}
        onClearFilters={handleClearFilters}
      />

      {/* Payments Table */}
      <Paper sx={{ borderRadius: 2 }}>
        <PaymentTable
          payments={filteredPayments}
          onEdit={handleEditPayment}
          onDelete={handleDeletePayment}
          onView={handleViewPayment}
        />
      </Paper>

      {/* Payment Form Dialog */}
      <PaymentForm
        open={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedPayment(undefined);
        }}
        onSubmit={handleCreatePayment}
        payment={selectedPayment}
        title={selectedPayment ? 'تعديل الدفعة' : 'إضافة دفعة جديدة'}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PaymentsPage;