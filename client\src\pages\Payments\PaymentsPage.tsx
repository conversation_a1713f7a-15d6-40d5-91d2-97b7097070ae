import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';

// Mock data for demonstration
interface Payment {
  id: number;
  invoiceNumber: string;
  amount: number;
  method: string;
  date: string;
  reference: string;
  status: string;
}

const mockPayments: Payment[] = [
  {
    id: 1,
    invoiceNumber: 'INV-001',
    amount: 8625,
    method: 'تحويل بنكي',
    date: '2024-01-15',
    reference: 'TXN-001',
    status: 'مكتمل'
  },
  {
    id: 2,
    invoiceNumber: 'INV-002',
    amount: 5000,
    method: 'نقداً',
    date: '2024-01-16',
    reference: 'TXN-002',
    status: 'في الانتظار'
  },
  {
    id: 3,
    invoiceNumber: 'INV-003',
    amount: 12000,
    method: 'بطاقة ائتمان',
    date: '2024-01-17',
    reference: 'TXN-003',
    status: 'مكتمل'
  }
];

// Helper functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ar-SA');
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'مكتمل':
      return '#4caf50';
    case 'في الانتظار':
      return '#ff9800';
    case 'فشل':
      return '#f44336';
    default:
      return '#757575';
  }
};

const PaymentsPage: React.FC = () => {
  const [payments] = useState<Payment[]>(mockPayments);
  const [loading, setLoading] = useState(false);

  // Calculate statistics
  const stats = {
    totalPayments: payments.length,
    totalAmount: payments.reduce((sum, payment) => sum + payment.amount, 0),
    pendingPayments: payments.filter(p => p.status === 'في الانتظار').length,
    completedPayments: payments.filter(p => p.status === 'مكتمل').length,
  };

  // Simulate loading data
  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleAddPayment = () => {
    // This would open a form dialog in a real implementation
    alert('سيتم فتح نموذج إضافة دفعة جديدة');
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 500);
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight={700}>
          إدارة المدفوعات
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            onClick={handleRefresh}
            disabled={loading}
          >
            تحديث
          </Button>
          <Button
            variant="contained"
            onClick={handleAddPayment}
          >
            إضافة دفعة
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 2, mb: 3 }}>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            إجمالي المدفوعات
          </Typography>
          <Typography variant="h4" component="div" color="primary">
            {stats.totalPayments}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            إجمالي المبلغ
          </Typography>
          <Typography variant="h4" component="div" color="primary">
            {formatCurrency(stats.totalAmount)}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            في الانتظار
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#ff9800' }}>
            {stats.pendingPayments}
          </Typography>
        </Paper>
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="textSecondary" gutterBottom>
            مكتملة
          </Typography>
          <Typography variant="h4" component="div" sx={{ color: '#4caf50' }}>
            {stats.completedPayments}
          </Typography>
        </Paper>
      </Box>

      {/* Payments Table */}
      <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
          <Typography variant="h6">قائمة المدفوعات</Typography>
        </Box>

        {loading ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography>جاري التحميل...</Typography>
          </Box>
        ) : (
          <Box sx={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f5f5f5' }}>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>رقم الدفعة</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>رقم الفاتورة</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>المبلغ</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>طريقة الدفع</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>التاريخ</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>رقم المرجع</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #e0e0e0' }}>الحالة</th>
                  <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #e0e0e0' }}>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {payments.map((payment) => (
                  <tr key={payment.id} style={{ borderBottom: '1px solid #e0e0e0' }}>
                    <td style={{ padding: '12px' }}>{payment.id}</td>
                    <td style={{ padding: '12px' }}>{payment.invoiceNumber}</td>
                    <td style={{ padding: '12px' }}>{formatCurrency(payment.amount)}</td>
                    <td style={{ padding: '12px' }}>{payment.method}</td>
                    <td style={{ padding: '12px' }}>{formatDate(payment.date)}</td>
                    <td style={{ padding: '12px' }}>{payment.reference}</td>
                    <td style={{ padding: '12px' }}>
                      <span
                        style={{
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          color: 'white',
                          backgroundColor: getStatusColor(payment.status)
                        }}
                      >
                        {payment.status}
                      </span>
                    </td>
                    <td style={{ padding: '12px', textAlign: 'center' }}>
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{ mr: 1, minWidth: 'auto', px: 1 }}
                        onClick={() => alert(`تعديل الدفعة ${payment.id}`)}
                      >
                        تعديل
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        color="error"
                        sx={{ minWidth: 'auto', px: 1 }}
                        onClick={() => alert(`حذف الدفعة ${payment.id}`)}
                      >
                        حذف
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Box>
        )}

        {!loading && payments.length === 0 && (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Typography color="textSecondary">
              لا توجد مدفوعات متاحة
            </Typography>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default PaymentsPage;