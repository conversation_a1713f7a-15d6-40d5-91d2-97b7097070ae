{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getBottomNavigationActionUtilityClass(slot) {\n  return generateUtilityClass('MuiBottomNavigationAction', slot);\n}\nconst bottomNavigationActionClasses = generateUtilityClasses('MuiBottomNavigationAction', ['root', 'iconOnly', 'selected', 'label']);\nexport default bottomNavigationActionClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getBottomNavigationActionUtilityClass", "slot", "bottomNavigationActionClasses"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@mui/material/BottomNavigationAction/bottomNavigationActionClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getBottomNavigationActionUtilityClass(slot) {\n  return generateUtilityClass('MuiBottomNavigationAction', slot);\n}\nconst bottomNavigationActionClasses = generateUtilityClasses('MuiBottomNavigationAction', ['root', 'iconOnly', 'selected', 'label']);\nexport default bottomNavigationActionClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,qCAAqCA,CAACC,IAAI,EAAE;EAC1D,OAAOF,oBAAoB,CAAC,2BAA2B,EAAEE,IAAI,CAAC;AAChE;AACA,MAAMC,6BAA6B,GAAGJ,sBAAsB,CAAC,2BAA2B,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AACpI,eAAeI,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}