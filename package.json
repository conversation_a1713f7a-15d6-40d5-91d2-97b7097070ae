{"name": "zencod-erp-client", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^5.15.1", "@mui/material": "^5.15.1", "@mui/x-data-grid": "^8.7.0", "@mui/x-date-pickers": "^6.20.2", "@types/node": "^18.19.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "axios": "^1.10.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "html2canvas": "^1.4.1", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "qrcode.js": "^0.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-to-print": "^3.1.1", "recharts": "^2.8.0", "typescript": "^4.9.5", "uuid": "^11.1.0", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}