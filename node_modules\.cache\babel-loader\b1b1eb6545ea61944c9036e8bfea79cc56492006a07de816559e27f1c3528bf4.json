{"ast": null, "code": "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "map": {"version": 3, "names": ["getNodeName", "element", "nodeName", "toLowerCase"], "sources": ["C:/Users/<USER>/OneDrive/سطح المكتب/zencod/node_modules/@popperjs/core/lib/dom-utils/getNodeName.js"], "sourcesContent": ["export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAACC,OAAO,EAAE;EAC3C,OAAOA,OAAO,GAAG,CAACA,OAAO,CAACC,QAAQ,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC,GAAG,IAAI;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}