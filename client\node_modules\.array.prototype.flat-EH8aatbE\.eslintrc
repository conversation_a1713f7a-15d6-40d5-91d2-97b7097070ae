{
	"root": true,

	"extends": "@ljharb",

	"rules": {
		"new-cap": [2, {
			"capIsNewExceptions": [
				"ArraySpeciesCreate",
				"Call",
				"CreateDataPropertyOrThrow",
				"FlattenIntoArray",
				"Get",
				"HasProperty",
				"IsArray",
				"Set",
				"ToIntegerOrInfinity",
				"ToLength",
				"ToObject",
				"ToString",
			],
		}],
		"no-magic-numbers": 0,
	},

	"overrides": [
		{
			"files": "test/**",
			"rules": {
				"func-name-matching": 0,
				"max-params": 0,
				"no-invalid-this": [1],
			},
		},
	],
}
